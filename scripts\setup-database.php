<?php
/**
 * Database Setup Script for Tan-Aw Job Portal
 * 
 * This script helps you set up the database on a new computer.
 * Run this script in your browser to:
 * 1. Test database connection
 * 2. Create database if it doesn't exist
 * 3. Import the database structure and data
 * 4. Set up plain text passwords
 */

// Include configuration
require_once __DIR__ . '/../config/config.php';

$step = $_GET['step'] ?? 'test';
$message = '';
$error = '';
$success = '';

// Function to test database connection
function testConnection($host, $username, $password, $database = null) {
    try {
        $dsn = "mysql:host=$host;charset=utf8mb4";
        if ($database) {
            $dsn .= ";dbname=$database";
        }
        
        $pdo = new PDO($dsn, $username, $password, [
            PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
            PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
        ]);
        
        return ['success' => true, 'connection' => $pdo];
    } catch (PDOException $e) {
        return ['success' => false, 'error' => $e->getMessage()];
    }
}

// Function to create database
function createDatabase($host, $username, $password, $database) {
    try {
        $pdo = new PDO("mysql:host=$host;charset=utf8mb4", $username, $password, [
            PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
        ]);
        
        $pdo->exec("CREATE DATABASE IF NOT EXISTS `$database` CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci");
        return ['success' => true];
    } catch (PDOException $e) {
        return ['success' => false, 'error' => $e->getMessage()];
    }
}

// Function to import SQL file
function importSQL($host, $username, $password, $database, $sqlFile) {
    try {
        $pdo = new PDO("mysql:host=$host;dbname=$database;charset=utf8mb4", $username, $password, [
            PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
        ]);
        
        $sql = file_get_contents($sqlFile);
        $pdo->exec($sql);
        return ['success' => true];
    } catch (PDOException $e) {
        return ['success' => false, 'error' => $e->getMessage()];
    }
}

// Handle form submission
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $host = $_POST['host'] ?? DB_HOST;
    $username = $_POST['username'] ?? DB_USER;
    $password = $_POST['password'] ?? DB_PASS;
    $database = $_POST['database'] ?? DB_NAME;
    
    switch ($step) {
        case 'test':
            $result = testConnection($host, $username, $password);
            if ($result['success']) {
                $success = "✅ Database connection successful!";
                $step = 'create';
            } else {
                $error = "❌ Connection failed: " . $result['error'];
            }
            break;
            
        case 'create':
            $result = createDatabase($host, $username, $password, $database);
            if ($result['success']) {
                $success = "✅ Database '$database' created successfully!";
                $step = 'import';
            } else {
                $error = "❌ Failed to create database: " . $result['error'];
            }
            break;
            
        case 'import':
            $sqlFile = __DIR__ . '/../import mo ni dar ang name sa database kay tan-aw-job-portal/tan-aw-job-portal.sql';
            if (file_exists($sqlFile)) {
                $result = importSQL($host, $username, $password, $database, $sqlFile);
                if ($result['success']) {
                    $success = "✅ Database structure and data imported successfully!";
                    $step = 'convert';
                } else {
                    $error = "❌ Failed to import SQL: " . $result['error'];
                }
            } else {
                $error = "❌ SQL file not found: $sqlFile";
            }
            break;
            
        case 'convert':
            $result = testConnection($host, $username, $password, $database);
            if ($result['success']) {
                $pdo = $result['connection'];
                
                // Convert passwords to plain text
                $sqlFile = __DIR__ . '/convert-to-plaintext-passwords.sql';
                if (file_exists($sqlFile)) {
                    $sql = file_get_contents($sqlFile);
                    $pdo->exec($sql);
                    $success = "✅ Passwords converted to plain text! Setup complete!";
                    $step = 'complete';
                } else {
                    $error = "❌ Password conversion script not found!";
                }
            } else {
                $error = "❌ Cannot connect to database for password conversion!";
            }
            break;
    }
}

?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Database Setup - Tan-Aw Job Portal</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
</head>
<body class="bg-light">
    <div class="container py-5">
        <div class="row justify-content-center">
            <div class="col-lg-8">
                <div class="card shadow">
                    <div class="card-header text-center bg-primary text-white">
                        <h3 class="mb-0">
                            <i class="fas fa-database me-2"></i>Database Setup
                        </h3>
                        <p class="mb-0">Tan-Aw Job Portal</p>
                    </div>
                    <div class="card-body">
                        
                        <!-- Progress Steps -->
                        <div class="mb-4">
                            <div class="progress" style="height: 4px;">
                                <?php
                                $steps = ['test', 'create', 'import', 'convert', 'complete'];
                                $currentIndex = array_search($step, $steps);
                                $progress = (($currentIndex + 1) / count($steps)) * 100;
                                ?>
                                <div class="progress-bar bg-primary" style="width: <?php echo $progress; ?>%"></div>
                            </div>
                            <div class="d-flex justify-content-between mt-2">
                                <small class="text-muted">Test Connection</small>
                                <small class="text-muted">Create Database</small>
                                <small class="text-muted">Import Data</small>
                                <small class="text-muted">Convert Passwords</small>
                                <small class="text-muted">Complete</small>
                            </div>
                        </div>

                        <?php if ($error): ?>
                        <div class="alert alert-danger">
                            <i class="fas fa-exclamation-circle me-2"></i><?php echo htmlspecialchars($error); ?>
                        </div>
                        <?php endif; ?>

                        <?php if ($success): ?>
                        <div class="alert alert-success">
                            <i class="fas fa-check-circle me-2"></i><?php echo htmlspecialchars($success); ?>
                        </div>
                        <?php endif; ?>

                        <?php if ($step === 'complete'): ?>
                            <div class="text-center">
                                <i class="fas fa-check-circle text-success" style="font-size: 4rem;"></i>
                                <h4 class="mt-3">Setup Complete!</h4>
                                <p class="text-muted">Your database has been successfully configured.</p>
                                <div class="mt-4">
                                    <a href="../auth/login.php" class="btn btn-primary me-2">
                                        <i class="fas fa-sign-in-alt me-1"></i>Go to Login
                                    </a>
                                    <a href="../index.php" class="btn btn-outline-primary">
                                        <i class="fas fa-home me-1"></i>Go to Home
                                    </a>
                                </div>
                            </div>
                        <?php else: ?>
                            <form method="POST">
                                <div class="mb-3">
                                    <label for="host" class="form-label">Database Host</label>
                                    <input type="text" class="form-control" id="host" name="host" 
                                           value="<?php echo htmlspecialchars($_POST['host'] ?? DB_HOST); ?>" required>
                                    <small class="form-text text-muted">Usually 'localhost' or '127.0.0.1'</small>
                                </div>
                                
                                <div class="mb-3">
                                    <label for="username" class="form-label">Database Username</label>
                                    <input type="text" class="form-control" id="username" name="username" 
                                           value="<?php echo htmlspecialchars($_POST['username'] ?? DB_USER); ?>" required>
                                    <small class="form-text text-muted">Usually 'root' for XAMPP/WAMP</small>
                                </div>
                                
                                <div class="mb-3">
                                    <label for="password" class="form-label">Database Password</label>
                                    <input type="password" class="form-control" id="password" name="password" 
                                           value="<?php echo htmlspecialchars($_POST['password'] ?? DB_PASS); ?>">
                                    <small class="form-text text-muted">Leave empty if no password is set</small>
                                </div>
                                
                                <div class="mb-3">
                                    <label for="database" class="form-label">Database Name</label>
                                    <input type="text" class="form-control" id="database" name="database" 
                                           value="<?php echo htmlspecialchars($_POST['database'] ?? DB_NAME); ?>" required>
                                    <small class="form-text text-muted">The database will be created if it doesn't exist</small>
                                </div>

                                <div class="d-grid">
                                    <?php
                                    $buttonText = '';
                                    $buttonIcon = '';
                                    switch ($step) {
                                        case 'test':
                                            $buttonText = 'Test Connection';
                                            $buttonIcon = 'fas fa-plug';
                                            break;
                                        case 'create':
                                            $buttonText = 'Create Database';
                                            $buttonIcon = 'fas fa-database';
                                            break;
                                        case 'import':
                                            $buttonText = 'Import Data';
                                            $buttonIcon = 'fas fa-upload';
                                            break;
                                        case 'convert':
                                            $buttonText = 'Convert Passwords';
                                            $buttonIcon = 'fas fa-key';
                                            break;
                                    }
                                    ?>
                                    <button type="submit" class="btn btn-primary btn-lg">
                                        <i class="<?php echo $buttonIcon; ?> me-2"></i><?php echo $buttonText; ?>
                                    </button>
                                </div>
                            </form>
                        <?php endif; ?>

                        <!-- Troubleshooting Tips -->
                        <?php if ($step === 'test'): ?>
                        <div class="mt-4">
                            <h6><i class="fas fa-lightbulb me-2"></i>Troubleshooting Tips:</h6>
                            <ul class="list-unstyled">
                                <li><i class="fas fa-check text-success me-2"></i>Make sure XAMPP/WAMP is running</li>
                                <li><i class="fas fa-check text-success me-2"></i>Check if MySQL service is started</li>
                                <li><i class="fas fa-check text-success me-2"></i>Verify username and password</li>
                                <li><i class="fas fa-check text-success me-2"></i>Try '127.0.0.1' instead of 'localhost'</li>
                            </ul>
                        </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html> 