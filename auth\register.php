<?php
require_once __DIR__ . '/../config/config.php';

// Redirect if already logged in
if (isLoggedIn()) {
    $user_type = getUserType();
    switch ($user_type) {
        case 'admin':
            redirect('../admin/dashboard.php');
            break;
        case 'business':
            redirect('../business/dashboard.php');
            break;
        case 'jobseeker':
            redirect('../jobseeker/dashboard.php');
            break;
        default:
            redirect('../index.php');
            break;
    }
}

$error = '';
$success = '';

// Get all degrees for jobseeker registration
try {
    $database = new Database();
    $db = $database->getConnection();
    
    $query = "SELECT * FROM degrees ORDER BY level, category, course_name";
    $stmt = $db->prepare($query);
    $stmt->execute();
    $degrees = $stmt->fetchAll(PDO::FETCH_ASSOC);
} catch (Exception $e) {
    $degrees = [];
    error_log("Error fetching degrees: " . $e->getMessage());
}

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    try {
        $database = new Database();
        $db = $database->getConnection();
        
        // Common fields with improved validation
        $first_name = trim($_POST['first_name'] ?? '');
        $last_name = trim($_POST['last_name'] ?? '');
        $email = trim($_POST['email'] ?? '');
        $phone = trim($_POST['phone'] ?? '');
        $password = $_POST['password'] ?? '';
        $confirm_password = $_POST['confirm_password'] ?? '';
        $user_type = trim($_POST['user_type'] ?? '');
        
        // Enhanced validation
        $errors = [];
        
        // Required field validation
        if (empty($first_name)) {
            $errors[] = 'First name is required.';
        } elseif (strlen($first_name) < 2 || strlen($first_name) > 50) {
            $errors[] = 'First name must be between 2 and 50 characters.';
        } elseif (!preg_match('/^[a-zA-Z\s]+$/', $first_name)) {
            $errors[] = 'First name can only contain letters and spaces.';
        }
        
        if (empty($last_name)) {
            $errors[] = 'Last name is required.';
        } elseif (strlen($last_name) < 2 || strlen($last_name) > 50) {
            $errors[] = 'Last name must be between 2 and 50 characters.';
        } elseif (!preg_match('/^[a-zA-Z\s]+$/', $last_name)) {
            $errors[] = 'Last name can only contain letters and spaces.';
        }
        
        if (empty($email)) {
            $errors[] = 'Email address is required.';
        } elseif (!filter_var($email, FILTER_VALIDATE_EMAIL)) {
            $errors[] = 'Please enter a valid email address.';
        } elseif (strlen($email) > 255) {
            $errors[] = 'Email address is too long.';
        }
        
        if (empty($phone)) {
            $errors[] = 'Phone number is required.';
        } elseif (!preg_match('/^[0-9]{11}$/', $phone)) {
            $errors[] = 'Phone number must be exactly 11 digits.';
        }
        
        if (empty($password)) {
            $errors[] = 'Password is required.';
        } elseif (strlen($password) < 8) {
            $errors[] = 'Password must be at least 8 characters long.';
        } elseif (!preg_match('/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)/', $password)) {
            $errors[] = 'Password must contain at least one uppercase letter, one lowercase letter, and one number.';
        }
        
        if (empty($confirm_password)) {
            $errors[] = 'Please confirm your password.';
        } elseif ($password !== $confirm_password) {
            $errors[] = 'Passwords do not match.';
        }
        
        if (empty($user_type)) {
            $errors[] = 'Please select an account type.';
        } elseif (!in_array($user_type, ['business', 'jobseeker'])) {
            $errors[] = 'Please select a valid account type.';
        }
        
        // Check if terms are accepted
        if (!isset($_POST['accept_terms']) || $_POST['accept_terms'] !== 'on') {
            $errors[] = 'You must agree to the Terms and Conditions and Privacy Policy to continue.';
        }
        
        // Business-specific validation
        if ($user_type === 'business') {
            $company_name = trim($_POST['company_name'] ?? '');
            if (empty($company_name)) {
                $errors[] = 'Company name is required for business accounts.';
            } elseif (strlen($company_name) < 2 || strlen($company_name) > 255) {
                $errors[] = 'Company name must be between 2 and 255 characters.';
            }
        }
        
        // If there are validation errors, display them
        if (!empty($errors)) {
            $error = implode('<br>', $errors);
        } else {
            // Check if email already exists
            $query = "SELECT id FROM users WHERE email = ?";
            $stmt = $db->prepare($query);
            $stmt->execute([$email]);
            
            if ($stmt->fetch()) {
                $error = 'An account with this email already exists.';
            } else {
                // Check if phone number already exists
                $query = "SELECT id FROM users WHERE phone = ?";
                $stmt = $db->prepare($query);
                $stmt->execute([$phone]);
                
                if ($stmt->fetch()) {
                    $error = 'An account with this phone number already exists.';
                } else {
                    $db->beginTransaction();
                    
                    try {
                        // Store password as plain text (no hashing)
                        $plain_password = $password;
                        
                        // Insert user with sanitized data
                        $query = "INSERT INTO users (first_name, last_name, email, phone, password, user_type, is_verified) VALUES (?, ?, ?, ?, ?, ?, ?)";
                        $stmt = $db->prepare($query);
                        $is_verified = ($user_type === 'jobseeker') ? 1 : 0; // Auto-verify jobseekers, businesses need approval
                        $stmt->execute([$first_name, $last_name, $email, $phone, $plain_password, $user_type, $is_verified]);
                        
                        $user_id = $db->lastInsertId();
                        
                        if ($user_type === 'business') {
                            // Business-specific fields
                            $company_name = trim($_POST['company_name'] ?? '');
                            $business_description = trim($_POST['business_description'] ?? '');
                            $business_address = trim($_POST['business_address'] ?? '');
                            $mayors_permit = trim($_POST['mayors_permit'] ?? '');
                            $dti_number = trim($_POST['dti_number'] ?? '');
                            
                            // Insert business profile
                            $query = "INSERT INTO business_profiles (user_id, company_name, business_description, business_address, mayors_permit, dti_number, is_approved) VALUES (?, ?, ?, ?, ?, ?, 0)";
                            $stmt = $db->prepare($query);
                            $stmt->execute([$user_id, $company_name, $business_description, $business_address, $mayors_permit, $dti_number]);
                            
                        } elseif ($user_type === 'jobseeker') {
                            // Jobseeker-specific fields
                            $education_level = trim($_POST['education_level'] ?? '');
                            $degree_course = trim($_POST['degree_course'] ?? '');
                            
                            // Insert jobseeker profile
                            $query = "INSERT INTO jobseeker_profiles (user_id, education_level, degree_course) VALUES (?, ?, ?)";
                            $stmt = $db->prepare($query);
                            $stmt->execute([$user_id, $education_level, $degree_course]);
                        }
                        
                        $db->commit();
                        
                        if ($user_type === 'business') {
                            $success = 'Business account created successfully! Your account is pending admin approval. You will be notified once approved.';
                        } else {
                            $success = 'Account created successfully! You can now log in.';
                        }
                        
                    } catch (Exception $e) {
                        $db->rollback();
                        $error = 'Registration failed. Please try again later.';
                        error_log("Registration error: " . $e->getMessage());
                    }
                }
            }
        }
        
    } catch (Exception $e) {
        $error = 'Database connection failed. Please try again later.';
        error_log("Registration database error: " . $e->getMessage());
    }
}

$page_title = "Register";
include __DIR__ . '/../includes/header.php';
?>

<main class="py-5">
    <div class="container">
        <div class="row justify-content-center">
            <div class="col-lg-8">
                <div class="card shadow">
                    <div class="card-header text-center">
                        <h3 class="mb-0">Create Your Account</h3>
                        <p class="text-muted mb-0">Join Tan-Aw Job Portal</p>
                    </div>
                    <div class="card-body">
                        <?php if ($error): ?>
                        <div class="alert alert-danger alert-dismissible fade show" role="alert">
                            <?php echo $error; ?>
                            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                        </div>
                        <?php endif; ?>

                        <?php if ($success): ?>
                        <div class="alert alert-success alert-dismissible fade show" role="alert">
                            <?php echo htmlspecialchars($success); ?>
                            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                            <div class="mt-3">
                                <a href="login.php" class="btn btn-primary">Go to Login</a>
                            </div>
                        </div>
                        <?php else: ?>
                        
                        <form method="POST" id="registerForm" novalidate>
                            <!-- Account Type Selection -->
                            <div class="mb-4">
                                <label class="form-label">Account Type <span class="text-danger">*</span></label>
                                <div class="row">
                                    <div class="col-md-6 mb-2">
                                        <div class="form-check">
                                            <input class="form-check-input" type="radio" name="user_type" id="jobseeker" value="jobseeker" required>
                                            <label class="form-check-label" for="jobseeker">
                                                <i class="fas fa-user me-2"></i>Job Seeker
                                                <small class="d-block text-muted">Looking for employment opportunities</small>
                                            </label>
                                        </div>
                                    </div>
                                    <div class="col-md-6 mb-2">
                                        <div class="form-check">
                                            <input class="form-check-input" type="radio" name="user_type" id="business" value="business" required>
                                            <label class="form-check-label" for="business">
                                                <i class="fas fa-building me-2"></i>Business/Employer
                                                <small class="d-block text-muted">Posting job opportunities</small>
                                            </label>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Personal Information -->
                            <div class="mb-4">
                                <h5 class="border-bottom pb-2 mb-3 text-center">Personal Information</h5>
                                <div class="row">
                                    <div class="col-md-6 mb-3">
                                        <label for="first_name" class="form-label">First Name <span class="text-danger">*</span></label>
                                        <input type="text" class="form-control" id="first_name" name="first_name" 
                                               value="<?php echo htmlspecialchars($_POST['first_name'] ?? ''); ?>" 
                                               pattern="[a-zA-Z\s]+" minlength="2" maxlength="50" required>
                                        <div class="invalid-feedback">
                                            Please enter a valid first name (letters only, 2-50 characters).
                                        </div>
                                    </div>
                                    <div class="col-md-6 mb-3">
                                        <label for="last_name" class="form-label">Last Name <span class="text-danger">*</span></label>
                                        <input type="text" class="form-control" id="last_name" name="last_name" 
                                               value="<?php echo htmlspecialchars($_POST['last_name'] ?? ''); ?>" 
                                               pattern="[a-zA-Z\s]+" minlength="2" maxlength="50" required>
                                        <div class="invalid-feedback">
                                            Please enter a valid last name (letters only, 2-50 characters).
                                        </div>
                                    </div>
                                </div>
                                <div class="row">
                                    <div class="col-md-6 mb-3">
                                        <label for="email" class="form-label">Email Address <span class="text-danger">*</span></label>
                                        <input type="email" class="form-control" id="email" name="email" 
                                               value="<?php echo htmlspecialchars($_POST['email'] ?? ''); ?>" 
                                               maxlength="255" required>
                                        <div class="invalid-feedback">
                                            Please enter a valid email address.
                                        </div>
                                    </div>
                                    <div class="col-md-6 mb-3">
                                        <label for="phone" class="form-label">Phone Number <span class="text-danger">*</span></label>
                                        <div class="input-group">
                                            <span class="input-group-text">+63</span>
                                            <input type="tel" class="form-control" id="phone" name="phone" 
                                                   placeholder="Enter your phone number" maxlength="11" pattern="[0-9]{11}" 
                                                   value="<?php echo htmlspecialchars($_POST['phone'] ?? ''); ?>" required>
                                        </div>
                                        <div class="invalid-feedback">
                                            Please enter a valid 11-digit phone number.
                                        </div>
                                        <small class="form-text text-muted" id="phoneCounter">
                                            <span id="phoneDigits">0</span>/11 digits entered
                                        </small>
                                    </div>
                                </div>
                                <div class="row">
                                    <div class="col-md-6 mb-3">
                                        <label for="password" class="form-label">Password <span class="text-danger">*</span></label>
                                        <input type="password" class="form-control" id="password" name="password" 
                                               minlength="8" required>
                                        <small class="form-text text-muted">
                                            Must be at least 8 characters with uppercase, lowercase, and number.
                                        </small>
                                        <div class="invalid-feedback">
                                            Password must be at least 8 characters with uppercase, lowercase, and number.
                                        </div>
                                    </div>
                                    <div class="col-md-6 mb-3">
                                        <label for="confirm_password" class="form-label">Confirm Password <span class="text-danger">*</span></label>
                                        <input type="password" class="form-control" id="confirm_password" name="confirm_password" 
                                               minlength="8" required>
                                        <div class="invalid-feedback">
                                            Passwords do not match.
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Business-specific fields -->
                            <div id="businessFields" class="mb-4" style="display: none;">
                                <h5 class="border-bottom pb-2 mb-3">Business Information</h5>
                                <div class="mb-3">
                                    <label for="company_name" class="form-label">Company Name <span class="text-danger">*</span></label>
                                    <input type="text" class="form-control" id="company_name" name="company_name" 
                                           value="<?php echo htmlspecialchars($_POST['company_name'] ?? ''); ?>"
                                           minlength="2" maxlength="255">
                                    <div class="invalid-feedback">
                                        Please enter a valid company name.
                                    </div>
                                </div>
                                <div class="mb-3">
                                    <label for="business_description" class="form-label">Business Description</label>
                                    <textarea class="form-control" id="business_description" name="business_description" rows="3"><?php echo htmlspecialchars($_POST['business_description'] ?? ''); ?></textarea>
                                </div>
                                <div class="mb-3">
                                    <label for="business_address" class="form-label">Business Address</label>
                                    <textarea class="form-control" id="business_address" name="business_address" rows="2"><?php echo htmlspecialchars($_POST['business_address'] ?? ''); ?></textarea>
                                </div>
                                <div class="row">
                                    <div class="col-md-6 mb-3">
                                        <label for="mayors_permit" class="form-label">Mayor's Permit Number</label>
                                        <input type="text" class="form-control" id="mayors_permit" name="mayors_permit" 
                                               value="<?php echo htmlspecialchars($_POST['mayors_permit'] ?? ''); ?>">
                                    </div>
                                    <div class="col-md-6 mb-3">
                                        <label for="dti_number" class="form-label">DTI Number</label>
                                        <input type="text" class="form-control" id="dti_number" name="dti_number" 
                                               value="<?php echo htmlspecialchars($_POST['dti_number'] ?? ''); ?>">
                                    </div>
                                </div>
                                <div class="alert alert-info">
                                    <small><i class="fas fa-info-circle me-1"></i>Business accounts require admin approval before you can post jobs.</small>
                                </div>
                            </div>

                            <!-- Jobseeker-specific fields -->
                            <div id="jobseekerFields" class="mb-4" style="display: none;">
                                <h5 class="border-bottom pb-2 mb-3">Educational Background</h5>
                                <div class="row">
                                    <div class="col-md-6 mb-3">
                                        <label for="education_level" class="form-label">Education Level</label>
                                        <select class="form-select" id="education_level" name="education_level" onchange="filterDegrees()">
                                            <option value="">Select education level</option>
                                            <option value="High School" <?php echo ($_POST['education_level'] ?? '') == 'High School' ? 'selected' : ''; ?>>High School</option>
                                            <option value="Vocational" <?php echo ($_POST['education_level'] ?? '') == 'Vocational' ? 'selected' : ''; ?>>Vocational/Technical</option>
                                            <option value="Bachelor" <?php echo ($_POST['education_level'] ?? '') == 'Bachelor' ? 'selected' : ''; ?>>Bachelor's Degree</option>
                                            <option value="Master" <?php echo ($_POST['education_level'] ?? '') == 'Master' ? 'selected' : ''; ?>>Master's Degree</option>
                                            <option value="Doctorate" <?php echo ($_POST['education_level'] ?? '') == 'Doctorate' ? 'selected' : ''; ?>>Doctorate</option>
                                        </select>
                                    </div>
                                    <div class="col-md-6 mb-3">
                                        <label for="degree_course" class="form-label">Degree/Course</label>
                                        <div class="position-relative">
                                            <input type="text" class="form-control" id="degree_course" name="degree_course" 
                                                   placeholder="Search for your degree/course..." 
                                                   value="<?php echo htmlspecialchars($_POST['degree_course'] ?? ''); ?>"
                                                   autocomplete="off" onkeyup="searchDegrees()" onclick="showDegreeDropdown()">
                                            <div id="degreeDropdown" class="dropdown-menu w-100" style="max-height: 300px; overflow-y: auto; display: none;">
                                                <!-- Degree options will be populated here -->
                                            </div>
                                        </div>
                                        <small class="form-text text-muted">Start typing to search for your degree or course</small>
                                    </div>
                                </div>
                            </div>

                            <div class="d-grid gap-2">
                                <button type="submit" class="btn btn-primary btn-lg">
                                    <i class="fas fa-user-plus me-1"></i>Create Account
                                </button>
                            </div>

                            <!-- Terms and Conditions -->
                            <div class="mb-3 mt-4">
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="acceptTerms" name="accept_terms" required>
                                    <label class="form-check-label" for="acceptTerms">
                                        I agree to the <a href="#" data-bs-toggle="modal" data-bs-target="#termsModal">Terms and Conditions</a> and <a href="#" data-bs-toggle="modal" data-bs-target="#privacyModal">Privacy Policy</a> <span class="text-danger">*</span>
                                    </label>
                                    <div class="invalid-feedback">
                                        You must agree to the Terms and Conditions and Privacy Policy to continue.
                                    </div>
                                </div>
                            </div>
                        </form>
                        
                        <?php endif; ?>
                        
                        <div class="text-center mt-3">
                            <p class="mb-0">Already have an account? <a href="login.php">Login here</a></p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</main>

<!-- Terms and Conditions Modal -->
<div class="modal fade" id="termsModal" tabindex="-1" aria-labelledby="termsModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="termsModalLabel">Terms and Conditions</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <h6>1. Acceptance of Terms</h6>
                <p>By accessing and using Tan-Aw Job Portal, you accept and agree to be bound by the terms and provision of this agreement.</p>
                
                <h6>2. User Responsibilities</h6>
                <p>Users are responsible for:</p>
                <ul>
                    <li>Providing accurate and truthful information</li>
                    <li>Maintaining the confidentiality of their account credentials</li>
                    <li>Not sharing account access with others</li>
                    <li>Complying with all applicable laws and regulations</li>
                </ul>
                
                <h6>3. Job Posting Guidelines (Business Users)</h6>
                <p>Business users must:</p>
                <ul>
                    <li>Post only legitimate job opportunities</li>
                    <li>Provide accurate job descriptions and requirements</li>
                    <li>Respond to applications in a timely manner</li>
                    <li>Comply with employment laws and regulations</li>
                </ul>
                
                <h6>4. Prohibited Activities</h6>
                <p>Users are prohibited from:</p>
                <ul>
                    <li>Posting false or misleading information</li>
                    <li>Harassing or discriminating against other users</li>
                    <li>Using the platform for illegal purposes</li>
                    <li>Attempting to gain unauthorized access to the system</li>
                </ul>
                
                <h6>5. Termination</h6>
                <p>We reserve the right to terminate or suspend accounts that violate these terms.</p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
            </div>
        </div>
    </div>
</div>

<!-- Privacy Policy Modal -->
<div class="modal fade" id="privacyModal" tabindex="-1" aria-labelledby="privacyModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="privacyModalLabel">Privacy Policy</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <h6>1. Information We Collect</h6>
                <p>We collect information you provide directly to us, including:</p>
                <ul>
                    <li>Personal information (name, email, phone number)</li>
                    <li>Professional information (education, work experience)</li>
                    <li>Business information (company details, job postings)</li>
                </ul>
                
                <h6>2. How We Use Your Information</h6>
                <p>We use the information we collect to:</p>
                <ul>
                    <li>Provide and maintain our services</li>
                    <li>Process job applications and postings</li>
                    <li>Communicate with you about your account</li>
                    <li>Improve our platform and services</li>
                </ul>
                
                <h6>3. Information Sharing</h6>
                <p>We do not sell, trade, or otherwise transfer your personal information to third parties without your consent, except as described in this policy.</p>
                
                <h6>4. Data Security</h6>
                <p>We implement appropriate security measures to protect your personal information against unauthorized access, alteration, disclosure, or destruction.</p>
                
                <h6>5. Your Rights</h6>
                <p>You have the right to:</p>
                <ul>
                    <li>Access your personal information</li>
                    <li>Update or correct your information</li>
                    <li>Request deletion of your account</li>
                    <li>Opt-out of certain communications</li>
                </ul>
                
                <h6>6. Contact Us</h6>
                <p>If you have questions about this Privacy Policy, please contact us through our support channels.</p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
            </div>
        </div>
    </div>
</div>

<script>
// Store degrees data
const degrees = <?php echo json_encode($degrees); ?>;

// Enhanced form validation
document.addEventListener('DOMContentLoaded', function() {
    const form = document.getElementById('registerForm');
    const userTypeRadios = document.querySelectorAll('input[name="user_type"]');
    const businessFields = document.getElementById('businessFields');
    const jobseekerFields = document.getElementById('jobseekerFields');
    const companyNameField = document.getElementById('company_name');
    
    // Disable personal information fields until account type is selected
    const personalInfoFields = document.querySelectorAll('#first_name, #last_name, #email, #phone, #password, #confirm_password');
    personalInfoFields.forEach(field => {
        field.disabled = true;
        field.addEventListener('focus', function() {
            const selectedType = document.querySelector('input[name="user_type"]:checked');
            if (!selectedType) {
                alert('Please choose an account type first before entering personal information.');
                this.blur();
                return false;
            }
        });
    });
    
    // Show/hide fields based on user type
    userTypeRadios.forEach(function(radio) {
        radio.addEventListener('change', function() {
            // Enable personal information fields when account type is selected
            personalInfoFields.forEach(field => {
                field.disabled = false;
            });
            
            if (this.value === 'business') {
                businessFields.style.display = 'block';
                jobseekerFields.style.display = 'none';
                companyNameField.required = true;
            } else if (this.value === 'jobseeker') {
                businessFields.style.display = 'none';
                jobseekerFields.style.display = 'block';
                companyNameField.required = false;
            }
        });
    });
    
    // Check if a user type was previously selected
    const selectedUserType = document.querySelector('input[name="user_type"]:checked');
    if (selectedUserType) {
        selectedUserType.dispatchEvent(new Event('change'));
    }
    
    // Form validation
    form.addEventListener('submit', function(e) {
        if (!form.checkValidity()) {
            e.preventDefault();
            e.stopPropagation();
        }
        form.classList.add('was-validated');
    });
    
    // Real-time validation
    const inputs = form.querySelectorAll('input[required], select[required]');
    inputs.forEach(input => {
        input.addEventListener('blur', function() {
            validateField(this);
        });
        
        input.addEventListener('input', function() {
            if (this.classList.contains('is-invalid')) {
                validateField(this);
            }
        });
    });
});

// Field validation function
function validateField(field) {
    const value = field.value.trim();
    
    // Remove existing validation classes
    field.classList.remove('is-valid', 'is-invalid');
    
    // Check if field is required and empty
    if (field.hasAttribute('required') && !value) {
        field.classList.add('is-invalid');
        return false;
    }
    
    // Specific validation rules
    switch (field.type) {
        case 'email':
            if (value && !isValidEmail(value)) {
                field.classList.add('is-invalid');
                return false;
            }
            break;
        case 'tel':
            if (value && !/^[0-9]{11}$/.test(value)) {
                field.classList.add('is-invalid');
                return false;
            }
            break;
        case 'password':
            if (value && !isValidPassword(value)) {
                field.classList.add('is-invalid');
                return false;
            }
            break;
    }
    
    // Pattern validation
    if (field.hasAttribute('pattern') && value) {
        const pattern = new RegExp(field.getAttribute('pattern'));
        if (!pattern.test(value)) {
            field.classList.add('is-invalid');
            return false;
        }
    }
    
    // Length validation
    if (field.hasAttribute('minlength') && value.length < parseInt(field.getAttribute('minlength'))) {
        field.classList.add('is-invalid');
        return false;
    }
    
    if (field.hasAttribute('maxlength') && value.length > parseInt(field.getAttribute('maxlength'))) {
        field.classList.add('is-invalid');
        return false;
    }
    
    // If all validations pass
    if (value) {
        field.classList.add('is-valid');
    }
    
    return true;
}

// Email validation
function isValidEmail(email) {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
}

// Password validation
function isValidPassword(password) {
    return password.length >= 8 && 
           /[a-z]/.test(password) && 
           /[A-Z]/.test(password) && 
           /[0-9]/.test(password);
}

// Phone number validation
document.getElementById('phone').addEventListener('input', function(e) {
    let value = e.target.value.replace(/\D/g, '');
    if (value.length > 11) {
        value = value.slice(0, 11);
    }
    e.target.value = value;
    
    // Update character counter
    const phoneDigits = document.getElementById('phoneDigits');
    const phoneCounter = document.getElementById('phoneCounter');
    const remainingDigits = 11 - value.length;
    
    phoneDigits.textContent = value.length;
    
    // Update counter message
    if (value.length === 11) {
        phoneCounter.className = 'form-text text-success';
        phoneCounter.innerHTML = '<span id="phoneDigits">11</span>/11 digits entered ✓';
    } else if (value.length >= 8) {
        phoneCounter.className = 'form-text text-warning';
        phoneCounter.innerHTML = `<span id="phoneDigits">${value.length}</span>/11 digits entered - Need ${remainingDigits} more digit${remainingDigits > 1 ? 's' : ''}`;
    } else {
        phoneCounter.className = 'form-text text-muted';
        phoneCounter.innerHTML = `<span id="phoneDigits">${value.length}</span>/11 digits entered - Need ${remainingDigits} more digit${remainingDigits > 1 ? 's' : ''}`;
    }
    
    // Update validation feedback
    const invalidFeedback = e.target.parentElement.nextElementSibling;
    if (value.length > 0 && value.length < 11) {
        invalidFeedback.textContent = `Phone number must be exactly 11 digits. You need ${remainingDigits} more digit${remainingDigits > 1 ? 's' : ''}.`;
    } else {
        invalidFeedback.textContent = 'Please enter a valid 11-digit phone number.';
    }
});

// Password confirmation validation
document.getElementById('confirm_password').addEventListener('input', function() {
    const password = document.getElementById('password').value;
    const confirmPassword = this.value;
    
    this.classList.remove('is-valid', 'is-invalid');
    
    if (confirmPassword) {
        if (password === confirmPassword) {
            this.classList.add('is-valid');
            this.setCustomValidity('');
        } else {
            this.classList.add('is-invalid');
            this.setCustomValidity('Passwords do not match');
        }
    }
});

// Degree search functionality
function searchDegrees() {
    const input = document.getElementById('degree_course');
    const dropdown = document.getElementById('degreeDropdown');
    const searchTerm = input.value.toLowerCase();
    const educationLevel = document.getElementById('education_level').value;
    
    if (searchTerm.length < 2) {
        dropdown.style.display = 'none';
        return;
    }
    
    let filteredDegrees = degrees.filter(degree => {
        const matchesSearch = degree.course_name.toLowerCase().includes(searchTerm);
        const matchesLevel = !educationLevel || degree.level === educationLevel;
        return matchesSearch && matchesLevel;
    });
    
    displayDegreeOptions(filteredDegrees);
}

function filterDegrees() {
    const educationLevel = document.getElementById('education_level').value;
    const input = document.getElementById('degree_course');
    
    if (!educationLevel) {
        input.value = '';
        document.getElementById('degreeDropdown').style.display = 'none';
        return;
    }
    
    const filteredDegrees = degrees.filter(degree => degree.level === educationLevel);
    displayDegreeOptions(filteredDegrees);
}

function showDegreeDropdown() {
    const educationLevel = document.getElementById('education_level').value;
    if (educationLevel) {
        const filteredDegrees = degrees.filter(degree => degree.level === educationLevel);
        displayDegreeOptions(filteredDegrees);
    }
}

function displayDegreeOptions(degreesToShow) {
    const dropdown = document.getElementById('degreeDropdown');
    
    if (degreesToShow.length === 0) {
        dropdown.style.display = 'none';
        return;
    }
    
    let html = '';
    let currentCategory = '';
    
    degreesToShow.forEach(degree => {
        if (degree.category !== currentCategory) {
            if (currentCategory !== '') {
                html += '<div class="dropdown-divider"></div>';
            }
            html += `<h6 class="dropdown-header">${degree.category}</h6>`;
            currentCategory = degree.category;
        }
        
        html += `<a class="dropdown-item" href="#" onclick="selectDegree('${degree.course_name}')">${degree.course_name}</a>`;
    });
    
    dropdown.innerHTML = html;
    dropdown.style.display = 'block';
}

function selectDegree(courseName) {
    document.getElementById('degree_course').value = courseName;
    document.getElementById('degreeDropdown').style.display = 'none';
}

// Hide dropdown when clicking outside
document.addEventListener('click', function(e) {
    const dropdown = document.getElementById('degreeDropdown');
    const input = document.getElementById('degree_course');
    
    if (!dropdown.contains(e.target) && e.target !== input) {
        dropdown.style.display = 'none';
    }
});
</script>

<?php include __DIR__ . '/../includes/footer.php'; ?>
