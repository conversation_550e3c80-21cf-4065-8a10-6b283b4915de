<?php
require_once __DIR__ . '/../config/config.php';

// Check if user is already logged in
if (isLoggedIn()) {
    $userType = getUserType();
    switch ($userType) {
        case 'admin':
            redirect('../admin/dashboard.php');
            break;
        case 'business':
            redirect('../business/dashboard.php');
            break;
        case 'jobseeker':
            redirect('../jobseeker/dashboard.php');
            break;
        default:
            redirect('../index.php');
    }
}

$error = '';
$success = '';

// Handle success message from registration
if (isset($_GET['success'])) {
    $success = sanitize($_GET['success']);
}

// Handle login form submission
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $email = sanitize($_POST['email'] ?? '');
    $password = $_POST['password'] ?? '';

    if (empty($email) || empty($password)) {
        $error = 'Please fill in all fields.';
    } elseif (!filter_var($email, FILTER_VALIDATE_EMAIL)) {
        $error = 'Please enter a valid email address.';
    } else {
        try {
            $database = new Database();
            $db = $database->getConnection();

            // Get user by email
            $query = "SELECT * FROM users WHERE email = ?";
            $stmt = $db->prepare($query);
            $stmt->execute([$email]);
            $user = $stmt->fetch(PDO::FETCH_ASSOC);

            if ($user && $password === $user['password']) {
                // Check if user is verified (for business accounts)
                if ($user['user_type'] === 'business' && !$user['is_verified']) {
                    $error = 'Your business account is pending admin approval. Please wait for verification.';
                } else {
                    // Login successful
                    $_SESSION['user_id'] = $user['id'];
                    $_SESSION['email'] = $user['email'];
                    $_SESSION['first_name'] = $user['first_name'];
                    $_SESSION['last_name'] = $user['last_name'];
                    $_SESSION['user_type'] = $user['user_type'];

                    // Redirect based on user type
                    switch ($user['user_type']) {
                        case 'admin':
                            redirect('/admin/dashboard.php');
                            break;
                        case 'business':
                            redirect('/business/dashboard.php');
                            break;
                        case 'jobseeker':
                            redirect('/jobseeker/dashboard.php');
                            break;
                        default:
                            redirect('/index.php');
                    }
                }
            } else {
                $error = 'Invalid email or password.';
            }
        } catch (Exception $e) {
            error_log("Login error: " . $e->getMessage());
            $error = 'An error occurred during login. Please try again.';
        }
    }
}

$page_title = "Login";
include __DIR__ . '/../includes/header.php';
?>

<main class="py-5">
    <div class="container">
        <div class="row justify-content-center">
            <div class="col-md-6 col-lg-5">
                <div class="card shadow-lg beautiful-card">
                    <div class="card-header text-center beautiful-header">
                        <h3 class="mb-0">
                            <i class="fas fa-sign-in-alt me-2"></i>Login
                        </h3>
                    </div>
                    <div class="card-body p-4 beautiful-body">
                        <?php if ($error): ?>
                        <div class="alert alert-danger alert-dismissible fade show" role="alert">
                            <i class="fas fa-exclamation-circle me-2"></i>
                            <?php echo htmlspecialchars($error); ?>
                            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                        </div>
                        <?php endif; ?>

                        <?php if ($success): ?>
                        <div class="alert alert-success alert-dismissible fade show" role="alert">
                            <i class="fas fa-check-circle me-2"></i>
                            <?php echo htmlspecialchars($success); ?>
                            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                        </div>
                        <?php endif; ?>

                        <form method="POST" id="loginForm">
                            <div class="mb-3">
                                <label for="email" class="form-label">Email Address</label>
                                <div class="input-group">
                                    <span class="input-group-text">
                                        <i class="fas fa-envelope"></i>
                                    </span>
                                    <input type="email" class="form-control animate-input" id="email" name="email" 
                                           value="<?php echo htmlspecialchars($_POST['email'] ?? ''); ?>" 
                                           placeholder="Enter your email" required>
                                </div>
                            </div>

                            <div class="mb-3">
                                <label for="password" class="form-label">Password</label>
                                <div class="input-group">
                                    <span class="input-group-text">
                                        <i class="fas fa-lock"></i>
                                    </span>
                                    <input type="password" class="form-control animate-input" id="password" name="password" 
                                           placeholder="Enter your password" required>
                                    <button class="btn btn-outline-secondary" type="button" id="togglePassword">
                                        <i class="fas fa-eye"></i>
                                    </button>
                                </div>
                            </div>

                            <div class="d-grid">
                                <button type="submit" class="btn btn-primary btn-lg">
                                    <i class="fas fa-sign-in-alt me-2"></i>Login
                                </button>
                            </div>
                        </form>

                        <hr class="my-4">

                        <div class="text-center">
                            <p class="mb-3 text-warning fs-6">Don't have an account?</p>
                            <a href="register.php" class="btn btn-outline-primary btn-lg px-4">
                                <i class="fas fa-user-plus me-2"></i>Create Account
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</main>

<script>
// Add animation styles
const style = document.createElement('style');
style.textContent = `
    .beautiful-card {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        border: none;
        border-radius: 20px;
        overflow: hidden;
        transform: translateY(20px);
        opacity: 0;
        animation: slideInUp 0.8s ease forwards;
        box-shadow: 0 20px 40px rgba(0,0,0,0.1);
    }
    
    .beautiful-header {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        border: none;
        color: white;
        padding: 2rem 1.5rem;
        position: relative;
        overflow: hidden;
    }
    
    .beautiful-header::before {
        content: '';
        position: absolute;
        top: 0;
        left: -100%;
        width: 100%;
        height: 100%;
        background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
        animation: shimmer 3s infinite;
    }
    
    .beautiful-body {
        background: rgba(255, 255, 255, 0.95);
        backdrop-filter: blur(10px);
        border-radius: 0 0 20px 20px;
    }
    
    @keyframes slideInUp {
        to {
            transform: translateY(0);
            opacity: 1;
        }
    }
    
    @keyframes shimmer {
        0% { left: -100%; }
        100% { left: 100%; }
    }
    
    .animate-input {
        transition: all 0.4s ease;
        border: 2px solid #e9ecef;
        position: relative;
        overflow: hidden;
        background: #fff;
        transform-style: preserve-3d;
    }
    
    .animate-input::before {
        content: '';
        position: absolute;
        top: -100%;
        left: -100%;
        width: 300%;
        height: 300%;
        background: linear-gradient(
            45deg,
            transparent 20%,
            rgba(255, 255, 255, 0.1) 30%,
            rgba(255, 255, 255, 0.9) 40%,
            rgba(255, 255, 255, 1) 50%,
            rgba(255, 255, 255, 0.9) 60%,
            rgba(255, 255, 255, 0.1) 70%,
            transparent 80%
        );
        transform: translateX(-100%) translateY(-100%) rotate(45deg);
        transition: transform 0.8s ease;
        pointer-events: none;
        z-index: 1;
    }
    
    .animate-input:hover::before {
        transform: translateX(100%) translateY(100%) rotate(45deg);
    }
    
    .animate-input:hover {
        transform: scale(1.1);
        border-color: #007bff;
        box-shadow: 0 0 30px rgba(0, 123, 255, 0.6);
        background: linear-gradient(135deg, #f8f9fa, #e3f2fd);
    }
    
    .animate-input:focus {
        transform: scale(1.15);
        border-color: #007bff;
        box-shadow: 0 0 40px rgba(0, 123, 255, 0.8);
        background: linear-gradient(135deg, #007bff, #0056b3);
        color: white;
    }
    
    .animate-input:focus::placeholder {
        color: rgba(255, 255, 255, 0.8);
    }
    
    .input-group:hover .input-group-text {
        background: linear-gradient(135deg, #007bff, #0056b3);
        color: white;
        transform: scale(1.2);
        transition: all 0.4s ease;
    }
    
    .input-group:focus-within .input-group-text {
        background: linear-gradient(135deg, #007bff, #0056b3);
        color: white;
        transform: scale(1.25);
        transition: all 0.4s ease;
    }
    
    .input-group:hover {
        transform: scale(1.05);
        transition: all 0.4s ease;
    }
    
    .btn-primary {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        border: none;
        transition: all 0.3s ease;
    }
    
    .btn-primary:hover {
        transform: translateY(-2px);
        box-shadow: 0 10px 20px rgba(102, 126, 234, 0.4);
    }
    
    .btn-outline-primary {
        border: 2px solid #667eea;
        color: #667eea;
        transition: all 0.3s ease;
    }
    
    .btn-outline-primary:hover {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        border-color: transparent;
        transform: translateY(-2px);
        box-shadow: 0 10px 20px rgba(102, 126, 234, 0.4);
    }
`;
document.head.appendChild(style);

// Password toggle functionality
document.getElementById('togglePassword').addEventListener('click', function() {
    const passwordField = document.getElementById('password');
    const icon = this.querySelector('i');
    
    if (passwordField.type === 'password') {
        passwordField.type = 'text';
        icon.classList.remove('fa-eye');
        icon.classList.add('fa-eye-slash');
    } else {
        passwordField.type = 'password';
        icon.classList.remove('fa-eye-slash');
        icon.classList.add('fa-eye');
    }
});

// Form submission with loading state
document.getElementById('loginForm').addEventListener('submit', function(e) {
    const submitBtn = this.querySelector('button[type="submit"]');
    submitBtn.disabled = true;
    submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Logging in...';
});

// Add fade-in animation
document.addEventListener('DOMContentLoaded', function() {
    document.querySelector('.card').classList.add('fade-in');
});
</script>

<?php include __DIR__ . '/../includes/footer.php'; ?>
