-- Fix jobseeker_profiles table by adding missing fields
-- This script safely adds the location, expected_salary, and resume_file fields that are referenced in the admin code

USE `tan-aw-job-portal`;

-- Check and add location field to jobseeker_profiles table
SET @sql = (SELECT IF(
    (SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS 
     WHERE TABLE_SCHEMA = 'tan-aw-job-portal' 
     AND TABLE_NAME = 'jobseeker_profiles' 
     AND COLUMN_NAME = 'location') = 0,
    'ALTER TABLE jobseeker_profiles ADD COLUMN location VARCHAR(255) DEFAULT NULL AFTER experience_years',
    'SELECT "location column already exists" as message'
));
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- Check and add expected_salary field to jobseeker_profiles table
SET @sql = (SELECT IF(
    (SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS 
     WHERE TABLE_SCHEMA = 'tan-aw-job-portal' 
     AND TABLE_NAME = 'jobseeker_profiles' 
     AND COLUMN_NAME = 'expected_salary') = 0,
    'ALTER TABLE jobseeker_profiles ADD COLUMN expected_salary DECIMAL(10,2) DEFAULT NULL AFTER location',
    'SELECT "expected_salary column already exists" as message'
));
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- Check and add resume_file field to jobseeker_profiles table
SET @sql = (SELECT IF(
    (SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS 
     WHERE TABLE_SCHEMA = 'tan-aw-job-portal' 
     AND TABLE_NAME = 'jobseeker_profiles' 
     AND COLUMN_NAME = 'resume_file') = 0,
    'ALTER TABLE jobseeker_profiles ADD COLUMN resume_file VARCHAR(255) DEFAULT NULL AFTER expected_salary',
    'SELECT "resume_file column already exists" as message'
));
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- Update existing records with sample data (optional)
UPDATE jobseeker_profiles 
SET location = 'Midsayap, Cotabato', 
    expected_salary = 25000.00 
WHERE location IS NULL AND user_id IN (3, 4, 6, 7);

-- Verify the changes
DESCRIBE jobseeker_profiles; 