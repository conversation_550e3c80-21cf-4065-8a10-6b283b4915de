<?php
require_once __DIR__ . '/config/config.php';

// Check if user is logged in
if (!isLoggedIn()) {
    redirect('auth/login.php');
}

$user_type = getUserType();

// Redirect to appropriate profile page based on user type
switch ($user_type) {
    case 'admin':
        redirect('admin/profile.php');
        break;
    case 'business':
        redirect('business/profile.php');
        break;
    case 'jobseeker':
        redirect('jobseeker/profile.php');
        break;
    default:
        redirect('auth/login.php');
        break;
}
?>
