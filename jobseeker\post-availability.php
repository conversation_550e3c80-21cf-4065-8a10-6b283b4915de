<?php
require_once __DIR__ . '/../config/config.php';

// Check if user is logged in and is jobseeker
if (!isLoggedIn() || getUserType() !== 'jobseeker') {
    redirect('../auth/login.php');
}

$success = '';
$error = '';

try {
    $database = new Database();
    $db = $database->getConnection();
    $user_id = $_SESSION['user_id'];

    // Handle form submission
    if ($_SERVER['REQUEST_METHOD'] === 'POST') {
        $title = sanitize($_POST['title'] ?? '');
        $description = sanitize($_POST['description'] ?? '');
        $skills_offered = sanitize($_POST['skills_offered'] ?? '');
        $experience_years = (int)($_POST['experience_years'] ?? 0);
        $availability = sanitize($_POST['availability'] ?? '');
        $expected_salary_min = !empty($_POST['expected_salary_min']) ? (float)$_POST['expected_salary_min'] : null;
        $expected_salary_max = !empty($_POST['expected_salary_max']) ? (float)$_POST['expected_salary_max'] : null;
        $location = sanitize($_POST['location'] ?? '');

        if (empty($title) || empty($description) || empty($availability)) {
            $error = 'Please fill in all required fields.';
        } else {
            try {
                $query = "INSERT INTO jobseeker_posts (jobseeker_id, title, description, skills_offered, experience_years, availability, expected_salary_min, expected_salary_max, location) 
                          VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)";
                $stmt = $db->prepare($query);
                $stmt->execute([$user_id, $title, $description, $skills_offered, $experience_years, $availability, $expected_salary_min, $expected_salary_max, $location]);

                $success = 'Your availability post has been created successfully!';
                header("Location: dashboard.php?success=" . urlencode($success));
                exit();
            } catch (Exception $e) {
                $error = 'Failed to create post. Please try again.';
                error_log("Jobseeker post creation error: " . $e->getMessage());
            }
        }
    }

} catch (Exception $e) {
    error_log("Post availability page error: " . $e->getMessage());
    $error = "An error occurred while loading the page.";
}

$page_title = "Post Your Availability";
include __DIR__ . '/../includes/header.php';
?>

<main class="py-4">
    <div class="container">
        <div class="row">
            <div class="col-lg-8 mx-auto">
                <div class="card shadow">
                    <div class="card-header">
                        <h4 class="mb-0">
                            <i class="fas fa-bullhorn me-2"></i>Post Your Availability
                        </h4>
                        <p class="text-muted mb-0">Let employers know you're available for work</p>
                    </div>
                    <div class="card-body">
                        <?php if ($error): ?>
                        <div class="alert alert-danger alert-dismissible fade show" role="alert">
                            <?php echo htmlspecialchars($error); ?>
                            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                        </div>
                        <?php endif; ?>

                        <form method="POST">
                            <div class="mb-4">
                                <label for="title" class="form-label">Post Title <span class="text-danger">*</span></label>
                                <input type="text" class="form-control" id="title" name="title" 
                                       placeholder="e.g. Experienced Web Developer Available for Hire"
                                       value="<?php echo htmlspecialchars($_POST['title'] ?? ''); ?>" required>
                            </div>

                            <div class="mb-4">
                                <label for="description" class="form-label">Description <span class="text-danger">*</span></label>
                                <textarea class="form-control" id="description" name="description" rows="6" required
                                          placeholder="Describe your experience, what you're looking for, and what makes you a great candidate..."><?php echo htmlspecialchars($_POST['description'] ?? ''); ?></textarea>
                            </div>

                            <div class="mb-4">
                                <label for="skills_offered" class="form-label">Skills You Offer</label>
                                <input type="text" class="form-control" id="skills_offered" name="skills_offered" 
                                       placeholder="e.g. PHP, JavaScript, HTML, CSS, MySQL, React"
                                       value="<?php echo htmlspecialchars($_POST['skills_offered'] ?? ''); ?>">
                                <small class="form-text text-muted">Separate skills with commas</small>
                            </div>

                            <div class="row">
                                <div class="col-md-6 mb-4">
                                    <label for="experience_years" class="form-label">Years of Experience</label>
                                    <input type="number" class="form-control" id="experience_years" name="experience_years" 
                                           min="0" value="<?php echo $_POST['experience_years'] ?? 0; ?>">
                                </div>
                                <div class="col-md-6 mb-4">
                                    <label for="availability" class="form-label">Availability <span class="text-danger">*</span></label>
                                    <select class="form-select" id="availability" name="availability" required>
                                        <option value="">Select Availability</option>
                                        <option value="full-time" <?php echo ($_POST['availability'] ?? '') === 'full-time' ? 'selected' : ''; ?>>Full-time</option>
                                        <option value="part-time" <?php echo ($_POST['availability'] ?? '') === 'part-time' ? 'selected' : ''; ?>>Part-time</option>
                                        <option value="contract" <?php echo ($_POST['availability'] ?? '') === 'contract' ? 'selected' : ''; ?>>Contract</option>
                                        <option value="freelance" <?php echo ($_POST['availability'] ?? '') === 'freelance' ? 'selected' : ''; ?>>Freelance</option>
                                        <option value="internship" <?php echo ($_POST['availability'] ?? '') === 'internship' ? 'selected' : ''; ?>>Internship</option>
                                    </select>
                                </div>
                            </div>

                            <div class="row">
                                <div class="col-md-6 mb-4">
                                    <label for="expected_salary_min" class="form-label">Expected Salary (Min)</label>
                                    <div class="input-group">
                                        <span class="input-group-text">₱</span>
                                        <input type="number" class="form-control" id="expected_salary_min" name="expected_salary_min" 
                                               min="0" step="1000" value="<?php echo $_POST['expected_salary_min'] ?? ''; ?>">
                                    </div>
                                </div>
                                <div class="col-md-6 mb-4">
                                    <label for="expected_salary_max" class="form-label">Expected Salary (Max)</label>
                                    <div class="input-group">
                                        <span class="input-group-text">₱</span>
                                        <input type="number" class="form-control" id="expected_salary_max" name="expected_salary_max" 
                                               min="0" step="1000" value="<?php echo $_POST['expected_salary_max'] ?? ''; ?>">
                                    </div>
                                </div>
                            </div>

                            <div class="mb-4">
                                <label for="location" class="form-label">Preferred Location</label>
                                <input type="text" class="form-control" id="location" name="location" 
                                       placeholder="e.g. Midsayap, Cotabato or Remote"
                                       value="<?php echo htmlspecialchars($_POST['location'] ?? 'Midsayap, Cotabato'); ?>">
                            </div>

                            <div class="d-flex justify-content-between">
                                <a href="dashboard.php" class="btn btn-secondary">
                                    <i class="fas fa-arrow-left me-1"></i>Back to Dashboard
                                </a>
                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-bullhorn me-1"></i>Post Availability
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>
</main>

<?php include __DIR__ . '/includes/footer.php'; ?>
