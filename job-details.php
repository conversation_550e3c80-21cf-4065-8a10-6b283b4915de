<?php
require_once __DIR__ . '/config/config.php';

$job_id = (int)($_GET['id'] ?? 0);
$error = '';
$success = '';
$job = null;
$skill_match = 0;

if ($job_id <= 0) {
    $error = 'Invalid job ID.';
} else {
    try {
        $database = new Database();
        $db = $database->getConnection();

        // Handle comment submission
        if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['action'])) {
            if ($_POST['action'] === 'add_comment') {
                if (isLoggedIn()) {
                    $comment = trim($_POST['comment'] ?? '');
                    $parent_id = !empty($_POST['parent_id']) ? (int)$_POST['parent_id'] : null;
                    
                    if (!empty($comment)) {
                        try {
                            $query = "INSERT INTO job_comments (job_id, user_id, comment, parent_id) VALUES (?, ?, ?, ?)";
                            $stmt = $db->prepare($query);
                            $stmt->execute([$job_id, $_SESSION['user_id'], $comment, $parent_id]);
                            
                            $success = "Comment added successfully!";
                            header("Location: job-details.php?id=$job_id&success=" . urlencode($success));
                            exit();
                        } catch (Exception $e) {
                            error_log("Comment submission error: " . $e->getMessage());
                            $error = "Failed to add comment. Please try again.";
                        }
                    } else {
                        $error = "Comment cannot be empty.";
                    }
                } else {
                    $error = "You must be logged in to comment.";
                }
            } elseif ($_POST['action'] === 'delete_comment') {
                if (isLoggedIn()) {
                    $comment_id = (int)($_POST['comment_id'] ?? 0);
                    
                    if ($comment_id > 0) {
                        try {
                            // Get comment details to check permissions
                            $query = "SELECT jc.*, jp.business_id, u.user_type 
                                      FROM job_comments jc 
                                      JOIN job_posts jp ON jc.job_id = jp.id 
                                      JOIN users u ON jc.user_id = u.id 
                                      WHERE jc.id = ?";
                            $stmt = $db->prepare($query);
                            $stmt->execute([$comment_id]);
                            $comment_details = $stmt->fetch(PDO::FETCH_ASSOC);
                            
                            if ($comment_details) {
                                $can_delete = false;
                                $current_user_id = $_SESSION['user_id'];
                                $current_user_type = getUserType();
                                
                                // Check permissions
                                if ($current_user_type === 'admin') {
                                    // Admin can delete any comment
                                    $can_delete = true;
                                } elseif ($current_user_type === 'business') {
                                    // Business can delete comments on their own job posts
                                    if ($comment_details['business_id'] == $current_user_id) {
                                        $can_delete = true;
                                    }
                                }
                                // Jobseekers cannot delete comments
                                
                                if ($can_delete) {
                                    // Delete the comment and all its replies
                                    $query = "DELETE FROM job_comments WHERE id = ? OR parent_id = ?";
                                    $stmt = $db->prepare($query);
                                    $stmt->execute([$comment_id, $comment_id]);
                                    
                                    $success = "Comment deleted successfully!";
                                    header("Location: job-details.php?id=$job_id&success=" . urlencode($success));
                                    exit();
                                } else {
                                    $error = "You don't have permission to delete this comment.";
                                }
                            } else {
                                $error = "Comment not found.";
                            }
                        } catch (Exception $e) {
                            error_log("Comment deletion error: " . $e->getMessage());
                            $error = "Failed to delete comment. Please try again.";
                        }
                    } else {
                        $error = "Invalid comment ID.";
                    }
                } else {
                    $error = "You must be logged in to delete comments.";
                }
            }
        }

        // Get success message
        if (isset($_GET['success'])) {
            $success = sanitize($_GET['success']);
        }

        // Get job details with company information
        $query = "SELECT jp.*, bp.company_name, bp.business_description, bp.business_address, u.first_name, u.last_name, u.email, u.phone
                  FROM job_posts jp 
                  JOIN business_profiles bp ON jp.business_id = bp.user_id 
                  JOIN users u ON bp.user_id = u.id 
                  WHERE jp.id = ? AND jp.status = 'approved'";
        $stmt = $db->prepare($query);
        $stmt->execute([$job_id]);
        $job = $stmt->fetch(PDO::FETCH_ASSOC);

        if (!$job) {
            $error = 'Job not found or not available.';
        } else {
            // Calculate skill match if user is jobseeker
            if (isLoggedIn() && getUserType() === 'jobseeker') {
                $query = "SELECT skills FROM jobseeker_profiles WHERE user_id = ?";
                $stmt = $db->prepare($query);
                $stmt->execute([$_SESSION['user_id']]);
                $profile = $stmt->fetch(PDO::FETCH_ASSOC);

                if (!empty($job['skills_required']) && !empty($profile['skills'])) {
                    $skill_match = calculateSkillMatch($job['skills_required'], $profile['skills']);
                }
            }

            // Get comments with user information and business_id for permission checking
            $query = "SELECT jc.*, u.first_name, u.last_name, u.user_type, jp.business_id 
                      FROM job_comments jc 
                      JOIN users u ON jc.user_id = u.id 
                      JOIN job_posts jp ON jc.job_id = jp.id 
                      WHERE jc.job_id = ? 
                      ORDER BY jc.created_at ASC";
            $stmt = $db->prepare($query);
            $stmt->execute([$job_id]);
            $all_comments = $stmt->fetchAll(PDO::FETCH_ASSOC);

            // Organize comments into parent-child structure
            $comments = [];
            $replies = [];
            
            foreach ($all_comments as $comment) {
                if ($comment['parent_id'] === null) {
                    $comments[] = $comment;
                } else {
                    if (!isset($replies[$comment['parent_id']])) {
                        $replies[$comment['parent_id']] = [];
                    }
                    $replies[$comment['parent_id']][] = $comment;
                }
            }
        }

    } catch (Exception $e) {
        error_log("Job details error: " . $e->getMessage());
        $error = 'An error occurred while loading job details.';
    }
}

$page_title = $job ? htmlspecialchars($job['title']) : 'Job Details';
include __DIR__ . '/includes/header.php';
?>

<main class="py-4">
    <div class="container">
        <?php if ($success): ?>
        <div class="alert alert-success alert-dismissible fade show" role="alert">
            <i class="fas fa-check me-2"></i><?php echo htmlspecialchars($success); ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
        <?php endif; ?>

        <?php if ($error): ?>
        <div class="alert alert-danger alert-dismissible fade show" role="alert">
            <i class="fas fa-exclamation-triangle me-2"></i><?php echo htmlspecialchars($error); ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
        <?php endif; ?>

        <?php if ($error && !$job): ?>
        <div class="row">
            <div class="col-12">
                <div class="alert alert-danger" role="alert">
                    <h4 class="alert-heading">Error</h4>
                    <p><?php echo htmlspecialchars($error); ?></p>
                    <hr>
                    <a href="jobs.php" class="btn btn-primary">Browse Jobs</a>
                </div>
            </div>
        </div>
        <?php else: ?>
        <div class="row">
            <!-- Job Details -->
            <div class="col-lg-8">
                <div class="card shadow mb-4">
                    <?php if (!empty($job['job_image'])): ?>
                    <div class="job-header-image">
                        <img src="uploads/job_images/<?php echo htmlspecialchars($job['job_image']); ?>" 
                             class="card-img-top" alt="<?php echo htmlspecialchars($job['title']); ?>" 
                             style="height: 250px; object-fit: cover;">
                    </div>
                    <?php endif; ?>
                    
                    <div class="card-header">
                        <div class="d-flex justify-content-between align-items-start">
                            <div>
                                <h1 class="h3 mb-1"><?php echo htmlspecialchars($job['title']); ?></h1>
                                <p class="text-primary mb-0">
                                    <i class="fas fa-building me-1"></i>
                                    <?php echo htmlspecialchars($job['company_name']); ?>
                                </p>
                            </div>
                            <div class="text-end">
                                <?php if ($job['salary_min'] && $job['salary_max']): ?>
                                <div class="h5 text-success mb-1">
                                    ₱<?php echo number_format($job['salary_min']); ?> - ₱<?php echo number_format($job['salary_max']); ?>
                                </div>
                                <small class="text-muted">per month</small>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>
                    <div class="card-body">
                        <!-- Job Meta Information -->
                        <div class="row mb-4">
                            <div class="col-md-3 mb-2">
                                <small class="text-muted d-block">Job Type</small>
                                <span class="badge bg-info"><?php echo ucfirst($job['job_type']); ?></span>
                            </div>
                            <div class="col-md-3 mb-2">
                                <small class="text-muted d-block">Location</small>
                                <span><i class="fas fa-map-marker-alt me-1"></i><?php echo htmlspecialchars($job['location']); ?></span>
                            </div>
                            <div class="col-md-3 mb-2">
                                <small class="text-muted d-block">Slots Available</small>
                                <span class="badge bg-<?php echo $job['slots_available'] > 0 ? 'success' : 'danger'; ?>">
                                    <i class="fas fa-users me-1"></i><?php echo $job['slots_available']; ?> position(s)
                                </span>
                            </div>
                            <div class="col-md-3 mb-2">
                                <small class="text-muted d-block">Posted</small>
                                <span><i class="fas fa-calendar me-1"></i><?php echo date('M j, Y', strtotime($job['created_at'])); ?></span>
                            </div>
                        </div>

                        <!-- Skill Match Display -->
                        <?php if ($skill_match > 0): ?>
                        <div class="alert alert-success mb-4">
                            <div class="d-flex align-items-center">
                                <i class="fas fa-star fa-2x me-3"></i>
                                <div>
                                    <h5 class="mb-1">Great Match!</h5>
                                    <p class="mb-0">You have a <strong><?php echo $skill_match; ?>%</strong> skill match with this position!</p>
                                </div>
                            </div>
                        </div>
                        <?php endif; ?>

                        <!-- Slots Warning -->
                        <?php if ($job['slots_available'] <= 0): ?>
                        <div class="alert alert-danger mb-4">
                            <div class="d-flex align-items-center">
                                <i class="fas fa-exclamation-triangle fa-2x me-3"></i>
                                <div>
                                    <h5 class="mb-1">No Positions Available</h5>
                                    <p class="mb-0">All positions for this job have been filled.</p>
                                </div>
                            </div>
                        </div>
                        <?php elseif ($job['slots_available'] <= 2): ?>
                        <div class="alert alert-warning mb-4">
                            <div class="d-flex align-items-center">
                                <i class="fas fa-clock fa-2x me-3"></i>
                                <div>
                                    <h5 class="mb-1">Limited Positions Available</h5>
                                    <p class="mb-0">Only <strong><?php echo $job['slots_available']; ?></strong> position(s) remaining!</p>
                                </div>
                            </div>
                        </div>
                        <?php endif; ?>

                        <!-- Job Description -->
                        <div class="mb-4">
                            <h5>Job Description</h5>
                            <div class="text-muted">
                                <?php echo nl2br(htmlspecialchars($job['description'])); ?>
                            </div>
                        </div>

                        <!-- Requirements -->
                        <?php if (!empty($job['requirements'])): ?>
                        <div class="mb-4">
                            <h5>Requirements</h5>
                            <div class="text-muted">
                                <?php echo nl2br(htmlspecialchars($job['requirements'])); ?>
                            </div>
                        </div>
                        <?php endif; ?>

                        <!-- Skills Required -->
                        <?php if (!empty($job['skills_required'])): ?>
                        <div class="mb-4">
                            <h5>Skills Required</h5>
                            <div>
                                <?php 
                                $skills = explode(',', $job['skills_required']);
                                foreach ($skills as $skill): 
                                    $skill = trim($skill);
                                    if (!empty($skill)):
                                ?>
                                <span class="badge bg-secondary me-1 mb-1"><?php echo htmlspecialchars($skill); ?></span>
                                <?php 
                                    endif;
                                endforeach; 
                                ?>
                            </div>
                        </div>
                        <?php endif; ?>

                        <!-- Benefits Section -->
                        <div class="mb-4">
                            <h5>Benefits & Perks</h5>
                            <div class="row g-3">
                                <div class="col-md-6">
                                    <div class="d-flex align-items-center mb-2">
                                        <i class="fas fa-check-circle text-success me-2"></i>
                                        <span>Competitive Salary Package</span>
                                    </div>
                                    <div class="d-flex align-items-center mb-2">
                                        <i class="fas fa-check-circle text-success me-2"></i>
                                        <span>Health Insurance Coverage</span>
                                    </div>
                                    <div class="d-flex align-items-center mb-2">
                                        <i class="fas fa-check-circle text-success me-2"></i>
                                        <span>Professional Development</span>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="d-flex align-items-center mb-2">
                                        <i class="fas fa-check-circle text-success me-2"></i>
                                        <span>Flexible Work Schedule</span>
                                    </div>
                                    <div class="d-flex align-items-center mb-2">
                                        <i class="fas fa-check-circle text-success me-2"></i>
                                        <span>Performance Bonuses</span>
                                    </div>
                                    <div class="d-flex align-items-center mb-2">
                                        <i class="fas fa-check-circle text-success me-2"></i>
                                        <span>Team Building Activities</span>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Back Button -->
                        <div class="mb-4">
                            <button class="btn btn-outline-secondary" onclick="window.history.back()">
                                <i class="fas fa-arrow-left me-1"></i>Back
                            </button>
                        </div>
                    </div>
                </div>

                <!-- Comments Section -->
                <div class="card shadow comments-section">
                    <div class="card-header bg-gradient-primary text-white">
                        <h5 class="mb-0">
                            <i class="fas fa-comments me-2"></i>Comments (<?php echo count($comments); ?>)
                        </h5>
                    </div>
                    <div class="card-body">
                        <!-- Add Comment Form -->
                        <?php if (isLoggedIn()): ?>
                        <form method="POST" class="comment-form" id="commentForm">
                            <input type="hidden" name="action" value="add_comment">
                            <div class="d-flex align-items-start gap-3">
                                <div class="avatar">
                                    <i class="fas fa-user-circle"></i>
                                </div>
                                <div class="flex-grow-1">
                                    <textarea id="comment" name="comment" rows="3"
                                              placeholder="Share your thoughts about this job..." required></textarea>
                                    <div class="d-flex justify-content-between align-items-center mt-3">
                                        <small class="text-muted">
                                            <i class="fas fa-info-circle me-1"></i>
                                            Your comment will be visible to all users
                                        </small>
                                        <button type="submit" class="btn btn-primary">
                                            <i class="fas fa-paper-plane me-1"></i>Post Comment
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </form>
                        <?php else: ?>
                        <div class="alert alert-info mb-4">
                            <i class="fas fa-info-circle me-2"></i>
                            <a href="auth/login.php" class="alert-link">Login</a> to post a comment.
                        </div>
                        <?php endif; ?>

                        <!-- Display Comments -->
                        <?php if (empty($comments)): ?>
                        <div class="text-center py-5">
                            <i class="fas fa-comments fa-3x text-muted mb-3"></i>
                            <h6>No comments yet</h6>
                            <p class="text-muted">Be the first to comment on this job posting!</p>
                        </div>
                        <?php else: ?>
                        <div class="comments-list">
                            <?php foreach ($comments as $comment): ?>
                            <div class="comment-item">
                                <div class="comment-header">
                                    <div class="comment-author">
                                        <div class="avatar">
                                            <i class="fas fa-user-circle"></i>
                                        </div>
                                        <div>
                                            <h6>
                                                <?php
                                                $commenter_name = htmlspecialchars($comment['first_name'] . ' ' . $comment['last_name']);
                                                $is_jobseeker_comment = $comment['user_type'] === 'jobseeker';
                                                $can_view_profile = isLoggedIn() && (getUserType() === 'admin' || getUserType() === 'business');

                                                if ($is_jobseeker_comment && $can_view_profile): ?>
                                                    <a href="view-jobseeker-profile.php?user_id=<?php echo $comment['user_id']; ?>"
                                                       class="text-decoration-none jobseeker-profile-link"
                                                       title="View job seeker profile">
                                                        <?php echo $commenter_name; ?>
                                                        <i class="fas fa-external-link-alt ms-1 small"></i>
                                                    </a>
                                                <?php else: ?>
                                                    <?php echo $commenter_name; ?>
                                                <?php endif; ?>
                                            </h6>
                                            <span class="badge bg-<?php echo $comment['user_type'] === 'business' ? 'primary' : ($comment['user_type'] === 'admin' ? 'danger' : 'success'); ?>">
                                                <?php echo ucfirst($comment['user_type']); ?>
                                            </span>
                                        </div>
                                    </div>
                                    <div class="comment-timestamp">
                                        <?php echo date('M j, Y g:i A', strtotime($comment['created_at'])); ?>
                                    </div>
                                </div>
                                <div class="comment-content">
                                    <?php echo nl2br(htmlspecialchars($comment['comment'])); ?>
                                </div>

                                <!-- Action Buttons -->
                                <div class="comment-actions">
                                    <?php if (isLoggedIn()): ?>
                                    <button class="btn reply-btn" data-comment-id="<?php echo $comment['id']; ?>">
                                        <i class="fas fa-reply me-1"></i>Reply
                                    </button>
                                    <button class="btn like-btn" data-comment-id="<?php echo $comment['id']; ?>">
                                        <i class="far fa-thumbs-up me-1"></i>Like
                                    </button>

                                    <?php
                                    // Check if user can delete this comment
                                    $can_delete = false;
                                    $current_user_id = $_SESSION['user_id'];
                                    $current_user_type = getUserType();

                                    if ($current_user_type === 'admin') {
                                        // Admin can delete any comment
                                        $can_delete = true;
                                    } elseif ($current_user_type === 'business') {
                                        // Business can delete comments on their own job posts
                                        if ($comment['business_id'] == $current_user_id) {
                                            $can_delete = true;
                                        }
                                    }
                                    // Jobseekers cannot delete comments

                                    if ($can_delete):
                                    ?>
                                    <form method="POST" class="d-inline" onsubmit="return confirm('Are you sure you want to delete this comment?');">
                                        <input type="hidden" name="action" value="delete_comment">
                                        <input type="hidden" name="comment_id" value="<?php echo $comment['id']; ?>">
                                        <button type="submit" class="btn btn-outline-danger btn-sm">
                                            <i class="fas fa-trash me-1"></i>Delete
                                        </button>
                                    </form>
                                    <?php endif; ?>
                                    <?php endif; ?>
                                </div>

                                <!-- Reply Form (Hidden by default) -->
                                <?php if (isLoggedIn()): ?>
                                <div class="reply-form" id="reply-form-<?php echo $comment['id']; ?>" style="display: none;">
                                    <form method="POST">
                                        <input type="hidden" name="action" value="add_comment">
                                        <input type="hidden" name="parent_id" value="<?php echo $comment['id']; ?>">
                                        <div class="d-flex gap-2">
                                            <textarea name="comment" rows="2"
                                                      placeholder="Write a reply..." required></textarea>
                                            <div class="d-flex flex-column gap-1">
                                                <button type="submit" class="btn btn-primary btn-sm">Reply</button>
                                                <button type="button" class="btn btn-outline-secondary btn-sm cancel-reply">Cancel</button>
                                            </div>
                                        </div>
                                    </form>
                                </div>
                                <?php endif; ?>

                                <!-- Replies -->
                                <?php if (isset($replies[$comment['id']])): ?>
                                <div class="replies">
                                    <?php foreach ($replies[$comment['id']] as $reply): ?>
                                    <div class="reply-item">
                                        <div class="comment-header">
                                            <div class="comment-author">
                                                <div class="avatar">
                                                    <i class="fas fa-user-circle"></i>
                                                </div>
                                                <div>
                                                    <h6>
                                                        <?php
                                                        $replier_name = htmlspecialchars($reply['first_name'] . ' ' . $reply['last_name']);
                                                        $is_jobseeker_reply = $reply['user_type'] === 'jobseeker';
                                                        $can_view_profile = isLoggedIn() && (getUserType() === 'admin' || getUserType() === 'business');

                                                        if ($is_jobseeker_reply && $can_view_profile): ?>
                                                            <a href="view-jobseeker-profile.php?user_id=<?php echo $reply['user_id']; ?>"
                                                               class="text-decoration-none jobseeker-profile-link"
                                                               title="View job seeker profile">
                                                                <?php echo $replier_name; ?>
                                                                <i class="fas fa-external-link-alt ms-1 small"></i>
                                                            </a>
                                                        <?php else: ?>
                                                            <?php echo $replier_name; ?>
                                                        <?php endif; ?>
                                                    </h6>
                                                    <span class="badge bg-<?php echo $reply['user_type'] === 'business' ? 'primary' : ($reply['user_type'] === 'admin' ? 'danger' : 'success'); ?>">
                                                        <?php echo ucfirst($reply['user_type']); ?>
                                                    </span>
                                                </div>
                                            </div>
                                            <div class="comment-timestamp">
                                                <?php echo date('M j, Y g:i A', strtotime($reply['created_at'])); ?>
                                            </div>
                                        </div>
                                        <div class="reply-content">
                                            <?php echo nl2br(htmlspecialchars($reply['comment'])); ?>
                                        </div>
                                    </div>
                                    <?php endforeach; ?>
                                </div>
                                <?php endif; ?>
                            </div>
                            <?php endforeach; ?>
                        </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>

            <!-- Company Sidebar -->
            <div class="col-lg-4">
                <div class="card shadow">
                    <div class="card-header">
                        <h5 class="mb-0">About the Company</h5>
                    </div>
                    <div class="card-body">
                        <h6 class="text-primary"><?php echo htmlspecialchars($job['company_name']); ?></h6>
                        
                        <?php if (!empty($job['business_description'])): ?>
                        <p class="text-muted mb-3">
                            <?php echo nl2br(htmlspecialchars($job['business_description'])); ?>
                        </p>
                        <?php endif; ?>

                        <div class="mb-3">
                            <small class="text-muted d-block">Contact Person</small>
                            <span><?php echo htmlspecialchars($job['first_name'] . ' ' . $job['last_name']); ?></span>
                        </div>

                        <?php if (!empty($job['business_address'])): ?>
                        <div class="mb-3">
                            <small class="text-muted d-block">Address</small>
                            <span><i class="fas fa-map-marker-alt me-1"></i><?php echo htmlspecialchars($job['business_address']); ?></span>
                        </div>
                        <?php endif; ?>

                        <?php if (!empty($job['phone'])): ?>
                        <div class="mb-3">
                            <small class="text-muted d-block">Phone</small>
                            <span><i class="fas fa-phone me-1"></i>+63<?php echo htmlspecialchars($job['phone']); ?></span>
                        </div>
                        <?php endif; ?>

                        <div class="mb-3">
                            <small class="text-muted d-block">Email</small>
                            <span><i class="fas fa-envelope me-1"></i><?php echo htmlspecialchars($job['email']); ?></span>
                        </div>
                    </div>
                </div>

                <!-- Similar Jobs -->
                <div class="card shadow mt-4">
                    <div class="card-header">
                        <h5 class="mb-0">Similar Jobs</h5>
                    </div>
                    <div class="card-body">
                        <?php
                        try {
                            // Get similar jobs (same job type or similar skills)
                            $query = "SELECT jp.id, jp.title, bp.company_name, jp.location, jp.created_at
                                      FROM job_posts jp 
                                      JOIN business_profiles bp ON jp.business_id = bp.user_id 
                                      WHERE jp.status = 'approved' AND jp.id != ? 
                                      AND (jp.job_type = ? OR jp.skills_required LIKE ?)
                                      ORDER BY jp.created_at DESC 
                                      LIMIT 5";
                            $stmt = $db->prepare($query);
                            $skills_pattern = '%' . $job['skills_required'] . '%';
                            $stmt->execute([$job_id, $job['job_type'], $skills_pattern]);
                            $similar_jobs = $stmt->fetchAll(PDO::FETCH_ASSOC);

                            if ($similar_jobs):
                                foreach ($similar_jobs as $similar_job):
                        ?>
                        <div class="mb-3 pb-3 border-bottom">
                            <h6 class="mb-1">
                                <a href="job-details.php?id=<?php echo $similar_job['id']; ?>" class="text-decoration-none">
                                    <?php echo htmlspecialchars($similar_job['title']); ?>
                                </a>
                            </h6>
                            <small class="text-muted d-block"><?php echo htmlspecialchars($similar_job['company_name']); ?></small>
                            <small class="text-muted">
                                <i class="fas fa-map-marker-alt me-1"></i><?php echo htmlspecialchars($similar_job['location']); ?>
                                <span class="ms-2">
                                    <i class="fas fa-calendar me-1"></i><?php echo date('M j', strtotime($similar_job['created_at'])); ?>
                                </span>
                            </small>
                        </div>
                        <?php 
                                endforeach;
                            else:
                        ?>
                        <p class="text-muted mb-0">No similar jobs found.</p>
                        <?php 
                            endif;
                        } catch (Exception $e) {
                            echo '<p class="text-muted mb-0">Unable to load similar jobs.</p>';
                        }
                        ?>
                    </div>
                </div>
            </div>
        </div>
        <?php endif; ?>
    </div>
</main>

<script>
// Enhanced Comment System with Animations
document.addEventListener('DOMContentLoaded', function() {
    // Reply button click handlers with smooth animations
    document.querySelectorAll('.reply-btn').forEach(button => {
        button.addEventListener('click', function() {
            const commentId = this.getAttribute('data-comment-id');
            const replyForm = document.getElementById('reply-form-' + commentId);

            // Hide all other reply forms with animation
            document.querySelectorAll('.reply-form').forEach(form => {
                if (form.id !== 'reply-form-' + commentId) {
                    form.classList.remove('show');
                    setTimeout(() => {
                        form.style.display = 'none';
                    }, 300);
                }
            });

            // Toggle current reply form with animation
            if (replyForm.style.display === 'none' || !replyForm.classList.contains('show')) {
                replyForm.style.display = 'block';
                setTimeout(() => {
                    replyForm.classList.add('show');
                    replyForm.querySelector('textarea').focus();
                }, 10);
            } else {
                replyForm.classList.remove('show');
                setTimeout(() => {
                    replyForm.style.display = 'none';
                }, 300);
            }
        });
    });

    // Cancel reply button handlers with animation
    document.querySelectorAll('.cancel-reply').forEach(button => {
        button.addEventListener('click', function() {
            const replyForm = this.closest('.reply-form');
            replyForm.classList.remove('show');
            setTimeout(() => {
                replyForm.style.display = 'none';
            }, 300);
        });
    });

    // Like button functionality with animation
    document.querySelectorAll('.like-btn').forEach(button => {
        button.addEventListener('click', function() {
            this.classList.toggle('liked');

            // Add heartbeat animation
            this.style.animation = 'none';
            setTimeout(() => {
                this.style.animation = 'heartbeat 1.2s ease-in-out';
            }, 10);

            // Update like count (placeholder - would need backend implementation)
            const icon = this.querySelector('i');
            if (this.classList.contains('liked')) {
                icon.classList.remove('far');
                icon.classList.add('fas');
            } else {
                icon.classList.remove('fas');
                icon.classList.add('far');
            }
        });
    });

    // Auto-resize textareas with smooth transition
    document.querySelectorAll('textarea').forEach(textarea => {
        textarea.style.transition = 'height 0.2s ease';

        textarea.addEventListener('input', function() {
            this.style.height = 'auto';
            this.style.height = this.scrollHeight + 'px';
        });

        // Add focus effects
        textarea.addEventListener('focus', function() {
            this.parentElement.style.transform = 'scale(1.02)';
            this.parentElement.style.transition = 'transform 0.2s ease';
        });

        textarea.addEventListener('blur', function() {
            this.parentElement.style.transform = 'scale(1)';
        });
    });

    // Add loading animation for form submissions
    document.querySelectorAll('form').forEach(form => {
        form.addEventListener('submit', function() {
            const submitBtn = this.querySelector('button[type="submit"]');
            if (submitBtn) {
                submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-1"></i>Posting...';
                submitBtn.disabled = true;
            }
        });
    });

    // Intersection Observer for comment animations
    const observerOptions = {
        threshold: 0.1,
        rootMargin: '0px 0px -50px 0px'
    };

    const commentObserver = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                entry.target.style.opacity = '1';
                entry.target.style.transform = 'translateX(0) scale(1)';
            }
        });
    }, observerOptions);

    // Observe all comment items
    document.querySelectorAll('.comment-item, .reply-item').forEach(item => {
        commentObserver.observe(item);
    });

    // Add hover sound effect (optional - can be enabled/disabled)
    const playHoverSound = false; // Set to true to enable

    if (playHoverSound) {
        document.querySelectorAll('.comment-item, .reply-item').forEach(item => {
            item.addEventListener('mouseenter', function() {
                // Create a subtle audio feedback (very quiet)
                const audio = new Audio('data:audio/wav;base64,UklGRnoGAABXQVZFZm10IBAAAAABAAEAQB8AAEAfAAABAAgAZGF0YQoGAACBhYqFbF1fdJivrJBhNjVgodDbq2EcBj+a2/LDciUFLIHO8tiJNwgZaLvt559NEAxQp+PwtmMcBjiR1/LMeSwFJHfH8N2QQAoUXrTp66hVFApGn+DyvmwhBSuBzvLZiTYIG2m98OScTgwOUarm7blmGgU7k9n1unEiBC13yO/eizEIHWq+8+OWT');
                audio.volume = 0.1;
                audio.play().catch(() => {}); // Ignore errors
            });
        });
    }
});
</script>

<style>
/* Modern Comment System Styles */
/* Light theme (default) */
:root {
    --comment-bg-primary: #ffffff;
    --comment-bg-secondary: #f8f9fa;
    --comment-bg-hover: #f1f3f4;
    --comment-border: #e1e5e9;
    --comment-border-hover: #c3c8cd;
    --comment-text-primary: #1c1e21;
    --comment-text-secondary: #65676b;
    --comment-text-muted: #8a8d91;
    --comment-shadow: rgba(0, 0, 0, 0.1);
    --comment-shadow-hover: rgba(0, 0, 0, 0.15);
    --comment-accent: #1877f2;
    --comment-accent-hover: #166fe5;
    --comment-success: #42b883;
    --comment-success-hover: #369870;
    --comment-danger: #e74c3c;
    --comment-danger-hover: #c0392b;
}

/* Dark mode support using existing theme system */
[data-theme="dark"] {
    --comment-bg-primary: var(--bg-secondary);
    --comment-bg-secondary: var(--bg-tertiary);
    --comment-bg-hover: var(--bg-quaternary);
    --comment-border: var(--border-color);
    --comment-border-hover: var(--border-strong);
    --comment-text-primary: var(--text-primary);
    --comment-text-secondary: var(--text-secondary);
    --comment-text-muted: var(--text-muted);
    --comment-shadow: rgba(0, 0, 0, 0.3);
    --comment-shadow-hover: rgba(0, 0, 0, 0.4);
    --comment-accent: var(--primary-color);
    --comment-accent-hover: var(--primary-hover);
    --comment-success: var(--success-color);
    --comment-success-hover: #2ea043;
    --comment-danger: var(--danger-color);
    --comment-danger-hover: #e5484d;
}

/* Comments Container - Right Aligned */
.comments-section {
    max-width: 100%;
    margin-left: auto;
    margin-right: 0;
}

.comments-section .card-body {
    padding-left: 0;
    padding-right: 1.5rem;
    display: flex;
    flex-direction: column;
    align-items: flex-end;
    text-align: right;
}

.comments-list {
    display: flex !important;
    flex-direction: column !important;
    align-items: flex-end !important;
    gap: 1.5rem;
    padding: 0;
    margin: 0;
    width: 100%;
}

/* Modern Comment Form */
.comment-form {
    background: var(--comment-bg-secondary);
    border: 1px solid var(--comment-border);
    border-radius: 16px;
    padding: 1.5rem;
    margin-bottom: 2rem;
    width: 100%;
    max-width: 800px;
    margin-left: auto !important;
    margin-right: 0 !important;
    box-shadow: 0 2px 8px var(--comment-shadow);
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    align-self: flex-end !important;
}

.comment-form:hover {
    border-color: var(--comment-border-hover);
    box-shadow: 0 4px 16px var(--comment-shadow-hover);
}

.comment-form textarea {
    background: var(--comment-bg-primary);
    border: 1px solid var(--comment-border);
    color: var(--comment-text-primary);
    border-radius: 12px;
    padding: 1rem;
    font-size: 0.95rem;
    line-height: 1.5;
    resize: vertical;
    min-height: 80px;
    transition: all 0.2s ease;
}

.comment-form textarea:focus {
    border-color: var(--comment-accent);
    box-shadow: 0 0 0 3px rgba(24, 119, 242, 0.1);
    outline: none;
}

.comment-form textarea::placeholder {
    color: var(--comment-text-muted);
}

/* Individual Comment Items - Left Aligned */
.comment-item {
    background: var(--comment-bg-primary);
    border: 1px solid var(--comment-border);
    border-radius: 16px;
    padding: 1.5rem;
    width: 100%;
    max-width: 750px;
    margin-left: 0 !important;
    margin-right: auto !important;
    box-shadow: 0 2px 12px var(--comment-shadow);
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
    overflow: hidden;
    align-self: flex-start !important;
}

.comment-item::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 4px;
    height: 100%;
    background: linear-gradient(180deg, var(--comment-accent), var(--comment-success));
    opacity: 0;
    transition: opacity 0.3s ease;
}

.comment-item:hover::before {
    opacity: 1;
}

.comment-item:hover {
    transform: translateY(-3px) translateX(2px);
    box-shadow: 0 8px 25px var(--comment-shadow-hover);
    border-color: var(--comment-border-hover);
}

/* Comment Content Styling */
.comment-content {
    color: var(--comment-text-primary);
    font-size: 0.95rem;
    line-height: 1.6;
    white-space: pre-wrap;
    word-wrap: break-word;
    margin: 0.75rem 0 1rem 0;
    padding: 0;
    text-align: left;
}

.comment-item,
.comment-form {
    text-align: left;
}

/* Comment Header */
.comment-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 0.75rem;
}

.comment-author {
    display: flex;
    align-items: center;
    gap: 0.75rem;
}

.comment-author h6 {
    color: var(--comment-text-primary);
    font-weight: 600;
    font-size: 0.9rem;
    margin: 0;
}

/* Jobseeker Profile Link Styling */
.jobseeker-profile-link {
    color: var(--comment-accent) !important;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
    display: inline-flex;
    align-items: center;
}

.jobseeker-profile-link:hover {
    color: var(--comment-success) !important;
    transform: translateY(-1px);
    text-decoration: none !important;
}

.jobseeker-profile-link::after {
    content: '';
    position: absolute;
    bottom: -2px;
    left: 0;
    width: 0;
    height: 2px;
    background: linear-gradient(90deg, var(--comment-accent), var(--comment-success));
    transition: width 0.3s ease;
}

.jobseeker-profile-link:hover::after {
    width: 100%;
}

.jobseeker-profile-link .fas {
    transition: transform 0.3s ease;
}

.jobseeker-profile-link:hover .fas {
    transform: translateX(2px) scale(1.1);
}

.comment-timestamp {
    color: var(--comment-text-muted);
    font-size: 0.8rem;
    font-weight: 400;
}

/* Avatar Styling */
.avatar {
    flex-shrink: 0;
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background: linear-gradient(135deg, var(--comment-accent), var(--comment-success));
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 1.2rem;
}

/* Action Buttons */
.comment-actions {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    margin-top: 1rem;
}

.comment-actions .btn {
    font-size: 0.8rem;
    padding: 0.4rem 0.8rem;
    border-radius: 20px;
    font-weight: 500;
    transition: all 0.2s ease;
}

.reply-btn {
    background: transparent;
    border: 1px solid var(--comment-border);
    color: var(--comment-text-secondary);
}

.reply-btn:hover {
    background: var(--comment-success);
    border-color: var(--comment-success);
    color: white;
    transform: translateY(-1px);
}

.like-btn {
    background: transparent;
    border: 1px solid var(--comment-border);
    color: var(--comment-text-secondary);
}

.like-btn:hover {
    background: var(--comment-accent);
    border-color: var(--comment-accent);
    color: white;
    transform: translateY(-1px);
}

/* Reply Items - Nested and Left Aligned */
.replies {
    margin-top: 1.5rem;
    padding-left: 2rem;
    border-left: 2px solid var(--comment-border);
    position: relative;
}

.reply-item {
    background: var(--comment-bg-secondary);
    border: 1px solid var(--comment-border);
    border-radius: 12px;
    padding: 1rem;
    margin-bottom: 1rem;
    width: 100%;
    max-width: 650px;
    margin-left: 0;
    transition: all 0.3s ease;
    position: relative;
}

.reply-item::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 3px;
    height: 100%;
    background: var(--comment-accent);
    border-radius: 12px 0 0 12px;
}

.reply-item:hover {
    background: var(--comment-bg-hover);
    transform: translateX(3px);
    box-shadow: 0 4px 12px var(--comment-shadow);
}

.reply-content {
    color: var(--comment-text-primary);
    font-size: 0.9rem;
    line-height: 1.5;
    white-space: pre-wrap;
    word-wrap: break-word;
    margin: 0.5rem 0;
}

/* Reply Form */
.reply-form {
    margin-top: 1rem;
    padding: 1rem;
    background: var(--comment-bg-secondary);
    border-radius: 12px;
    border: 1px solid var(--comment-border);
}

.reply-form textarea {
    background: var(--comment-bg-primary);
    border: 1px solid var(--comment-border);
    color: var(--comment-text-primary);
    border-radius: 8px;
    font-size: 0.9rem;
    min-height: 60px;
}

/* Badges */
.badge {
    font-size: 0.7rem;
    padding: 0.3rem 0.6rem;
    border-radius: 12px;
    font-weight: 500;
}

/* Enhanced Animations and Micro-interactions */
@keyframes slideInRight {
    from {
        opacity: 0;
        transform: translateX(30px) scale(0.95);
    }
    to {
        opacity: 1;
        transform: translateX(0) scale(1);
    }
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(20px) scale(0.98);
    }
    to {
        opacity: 1;
        transform: translateY(0) scale(1);
    }
}

@keyframes buttonPulse {
    0% {
        transform: scale(1);
        box-shadow: 0 0 0 0 rgba(24, 119, 242, 0.4);
    }
    70% {
        transform: scale(1.05);
        box-shadow: 0 0 0 10px rgba(24, 119, 242, 0);
    }
    100% {
        transform: scale(1);
        box-shadow: 0 0 0 0 rgba(24, 119, 242, 0);
    }
}

@keyframes shimmer {
    0% {
        background-position: -200px 0;
    }
    100% {
        background-position: calc(200px + 100%) 0;
    }
}

@keyframes heartbeat {
    0% {
        transform: scale(1);
    }
    14% {
        transform: scale(1.1);
    }
    28% {
        transform: scale(1);
    }
    42% {
        transform: scale(1.1);
    }
    70% {
        transform: scale(1);
    }
}

.comment-item {
    animation: slideInRight 0.4s cubic-bezier(0.4, 0, 0.2, 1);
}

.reply-item {
    animation: fadeInUp 0.3s ease-out;
}

/* Button hover animations */
.comment-actions .btn:hover {
    animation: buttonPulse 0.6s ease-in-out;
}

/* Like button special animation */
.like-btn.liked {
    animation: heartbeat 1.2s ease-in-out;
    color: #e74c3c !important;
    border-color: #e74c3c !important;
}

/* Loading shimmer effect for new comments */
.comment-loading {
    background: linear-gradient(90deg, var(--comment-bg-secondary) 25%, var(--comment-bg-hover) 50%, var(--comment-bg-secondary) 75%);
    background-size: 200px 100%;
    animation: shimmer 1.5s infinite;
}

/* Staggered animation for multiple comments */
.comment-item:nth-child(1) { animation-delay: 0s; }
.comment-item:nth-child(2) { animation-delay: 0.1s; }
.comment-item:nth-child(3) { animation-delay: 0.2s; }
.comment-item:nth-child(4) { animation-delay: 0.3s; }
.comment-item:nth-child(5) { animation-delay: 0.4s; }

/* Reply form slide animation */
.reply-form {
    max-height: 0;
    overflow: hidden;
    transition: max-height 0.3s cubic-bezier(0.4, 0, 0.2, 1),
                padding 0.3s cubic-bezier(0.4, 0, 0.2, 1),
                opacity 0.3s ease;
    opacity: 0;
}

.reply-form.show {
    max-height: 200px;
    opacity: 1;
}

/* Avatar hover effect */
.avatar {
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.avatar:hover {
    transform: scale(1.1) rotate(5deg);
    box-shadow: 0 4px 12px var(--comment-shadow-hover);
}

/* Text typing animation for new comments */
@keyframes typing {
    from { width: 0; }
    to { width: 100%; }
}

.comment-content.typing {
    overflow: hidden;
    white-space: nowrap;
    border-right: 2px solid var(--comment-accent);
    animation: typing 2s steps(40, end), blink-caret 0.75s step-end infinite;
}

@keyframes blink-caret {
    from, to { border-color: transparent; }
    50% { border-color: var(--comment-accent); }
}

/* Force left alignment - Override Bootstrap defaults */
.comments-section .card-body > * {
    margin-left: 0 !important;
    margin-right: auto !important;
}

/* Very specific targeting for comment alignment */
.comments-section .comments-list .comment-item {
    margin-left: 0 !important;
    margin-right: auto !important;
    float: left !important;
    clear: both !important;
}

.comments-section .comment-form {
    margin-left: 0 !important;
    margin-right: auto !important;
    float: left !important;
    clear: both !important;
}

.comments-section .card-body .text-center {
    margin-left: auto !important;
    margin-right: auto !important;
}

.comments-section .alert {
    margin-left: 0 !important;
    margin-right: auto !important;
    max-width: 800px;
}

/* Responsive Design */
@media (max-width: 768px) {
    .comments-list {
        align-items: stretch !important;
    }

    .comment-item,
    .comment-form {
        max-width: 100% !important;
        margin-left: 0 !important;
        margin-right: 0 !important;
        align-self: stretch !important;
    }

    .reply-item {
        max-width: 100% !important;
        margin-left: 0 !important;
    }

    .replies {
        padding-left: 1rem;
    }

    .comment-actions {
        flex-wrap: wrap;
        gap: 0.3rem;
    }

    .comment-actions .btn {
        font-size: 0.75rem;
        padding: 0.3rem 0.6rem;
    }

    .comments-section .card-body > * {
        margin-left: 0 !important;
        margin-right: 0 !important;
    }

    /* Mobile-specific jobseeker profile link styling */
    .jobseeker-profile-link {
        font-size: 0.9rem;
    }

    .jobseeker-profile-link .fas {
        font-size: 0.8rem;
    }
}

/* Gradient header */
.bg-gradient-primary {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

/* Job header image */
.job-header-image img {
    border-radius: 0.375rem 0.375rem 0 0;
}
</style>

<?php include __DIR__ . '/includes/footer.php'; ?>
