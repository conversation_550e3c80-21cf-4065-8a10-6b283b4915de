<?php
require_once __DIR__ . '/config/config.php';

$job_id = (int)($_GET['id'] ?? 0);
$error = '';
$success = '';
$job = null;
$skill_match = 0;

if ($job_id <= 0) {
    $error = 'Invalid job ID.';
} else {
    try {
        $database = new Database();
        $db = $database->getConnection();

        // Handle comment submission
        if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['action'])) {
            if ($_POST['action'] === 'add_comment') {
                if (isLoggedIn()) {
                    $comment = trim($_POST['comment'] ?? '');
                    $parent_id = !empty($_POST['parent_id']) ? (int)$_POST['parent_id'] : null;
                    
                    if (!empty($comment)) {
                        try {
                            $query = "INSERT INTO job_comments (job_id, user_id, comment, parent_id) VALUES (?, ?, ?, ?)";
                            $stmt = $db->prepare($query);
                            $stmt->execute([$job_id, $_SESSION['user_id'], $comment, $parent_id]);
                            
                            $success = "Comment added successfully!";
                            header("Location: job-details.php?id=$job_id&success=" . urlencode($success));
                            exit();
                        } catch (Exception $e) {
                            error_log("Comment submission error: " . $e->getMessage());
                            $error = "Failed to add comment. Please try again.";
                        }
                    } else {
                        $error = "Comment cannot be empty.";
                    }
                } else {
                    $error = "You must be logged in to comment.";
                }
            } elseif ($_POST['action'] === 'delete_comment') {
                if (isLoggedIn()) {
                    $comment_id = (int)($_POST['comment_id'] ?? 0);
                    
                    if ($comment_id > 0) {
                        try {
                            // Get comment details to check permissions
                            $query = "SELECT jc.*, jp.business_id, u.user_type 
                                      FROM job_comments jc 
                                      JOIN job_posts jp ON jc.job_id = jp.id 
                                      JOIN users u ON jc.user_id = u.id 
                                      WHERE jc.id = ?";
                            $stmt = $db->prepare($query);
                            $stmt->execute([$comment_id]);
                            $comment_details = $stmt->fetch(PDO::FETCH_ASSOC);
                            
                            if ($comment_details) {
                                $can_delete = false;
                                $current_user_id = $_SESSION['user_id'];
                                $current_user_type = getUserType();
                                
                                // Check permissions
                                if ($current_user_type === 'admin') {
                                    // Admin can delete any comment
                                    $can_delete = true;
                                } elseif ($current_user_type === 'business') {
                                    // Business can delete comments on their own job posts
                                    if ($comment_details['business_id'] == $current_user_id) {
                                        $can_delete = true;
                                    }
                                }
                                // Jobseekers cannot delete comments
                                
                                if ($can_delete) {
                                    // Delete the comment and all its replies
                                    $query = "DELETE FROM job_comments WHERE id = ? OR parent_id = ?";
                                    $stmt = $db->prepare($query);
                                    $stmt->execute([$comment_id, $comment_id]);
                                    
                                    $success = "Comment deleted successfully!";
                                    header("Location: job-details.php?id=$job_id&success=" . urlencode($success));
                                    exit();
                                } else {
                                    $error = "You don't have permission to delete this comment.";
                                }
                            } else {
                                $error = "Comment not found.";
                            }
                        } catch (Exception $e) {
                            error_log("Comment deletion error: " . $e->getMessage());
                            $error = "Failed to delete comment. Please try again.";
                        }
                    } else {
                        $error = "Invalid comment ID.";
                    }
                } else {
                    $error = "You must be logged in to delete comments.";
                }
            }
        }

        // Get success message
        if (isset($_GET['success'])) {
            $success = sanitize($_GET['success']);
        }

        // Get job details with company information
        $query = "SELECT jp.*, bp.company_name, bp.business_description, bp.business_address, u.first_name, u.last_name, u.email, u.phone
                  FROM job_posts jp 
                  JOIN business_profiles bp ON jp.business_id = bp.user_id 
                  JOIN users u ON bp.user_id = u.id 
                  WHERE jp.id = ? AND jp.status = 'approved'";
        $stmt = $db->prepare($query);
        $stmt->execute([$job_id]);
        $job = $stmt->fetch(PDO::FETCH_ASSOC);

        if (!$job) {
            $error = 'Job not found or not available.';
        } else {
            // Calculate skill match if user is jobseeker
            if (isLoggedIn() && getUserType() === 'jobseeker') {
                $query = "SELECT skills FROM jobseeker_profiles WHERE user_id = ?";
                $stmt = $db->prepare($query);
                $stmt->execute([$_SESSION['user_id']]);
                $profile = $stmt->fetch(PDO::FETCH_ASSOC);

                if (!empty($job['skills_required']) && !empty($profile['skills'])) {
                    $skill_match = calculateSkillMatch($job['skills_required'], $profile['skills']);
                }
            }

            // Get comments with user information and business_id for permission checking
            $query = "SELECT jc.*, u.first_name, u.last_name, u.user_type, jp.business_id 
                      FROM job_comments jc 
                      JOIN users u ON jc.user_id = u.id 
                      JOIN job_posts jp ON jc.job_id = jp.id 
                      WHERE jc.job_id = ? 
                      ORDER BY jc.created_at ASC";
            $stmt = $db->prepare($query);
            $stmt->execute([$job_id]);
            $all_comments = $stmt->fetchAll(PDO::FETCH_ASSOC);

            // Organize comments into parent-child structure
            $comments = [];
            $replies = [];
            
            foreach ($all_comments as $comment) {
                if ($comment['parent_id'] === null) {
                    $comments[] = $comment;
                } else {
                    if (!isset($replies[$comment['parent_id']])) {
                        $replies[$comment['parent_id']] = [];
                    }
                    $replies[$comment['parent_id']][] = $comment;
                }
            }
        }

    } catch (Exception $e) {
        error_log("Job details error: " . $e->getMessage());
        $error = 'An error occurred while loading job details.';
    }
}

$page_title = $job ? htmlspecialchars($job['title']) : 'Job Details';
include __DIR__ . '/includes/header.php';
?>

<main class="py-4">
    <div class="container">
        <?php if ($success): ?>
        <div class="alert alert-success alert-dismissible fade show" role="alert">
            <i class="fas fa-check me-2"></i><?php echo htmlspecialchars($success); ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
        <?php endif; ?>

        <?php if ($error): ?>
        <div class="alert alert-danger alert-dismissible fade show" role="alert">
            <i class="fas fa-exclamation-triangle me-2"></i><?php echo htmlspecialchars($error); ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
        <?php endif; ?>

        <?php if ($error && !$job): ?>
        <div class="row">
            <div class="col-12">
                <div class="alert alert-danger" role="alert">
                    <h4 class="alert-heading">Error</h4>
                    <p><?php echo htmlspecialchars($error); ?></p>
                    <hr>
                    <a href="jobs.php" class="btn btn-primary">Browse Jobs</a>
                </div>
            </div>
        </div>
        <?php else: ?>
        <div class="row">
            <!-- Job Details -->
            <div class="col-lg-8">
                <div class="card shadow mb-4">
                    <?php if (!empty($job['job_image'])): ?>
                    <div class="job-header-image">
                        <img src="uploads/job_images/<?php echo htmlspecialchars($job['job_image']); ?>" 
                             class="card-img-top" alt="<?php echo htmlspecialchars($job['title']); ?>" 
                             style="height: 250px; object-fit: cover;">
                    </div>
                    <?php endif; ?>
                    
                    <div class="card-header">
                        <div class="d-flex justify-content-between align-items-start">
                            <div>
                                <h1 class="h3 mb-1"><?php echo htmlspecialchars($job['title']); ?></h1>
                                <p class="text-primary mb-0">
                                    <i class="fas fa-building me-1"></i>
                                    <?php echo htmlspecialchars($job['company_name']); ?>
                                </p>
                            </div>
                            <div class="text-end">
                                <?php if ($job['salary_min'] && $job['salary_max']): ?>
                                <div class="h5 text-success mb-1">
                                    ₱<?php echo number_format($job['salary_min']); ?> - ₱<?php echo number_format($job['salary_max']); ?>
                                </div>
                                <small class="text-muted">per month</small>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>
                    <div class="card-body">
                        <!-- Job Meta Information -->
                        <div class="row mb-4">
                            <div class="col-md-3 mb-2">
                                <small class="text-muted d-block">Job Type</small>
                                <span class="badge bg-info"><?php echo ucfirst($job['job_type']); ?></span>
                            </div>
                            <div class="col-md-3 mb-2">
                                <small class="text-muted d-block">Location</small>
                                <span><i class="fas fa-map-marker-alt me-1"></i><?php echo htmlspecialchars($job['location']); ?></span>
                            </div>
                            <div class="col-md-3 mb-2">
                                <small class="text-muted d-block">Slots Available</small>
                                <span class="badge bg-<?php echo $job['slots_available'] > 0 ? 'success' : 'danger'; ?>">
                                    <i class="fas fa-users me-1"></i><?php echo $job['slots_available']; ?> position(s)
                                </span>
                            </div>
                            <div class="col-md-3 mb-2">
                                <small class="text-muted d-block">Posted</small>
                                <span><i class="fas fa-calendar me-1"></i><?php echo date('M j, Y', strtotime($job['created_at'])); ?></span>
                            </div>
                        </div>

                        <!-- Skill Match Display -->
                        <?php if ($skill_match > 0): ?>
                        <div class="alert alert-success mb-4">
                            <div class="d-flex align-items-center">
                                <i class="fas fa-star fa-2x me-3"></i>
                                <div>
                                    <h5 class="mb-1">Great Match!</h5>
                                    <p class="mb-0">You have a <strong><?php echo $skill_match; ?>%</strong> skill match with this position!</p>
                                </div>
                            </div>
                        </div>
                        <?php endif; ?>

                        <!-- Slots Warning -->
                        <?php if ($job['slots_available'] <= 0): ?>
                        <div class="alert alert-danger mb-4">
                            <div class="d-flex align-items-center">
                                <i class="fas fa-exclamation-triangle fa-2x me-3"></i>
                                <div>
                                    <h5 class="mb-1">No Positions Available</h5>
                                    <p class="mb-0">All positions for this job have been filled.</p>
                                </div>
                            </div>
                        </div>
                        <?php elseif ($job['slots_available'] <= 2): ?>
                        <div class="alert alert-warning mb-4">
                            <div class="d-flex align-items-center">
                                <i class="fas fa-clock fa-2x me-3"></i>
                                <div>
                                    <h5 class="mb-1">Limited Positions Available</h5>
                                    <p class="mb-0">Only <strong><?php echo $job['slots_available']; ?></strong> position(s) remaining!</p>
                                </div>
                            </div>
                        </div>
                        <?php endif; ?>

                        <!-- Job Description -->
                        <div class="mb-4">
                            <h5>Job Description</h5>
                            <div class="text-muted">
                                <?php echo nl2br(htmlspecialchars($job['description'])); ?>
                            </div>
                        </div>

                        <!-- Requirements -->
                        <?php if (!empty($job['requirements'])): ?>
                        <div class="mb-4">
                            <h5>Requirements</h5>
                            <div class="text-muted">
                                <?php echo nl2br(htmlspecialchars($job['requirements'])); ?>
                            </div>
                        </div>
                        <?php endif; ?>

                        <!-- Skills Required -->
                        <?php if (!empty($job['skills_required'])): ?>
                        <div class="mb-4">
                            <h5>Skills Required</h5>
                            <div>
                                <?php 
                                $skills = explode(',', $job['skills_required']);
                                foreach ($skills as $skill): 
                                    $skill = trim($skill);
                                    if (!empty($skill)):
                                ?>
                                <span class="badge bg-secondary me-1 mb-1"><?php echo htmlspecialchars($skill); ?></span>
                                <?php 
                                    endif;
                                endforeach; 
                                ?>
                            </div>
                        </div>
                        <?php endif; ?>

                        <!-- Benefits Section -->
                        <div class="mb-4">
                            <h5>Benefits & Perks</h5>
                            <div class="row g-3">
                                <div class="col-md-6">
                                    <div class="d-flex align-items-center mb-2">
                                        <i class="fas fa-check-circle text-success me-2"></i>
                                        <span>Competitive Salary Package</span>
                                    </div>
                                    <div class="d-flex align-items-center mb-2">
                                        <i class="fas fa-check-circle text-success me-2"></i>
                                        <span>Health Insurance Coverage</span>
                                    </div>
                                    <div class="d-flex align-items-center mb-2">
                                        <i class="fas fa-check-circle text-success me-2"></i>
                                        <span>Professional Development</span>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="d-flex align-items-center mb-2">
                                        <i class="fas fa-check-circle text-success me-2"></i>
                                        <span>Flexible Work Schedule</span>
                                    </div>
                                    <div class="d-flex align-items-center mb-2">
                                        <i class="fas fa-check-circle text-success me-2"></i>
                                        <span>Performance Bonuses</span>
                                    </div>
                                    <div class="d-flex align-items-center mb-2">
                                        <i class="fas fa-check-circle text-success me-2"></i>
                                        <span>Team Building Activities</span>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Back Button -->
                        <div class="mb-4">
                            <button class="btn btn-outline-secondary" onclick="window.history.back()">
                                <i class="fas fa-arrow-left me-1"></i>Back
                            </button>
                        </div>
                    </div>
                </div>

                <!-- Comments Section -->
                <div class="card shadow">
                    <div class="card-header bg-gradient-primary text-white">
                        <h5 class="mb-0">
                            <i class="fas fa-comments me-2"></i>Comments (<?php echo count($comments); ?>)
                        </h5>
                    </div>
                    <div class="card-body">
                        <!-- Add Comment Form -->
                        <?php if (isLoggedIn()): ?>
                        <form method="POST" class="mb-4 comment-form" id="commentForm">
                            <input type="hidden" name="action" value="add_comment">
                            <div class="d-flex align-items-start gap-3">
                                <div class="avatar">
                                    <i class="fas fa-user-circle fa-2x text-primary"></i>
                                </div>
                                <div class="flex-grow-1">
                                    <textarea class="form-control border-0 bg-light" id="comment" name="comment" rows="3" 
                                              placeholder="Write a comment..." required></textarea>
                                    <div class="d-flex justify-content-between align-items-center mt-2">
                                        <small class="text-muted">
                                            <i class="fas fa-info-circle me-1"></i>
                                            Share your thoughts about this job
                                        </small>
                                        <button type="submit" class="btn btn-primary btn-sm">
                                            <i class="fas fa-paper-plane me-1"></i>Post
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </form>
                        <?php else: ?>
                        <div class="alert alert-info mb-4">
                            <i class="fas fa-info-circle me-2"></i>
                            <a href="auth/login.php" class="alert-link">Login</a> to post a comment.
                        </div>
                        <?php endif; ?>

                        <!-- Display Comments -->
                        <?php if (empty($comments)): ?>
                        <div class="text-center py-5">
                            <i class="fas fa-comments fa-3x text-muted mb-3"></i>
                            <h6>No comments yet</h6>
                            <p class="text-muted">Be the first to comment on this job posting!</p>
                        </div>
                        <?php else: ?>
                        <div class="comments-list">
                            <?php foreach ($comments as $comment): ?>
                            <div class="comment-item mb-3 p-3 bg-light rounded-3 hover-lift">
                                <div class="d-flex gap-3">
                                    <div class="avatar">
                                        <i class="fas fa-user-circle fa-2x text-primary"></i>
                                    </div>
                                    <div class="flex-grow-1">
                                        <div class="d-flex align-items-center mb-2">
                                            <h6 class="mb-0 fw-bold">
                                                <?php 
                                                $commenter_name = htmlspecialchars($comment['first_name'] . ' ' . $comment['last_name']);
                                                $is_job_owner = isLoggedIn() && getUserType() === 'business' && $_SESSION['user_id'] == $job['business_id'];
                                                $is_jobseeker_comment = $comment['user_type'] === 'jobseeker';
                                                
                                                if ($is_job_owner && $is_jobseeker_comment): ?>
                                                    <a href="view-jobseeker-profile.php?user_id=<?php echo $comment['user_id']; ?>" 
                                                       class="text-decoration-none text-primary" 
                                                       title="View job seeker profile">
                                                        <?php echo $commenter_name; ?>
                                                        <i class="fas fa-external-link-alt ms-1 small"></i>
                                                    </a>
                                                <?php else: ?>
                                                    <?php echo $commenter_name; ?>
                                                <?php endif; ?>
                                            </h6>
                                            <span class="badge bg-<?php echo $comment['user_type'] === 'business' ? 'primary' : ($comment['user_type'] === 'admin' ? 'danger' : 'success'); ?> ms-2">
                                                <?php echo ucfirst($comment['user_type']); ?>
                                            </span>
                                            <small class="text-muted ms-auto">
                                                <?php echo date('M j, Y g:i A', strtotime($comment['created_at'])); ?>
                                            </small>
                                        </div>
                                        <div class="comment-content mb-3">
                                            <?php echo nl2br(htmlspecialchars($comment['comment'])); ?>
                                        </div>
                                        
                                        <!-- Action Buttons -->
                                        <div class="d-flex align-items-center gap-3">
                                            <?php if (isLoggedIn()): ?>
                                            <button class="btn btn-sm btn-outline-primary reply-btn" data-comment-id="<?php echo $comment['id']; ?>">
                                                <i class="fas fa-reply me-1"></i>Reply
                                            </button>
                                            <button class="btn btn-sm btn-outline-secondary like-btn" data-comment-id="<?php echo $comment['id']; ?>">
                                                <i class="far fa-thumbs-up me-1"></i>Like
                                            </button>
                                            
                                            <?php
                                            // Check if user can delete this comment
                                            $can_delete = false;
                                            $current_user_id = $_SESSION['user_id'];
                                            $current_user_type = getUserType();
                                            
                                            if ($current_user_type === 'admin') {
                                                // Admin can delete any comment
                                                $can_delete = true;
                                            } elseif ($current_user_type === 'business') {
                                                // Business can delete comments on their own job posts
                                                if ($comment['business_id'] == $current_user_id) {
                                                    $can_delete = true;
                                                }
                                            }
                                            // Jobseekers cannot delete comments
                                            
                                            if ($can_delete):
                                            ?>
                                            <form method="POST" class="d-inline" onsubmit="return confirm('Are you sure you want to delete this comment?');">
                                                <input type="hidden" name="action" value="delete_comment">
                                                <input type="hidden" name="comment_id" value="<?php echo $comment['id']; ?>">
                                                <button type="submit" class="btn btn-sm btn-outline-danger">
                                                    <i class="fas fa-trash me-1"></i>Delete
                                                </button>
                                            </form>
                                            <?php endif; ?>
                                            <?php endif; ?>
                                        </div>

                                        <!-- Reply Form (Hidden by default) -->
                                        <?php if (isLoggedIn()): ?>
                                        <div class="reply-form mt-3" id="reply-form-<?php echo $comment['id']; ?>" style="display: none;">
                                            <form method="POST">
                                                <input type="hidden" name="action" value="add_comment">
                                                <input type="hidden" name="parent_id" value="<?php echo $comment['id']; ?>">
                                                <div class="d-flex gap-2">
                                                    <textarea class="form-control" name="comment" rows="2" 
                                                              placeholder="Write a reply..." required></textarea>
                                                    <div class="d-flex flex-column gap-1">
                                                        <button type="submit" class="btn btn-sm btn-primary">Reply</button>
                                                        <button type="button" class="btn btn-sm btn-outline-secondary cancel-reply">Cancel</button>
                                                    </div>
                                                </div>
                                            </form>
                                        </div>
                                        <?php endif; ?>

                                        <!-- Display Replies -->
                                        <?php if (isset($replies[$comment['id']])): ?>
                                        <div class="replies mt-3">
                                            <?php foreach ($replies[$comment['id']] as $reply): ?>
                                            <div class="reply-item p-3 bg-white rounded-3 border-start border-3 border-primary ms-4">
                                                <div class="d-flex gap-2">
                                                    <div class="avatar">
                                                        <i class="fas fa-user-circle text-muted"></i>
                                                    </div>
                                                    <div class="flex-grow-1">
                                                        <div class="d-flex align-items-center mb-1">
                                                            <h6 class="mb-0 small fw-bold">
                                                                <?php 
                                                                $replyer_name = htmlspecialchars($reply['first_name'] . ' ' . $reply['last_name']);
                                                                $is_job_owner = isLoggedIn() && getUserType() === 'business' && $_SESSION['user_id'] == $job['business_id'];
                                                                $is_jobseeker_reply = $reply['user_type'] === 'jobseeker';
                                                                
                                                                if ($is_job_owner && $is_jobseeker_reply): ?>
                                                                    <a href="view-jobseeker-profile.php?user_id=<?php echo $reply['user_id']; ?>" 
                                                                       class="text-decoration-none text-primary" 
                                                                       title="View job seeker profile">
                                                                        <?php echo $replyer_name; ?>
                                                                        <i class="fas fa-external-link-alt ms-1 small"></i>
                                                                    </a>
                                                                <?php else: ?>
                                                                    <?php echo $replyer_name; ?>
                                                                <?php endif; ?>
                                                            </h6>
                                                            <span class="badge bg-<?php echo $reply['user_type'] === 'business' ? 'primary' : ($reply['user_type'] === 'admin' ? 'danger' : 'success'); ?> ms-2 small">
                                                                <?php echo ucfirst($reply['user_type']); ?>
                                                            </span>
                                                            <small class="text-muted ms-auto">
                                                                <?php echo date('M j, Y g:i A', strtotime($reply['created_at'])); ?>
                                                            </small>
                                                        </div>
                                                        <div class="reply-content small">
                                                            <?php echo nl2br(htmlspecialchars($reply['comment'])); ?>
                                                        </div>
                                                        
                                                        <!-- Reply Action Buttons -->
                                                        <?php if (isLoggedIn()): ?>
                                                        <div class="mt-2">
                                                            <?php
                                                            // Check if user can delete this reply
                                                            $can_delete_reply = false;
                                                            $current_user_id = $_SESSION['user_id'];
                                                            $current_user_type = getUserType();
                                                            
                                                            if ($current_user_type === 'admin') {
                                                                // Admin can delete any reply
                                                                $can_delete_reply = true;
                                                            } elseif ($current_user_type === 'business') {
                                                                // Business can delete replies on their own job posts
                                                                if ($comment['business_id'] == $current_user_id) {
                                                                    $can_delete_reply = true;
                                                                }
                                                            }
                                                            // Jobseekers cannot delete replies
                                                            
                                                            if ($can_delete_reply):
                                                            ?>
                                                            <form method="POST" class="d-inline" onsubmit="return confirm('Are you sure you want to delete this reply?');">
                                                                <input type="hidden" name="action" value="delete_comment">
                                                                <input type="hidden" name="comment_id" value="<?php echo $reply['id']; ?>">
                                                                <button type="submit" class="btn btn-sm btn-outline-danger btn-sm">
                                                                    <i class="fas fa-trash me-1"></i>Delete
                                                                </button>
                                                            </form>
                                                            <?php endif; ?>
                                                        </div>
                                                        <?php endif; ?>
                                                    </div>
                                                </div>
                                            </div>
                                            <?php endforeach; ?>
                                        </div>
                                        <?php endif; ?>
                                    </div>
                                </div>
                            </div>
                            <?php endforeach; ?>
                        </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>

            <!-- Company Sidebar -->
            <div class="col-lg-4">
                <div class="card shadow">
                    <div class="card-header">
                        <h5 class="mb-0">About the Company</h5>
                    </div>
                    <div class="card-body">
                        <h6 class="text-primary"><?php echo htmlspecialchars($job['company_name']); ?></h6>
                        
                        <?php if (!empty($job['business_description'])): ?>
                        <p class="text-muted mb-3">
                            <?php echo nl2br(htmlspecialchars($job['business_description'])); ?>
                        </p>
                        <?php endif; ?>

                        <div class="mb-3">
                            <small class="text-muted d-block">Contact Person</small>
                            <span><?php echo htmlspecialchars($job['first_name'] . ' ' . $job['last_name']); ?></span>
                        </div>

                        <?php if (!empty($job['business_address'])): ?>
                        <div class="mb-3">
                            <small class="text-muted d-block">Address</small>
                            <span><i class="fas fa-map-marker-alt me-1"></i><?php echo htmlspecialchars($job['business_address']); ?></span>
                        </div>
                        <?php endif; ?>

                        <?php if (!empty($job['phone'])): ?>
                        <div class="mb-3">
                            <small class="text-muted d-block">Phone</small>
                            <span><i class="fas fa-phone me-1"></i>+63<?php echo htmlspecialchars($job['phone']); ?></span>
                        </div>
                        <?php endif; ?>

                        <div class="mb-3">
                            <small class="text-muted d-block">Email</small>
                            <span><i class="fas fa-envelope me-1"></i><?php echo htmlspecialchars($job['email']); ?></span>
                        </div>
                    </div>
                </div>

                <!-- Similar Jobs -->
                <div class="card shadow mt-4">
                    <div class="card-header">
                        <h5 class="mb-0">Similar Jobs</h5>
                    </div>
                    <div class="card-body">
                        <?php
                        try {
                            // Get similar jobs (same job type or similar skills)
                            $query = "SELECT jp.id, jp.title, bp.company_name, jp.location, jp.created_at
                                      FROM job_posts jp 
                                      JOIN business_profiles bp ON jp.business_id = bp.user_id 
                                      WHERE jp.status = 'approved' AND jp.id != ? 
                                      AND (jp.job_type = ? OR jp.skills_required LIKE ?)
                                      ORDER BY jp.created_at DESC 
                                      LIMIT 5";
                            $stmt = $db->prepare($query);
                            $skills_pattern = '%' . $job['skills_required'] . '%';
                            $stmt->execute([$job_id, $job['job_type'], $skills_pattern]);
                            $similar_jobs = $stmt->fetchAll(PDO::FETCH_ASSOC);

                            if ($similar_jobs):
                                foreach ($similar_jobs as $similar_job):
                        ?>
                        <div class="mb-3 pb-3 border-bottom">
                            <h6 class="mb-1">
                                <a href="job-details.php?id=<?php echo $similar_job['id']; ?>" class="text-decoration-none">
                                    <?php echo htmlspecialchars($similar_job['title']); ?>
                                </a>
                            </h6>
                            <small class="text-muted d-block"><?php echo htmlspecialchars($similar_job['company_name']); ?></small>
                            <small class="text-muted">
                                <i class="fas fa-map-marker-alt me-1"></i><?php echo htmlspecialchars($similar_job['location']); ?>
                                <span class="ms-2">
                                    <i class="fas fa-calendar me-1"></i><?php echo date('M j', strtotime($similar_job['created_at'])); ?>
                                </span>
                            </small>
                        </div>
                        <?php 
                                endforeach;
                            else:
                        ?>
                        <p class="text-muted mb-0">No similar jobs found.</p>
                        <?php 
                            endif;
                        } catch (Exception $e) {
                            echo '<p class="text-muted mb-0">Unable to load similar jobs.</p>';
                        }
                        ?>
                    </div>
                </div>
            </div>
        </div>
        <?php endif; ?>
    </div>
</main>

<script>
// Reply functionality
document.addEventListener('DOMContentLoaded', function() {
    // Reply button click handlers
    document.querySelectorAll('.reply-btn').forEach(button => {
        button.addEventListener('click', function() {
            const commentId = this.getAttribute('data-comment-id');
            const replyForm = document.getElementById('reply-form-' + commentId);
            
            // Hide all other reply forms
            document.querySelectorAll('.reply-form').forEach(form => {
                if (form.id !== 'reply-form-' + commentId) {
                    form.style.display = 'none';
                }
            });
            
            // Toggle current reply form
            if (replyForm.style.display === 'none') {
                replyForm.style.display = 'block';
                replyForm.querySelector('textarea').focus();
            } else {
                replyForm.style.display = 'none';
            }
        });
    });

    // Cancel reply button handlers
    document.querySelectorAll('.cancel-reply').forEach(button => {
        button.addEventListener('click', function() {
            this.closest('.reply-form').style.display = 'none';
        });
    });

    // Auto-resize textareas
    document.querySelectorAll('textarea').forEach(textarea => {
        textarea.addEventListener('input', function() {
            this.style.height = 'auto';
            this.style.height = this.scrollHeight + 'px';
        });
    });
});
</script>

<style>
.comment-content {
    white-space: pre-wrap;
    word-wrap: break-word;
    line-height: 1.6;
    padding-left: 0;
    margin-left: 0;
}

.reply-content {
    white-space: pre-wrap;
    word-wrap: break-word;
    line-height: 1.5;
    padding-left: 0;
    margin-left: 0;
}

textarea {
    resize: vertical;
    min-height: 100px;
}

.job-header-image img {
    border-radius: 0.375rem 0.375rem 0 0;
}

/* Facebook-like comment styling */
.comment-form {
    background: #f8f9fa;
    border-radius: 20px;
    padding: 15px;
    margin-bottom: 20px;
}

.comment-item {
    transition: all 0.3s ease;
    border: 1px solid #e9ecef;
}

.comment-item:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0,0,0,0.1);
    border-color: #dee2e6;
}

.hover-lift {
    transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.hover-lift:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0,0,0,0.15);
}

.avatar {
    flex-shrink: 0;
}

.reply-item {
    transition: all 0.3s ease;
    border-left: 3px solid #007bff !important;
}

.reply-item:hover {
    background-color: #f8f9fa !important;
}

.like-btn:hover {
    background-color: #e3f2fd;
    border-color: #2196f3;
    color: #2196f3;
}

.reply-btn:hover {
    background-color: #e8f5e8;
    border-color: #4caf50;
    color: #4caf50;
}

/* Gradient header */
.bg-gradient-primary {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

/* Modern form styling */
.form-control:focus {
    border-color: #667eea;
    box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
}

/* Animation for new comments */
@keyframes slideIn {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.comment-item {
    animation: slideIn 0.3s ease-out;
}
</style>

<?php include __DIR__ . '/includes/footer.php'; ?>
