<?php
require_once __DIR__ . '/config/config.php';

try {
    $database = new Database();
    $db = $database->getConnection();
    
    echo "<h2>Updating Database Structure...</h2>";
    
    // Add missing columns to jobseeker_profiles table
    $queries = [
        "ALTER TABLE jobseeker_profiles ADD COLUMN IF NOT EXISTS location VARCHAR(255) AFTER experience_years",
        "ALTER TABLE jobseeker_profiles ADD COLUMN IF NOT EXISTS expected_salary DECIMAL(10,2) AFTER location", 
        "ALTER TABLE jobseeker_profiles ADD COLUMN IF NOT EXISTS resume_file VARCHAR(255) AFTER expected_salary"
    ];
    
    foreach ($queries as $query) {
        try {
            $db->exec($query);
            echo "<p style='color: green;'>✓ " . htmlspecialchars($query) . "</p>";
        } catch (Exception $e) {
            echo "<p style='color: orange;'>⚠ " . htmlspecialchars($query) . " - " . $e->getMessage() . "</p>";
        }
    }
    
    // Update existing records with default values
    try {
        $db->exec("UPDATE jobseeker_profiles SET location = 'Midsayap, Cotabato' WHERE location IS NULL");
        echo "<p style='color: green;'>✓ Updated existing records with default location</p>";
    } catch (Exception $e) {
        echo "<p style='color: orange;'>⚠ Could not update location: " . $e->getMessage() . "</p>";
    }
    
    try {
        $db->exec("UPDATE jobseeker_profiles SET expected_salary = 20000.00 WHERE expected_salary IS NULL");
        echo "<p style='color: green;'>✓ Updated existing records with default salary</p>";
    } catch (Exception $e) {
        echo "<p style='color: orange;'>⚠ Could not update salary: " . $e->getMessage() . "</p>";
    }
    
    echo "<h3 style='color: green;'>Database update completed!</h3>";
    echo "<p><a href='/jobseeker/profile.php'>Go to Profile Page</a></p>";
    
} catch (Exception $e) {
    echo "<h3 style='color: red;'>Error: " . $e->getMessage() . "</h3>";
}
?> 