<?php
require_once __DIR__ . '/../config/config.php';
require_once __DIR__ . '/../lib/QualificationAnalyzer.php';

// Check if user is logged in and is jobseeker
if (!isLoggedIn() || getUserType() !== 'jobseeker') {
    redirect('/auth/login.php'); // Fixed redirect path
}

try {
    $database = new Database();
    $db = $database->getConnection();
    
    // Initialize qualification analyzer
    $qualificationAnalyzer = new QualificationAnalyzer($db);
    
    $user_id = $_SESSION['user_id'];
    
    // Initialize variables
    $profile = null;
    $applications = [];
    $recommended_jobs = [];
    $qualification_jobs = []; // New array for qualification-based recommendations
    $total_applications = 0;
    $pending_applications = 0;
    $accepted_applications = 0;
    $rejected_applications = 0;
    
    // Get jobseeker profile
    try {
        $query = "SELECT jp.*, u.first_name, u.last_name, u.email, u.phone 
                  FROM jobseeker_profiles jp 
                  JOIN users u ON jp.user_id = u.id 
                  WHERE jp.user_id = ?";
        $stmt = $db->prepare($query);
        $stmt->execute([$user_id]);
        $profile = $stmt->fetch(PDO::FETCH_ASSOC);
    } catch (Exception $e) {
        error_log("Profile query error: " . $e->getMessage());
        $profile = ['first_name' => $_SESSION['first_name'] ?? 'User', 'last_name' => $_SESSION['last_name'] ?? ''];
    }
    
    // Get job applications
    try {
        $query = "SELECT ja.*, jp.title, jp.location, jp.job_type, jp.salary_min, jp.salary_max, 
                         bp.company_name, bp.business_address 
                  FROM job_applications ja 
                  JOIN job_posts jp ON ja.job_id = jp.id 
                  JOIN business_profiles bp ON jp.business_id = bp.user_id 
                  WHERE ja.user_id = ? 
                  ORDER BY ja.created_at DESC";
        $stmt = $db->prepare($query);
        $stmt->execute([$user_id]);
        $applications = $stmt->fetchAll(PDO::FETCH_ASSOC);
    } catch (Exception $e) {
        error_log("Applications query error: " . $e->getMessage());
        $applications = [];
    }
    
    // Get qualification-based recommended jobs
    try {
        $query = "SELECT jp.*, bp.company_name, bp.business_address, bp.business_description
                  FROM job_posts jp 
                  JOIN business_profiles bp ON jp.business_id = bp.user_id 
                  WHERE jp.status = 'approved' 
                  AND jp.id NOT IN (SELECT job_id FROM job_applications WHERE user_id = ?)
                  ORDER BY jp.created_at DESC 
                  LIMIT 20";
        $stmt = $db->prepare($query);
        $stmt->execute([$user_id]);
        $all_jobs = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        // Calculate qualifications for each job
        foreach ($all_jobs as $job) {
            $qualification = $qualificationAnalyzer->calculateQualification($user_id, $job['id']);
            $job['qualification_percentage'] = $qualification;
            $qualification_jobs[] = $job;
        }
        
        // Sort by qualification percentage (highest first)
        usort($qualification_jobs, function($a, $b) {
            return $b['qualification_percentage'] <=> $a['qualification_percentage'];
        });
        
        // Get top 6 qualification-based recommendations
        $qualification_jobs = array_slice($qualification_jobs, 0, 6);
        
    } catch (Exception $e) {
        error_log("Qualification jobs query error: " . $e->getMessage());
        $qualification_jobs = [];
    }
    
    // Get skill-based recommended jobs (existing logic)
    try {
        if (!empty($profile['skills'])) {
            $query = "SELECT jp.*, bp.company_name, bp.business_address, bp.business_description
                      FROM job_posts jp 
                      JOIN business_profiles bp ON jp.business_id = bp.user_id 
                      WHERE jp.status = 'approved' 
                      AND jp.id NOT IN (SELECT job_id FROM job_applications WHERE user_id = ?)
                      ORDER BY jp.created_at DESC 
                      LIMIT 12";
            $stmt = $db->prepare($query);
            $stmt->execute([$user_id]);
            $all_jobs = $stmt->fetchAll(PDO::FETCH_ASSOC);
            
            // Calculate skill matches
            foreach ($all_jobs as $job) {
                $match_percentage = calculateSkillMatch($job['skills_required'], $profile['skills']);
                if ($match_percentage > 0) {
                    $job['match_percentage'] = $match_percentage;
                    $recommended_jobs[] = $job;
                }
            }
            
            // Sort by match percentage
            usort($recommended_jobs, function($a, $b) {
                return $b['match_percentage'] <=> $a['match_percentage'];
            });
            
            $recommended_jobs = array_slice($recommended_jobs, 0, 6);
        }
    } catch (Exception $e) {
        error_log("Recommended jobs query error: " . $e->getMessage());
        $recommended_jobs = [];
    }
    
    // Get statistics
    try {
        $query = "SELECT COUNT(*) as total FROM job_applications WHERE user_id = ?";
        $stmt = $db->prepare($query);
        $stmt->execute([$user_id]);
        $result = $stmt->fetch(PDO::FETCH_ASSOC);
        $total_applications = $result ? (int)$result['total'] : 0;
        
        $query = "SELECT COUNT(*) as total FROM job_applications WHERE user_id = ? AND status = 'pending'";
        $stmt = $db->prepare($query);
        $stmt->execute([$user_id]);
        $result = $stmt->fetch(PDO::FETCH_ASSOC);
        $pending_applications = $result ? (int)$result['total'] : 0;
        
        $query = "SELECT COUNT(*) as total FROM job_applications WHERE user_id = ? AND status = 'accepted'";
        $stmt = $db->prepare($query);
        $stmt->execute([$user_id]);
        $result = $stmt->fetch(PDO::FETCH_ASSOC);
        $accepted_applications = $result ? (int)$result['total'] : 0;
        
        $query = "SELECT COUNT(*) as total FROM job_applications WHERE user_id = ? AND status = 'rejected'";
        $stmt = $db->prepare($query);
        $stmt->execute([$user_id]);
        $result = $stmt->fetch(PDO::FETCH_ASSOC);
        $rejected_applications = $result ? (int)$result['total'] : 0;
    } catch (Exception $e) {
        error_log("Statistics query error: " . $e->getMessage());
        // Variables already initialized to 0
    }
    
} catch (Exception $e) {
    error_log("Jobseeker dashboard error: " . $e->getMessage());
    $error = "Database error occurred.";
}

$page_title = "Job Seeker Dashboard";
include __DIR__ . '/../includes/header.php';
?>

<main class="py-4">
    <div class="container-fluid">
        <!-- Success/Error Messages -->
        <?php if (isset($_SESSION['success'])): ?>
        <div class="alert alert-success alert-dismissible fade show" role="alert">
            <i class="fas fa-check-circle me-2"></i>
            <?php echo htmlspecialchars($_SESSION['success']); unset($_SESSION['success']); ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
        <?php endif; ?>

        <?php if (isset($_SESSION['error'])): ?>
        <div class="alert alert-danger alert-dismissible fade show" role="alert">
            <i class="fas fa-exclamation-circle me-2"></i>
            <?php echo htmlspecialchars($_SESSION['error']); unset($_SESSION['error']); ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
        <?php endif; ?>

        <!-- Dashboard Header -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h1 class="h3 mb-0">Job Seeker Dashboard</h1>
                        <p class="text-muted">Welcome back, <?php echo htmlspecialchars($profile ? ($profile['first_name'] . ' ' . $profile['last_name']) : 'Job Seeker'); ?>!</p>
                    </div>
                    <div class="d-flex gap-2">
                        <a href="../jobs.php" class="btn btn-success">
                            <i class="fas fa-search me-1"></i>Browse Jobs
                        </a>
                        <a href="profile.php" class="btn btn-primary">
                            <i class="fas fa-user-edit me-1"></i>Edit Profile
                        </a>
                    </div>
                </div>
            </div>
        </div>

        <!-- Statistics Cards -->
        <div class="row mb-4">
            <div class="col-lg-3 col-md-6 mb-3">
                <a href="applications.php" class="text-decoration-none">
                    <div class="card bg-gradient-primary text-white h-100 stat-card animate-in hover-lift">
                        <div class="card-body">
                            <div class="d-flex justify-content-between">
                                <div>
                                    <h4 class="mb-0"><?php echo $total_applications; ?></h4>
                                    <p class="mb-0">Total Applications</p>
                                </div>
                                <div class="align-self-center">
                                    <i class="fas fa-file-alt fa-2x opacity-75"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </a>
            </div>
            <div class="col-lg-3 col-md-6 mb-3">
                <a href="applications.php?status=pending" class="text-decoration-none">
                    <div class="card bg-gradient-warning text-white h-100 stat-card animate-in hover-lift">
                        <div class="card-body">
                            <div class="d-flex justify-content-between">
                                <div>
                                    <h4 class="mb-0"><?php echo $pending_applications; ?></h4>
                                    <p class="mb-0">Pending Review</p>
                                </div>
                                <div class="align-self-center">
                                    <i class="fas fa-clock fa-2x opacity-75"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </a>
            </div>
            <div class="col-lg-3 col-md-6 mb-3">
                <a href="applications.php?status=accepted" class="text-decoration-none">
                    <div class="card bg-gradient-success text-white h-100 stat-card animate-in hover-lift">
                        <div class="card-body">
                            <div class="d-flex justify-content-between">
                                <div>
                                    <h4 class="mb-0"><?php echo $accepted_applications; ?></h4>
                                    <p class="mb-0">Accepted</p>
                                </div>
                                <div class="align-self-center">
                                    <i class="fas fa-check-circle fa-2x opacity-75"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </a>
            </div>
            <div class="col-lg-3 col-md-6 mb-3">
                <a href="../jobs.php" class="text-decoration-none">
                    <div class="card bg-gradient-info text-white h-100 stat-card animate-in hover-lift">
                        <div class="card-body">
                            <div class="d-flex justify-content-between">
                                <div>
                                    <h4 class="mb-0"><?php echo count($recommended_jobs); ?></h4>
                                    <p class="mb-0">Recommended Jobs</p>
                                </div>
                                <div class="align-self-center">
                                    <i class="fas fa-star fa-2x opacity-75"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </a>
            </div>
        </div>

        <div class="row">
            <!-- Recent Applications -->
            <div class="col-lg-8 mb-4">
                <div class="card h-100">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <h5 class="mb-0">
                            <i class="fas fa-file-alt me-2"></i>Recent Applications
                        </h5>
                        <a href="applications.php" class="btn btn-sm btn-outline-primary">View All</a>
                    </div>
                    <div class="card-body">
                        <?php if (empty($applications)): ?>
                        <div class="text-center py-4">
                            <i class="fas fa-file-alt fa-3x text-muted mb-3"></i>
                            <p class="text-muted">No applications yet</p>
                            <a href="../jobs.php" class="btn btn-primary">
                                <i class="fas fa-search me-1"></i>Browse Jobs
                            </a>
                        </div>
                        <?php else: ?>
                        <div class="table-responsive">
                            <table class="table">
                                <thead>
                                    <tr>
                                        <th>Job Title</th>
                                        <th>Company</th>
                                        <th>Location</th>
                                        <th>Status</th>
                                        <th>Applied Date</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach (array_slice($applications, 0, 5) as $app): ?>
                                    <tr>
                                        <td>
                                            <strong><?php echo htmlspecialchars($app['title']); ?></strong><br>
                                            <small class="text-muted"><?php echo ucfirst(str_replace('-', ' ', $app['job_type'])); ?></small>
                                        </td>
                                        <td>
                                            <strong><?php echo htmlspecialchars($app['company_name']); ?></strong>
                                            <?php if (!empty($app['business_address'])): ?>
                                            <br><small class="text-muted"><?php echo htmlspecialchars($app['business_address']); ?></small>
                                            <?php endif; ?>
                                        </td>
                                        <td><?php echo htmlspecialchars($app['location']); ?></td>
                                        <td>
                                            <?php
                                            $status = isset($app['status']) ? $app['status'] : 'pending';
                                            $status_class = '';
                                            switch ($status) {
                                                case 'pending':
                                                    $status_class = 'bg-warning';
                                                    break;
                                                case 'accepted':
                                                    $status_class = 'bg-success';
                                                    break;
                                                case 'rejected':
                                                    $status_class = 'bg-danger';
                                                    break;
                                                default:
                                                    $status_class = 'bg-secondary';
                                                    break;
                                            }
                                            ?>
                                            <span class="badge <?php echo $status_class; ?>">
                                                <?php echo ucfirst($status); ?>
                                            </span>
                                        </td>
                                        <td>
                                            <small><?php echo date('M j, Y', strtotime($app['created_at'])); ?></small>
                                        </td>
                                        <td>
                                            <div class="btn-group btn-group-sm">
                                                <a href="../job-details.php?id=<?php echo $app['job_id']; ?>" class="btn btn-outline-primary" title="View Job">
                                                    <i class="fas fa-eye"></i>
                                                </a>
                                                <?php if ($status === 'accepted'): ?>
                                                <a href="application-details.php?id=<?php echo $app['id']; ?>" class="btn btn-outline-success" title="View Details">
                                                    <i class="fas fa-check"></i>
                                                </a>
                                                <?php endif; ?>
                                            </div>
                                        </td>
                                    </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>

            <!-- Profile Summary -->
            <div class="col-lg-4 mb-4">
                <div class="card h-100">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="fas fa-user me-2"></i>Profile Summary
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="text-center mb-3">
                            <i class="fas fa-user-circle fa-4x text-primary mb-2"></i>
                            <h6><?php echo htmlspecialchars($profile ? ($profile['first_name'] . ' ' . $profile['last_name']) : 'User'); ?></h6>
                            <small class="text-muted"><?php echo htmlspecialchars($profile['email'] ?? $_SESSION['email'] ?? ''); ?></small>
                        </div>
                        
                        <?php if (!empty($profile['phone'])): ?>
                        <div class="mb-3">
                            <strong><i class="fas fa-phone me-1"></i>Phone:</strong><br>
                            <small><?php echo htmlspecialchars($profile['phone']); ?></small>
                        </div>
                        <?php endif; ?>
                        
                        <?php if (!empty($profile['education_level'])): ?>
                        <div class="mb-3">
                            <strong><i class="fas fa-graduation-cap me-1"></i>Education:</strong><br>
                            <small><?php echo htmlspecialchars($profile['education_level']); ?></small>
                        </div>
                        <?php endif; ?>
                        
                        <?php if (!empty($profile['degree_course'])): ?>
                        <div class="mb-3">
                            <strong><i class="fas fa-book me-1"></i>Course:</strong><br>
                            <small><?php echo htmlspecialchars($profile['degree_course']); ?></small>
                        </div>
                        <?php endif; ?>
                        
                        <?php if (!empty($profile['skills'])): ?>
                        <div class="mb-3">
                            <strong><i class="fas fa-tools me-1"></i>Skills:</strong><br>
                            <?php
                            $skills = explode(',', $profile['skills']);
                            foreach (array_slice($skills, 0, 5) as $skill):
                            ?>
                            <span class="badge bg-secondary me-1 mb-1"><?php echo htmlspecialchars(trim($skill)); ?></span>
                            <?php endforeach; ?>
                            <?php if (count($skills) > 5): ?>
                            <small class="text-muted">+<?php echo count($skills) - 5; ?> more</small>
                            <?php endif; ?>
                        </div>
                        <?php endif; ?>
                        
                        <div class="d-grid gap-2">
                            <a href="profile.php" class="btn btn-primary">
                                <i class="fas fa-edit me-1"></i>Update Profile
                            </a>
                            <a href="../jobs.php" class="btn btn-outline-success">
                                <i class="fas fa-search me-1"></i>Find More Jobs
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Recommended Jobs -->
        <?php if (!empty($recommended_jobs)): ?>
        <div class="row">
            <div class="col-12">
                <div class="card">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <h5 class="mb-0">
                            <i class="fas fa-star me-2"></i>Recommended Jobs for You
                        </h5>
                        <a href="../jobs.php" class="btn btn-sm btn-outline-primary">Browse All Jobs</a>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <?php foreach ($recommended_jobs as $job): ?>
                            <div class="col-lg-4 col-md-6 mb-3">
                                <div class="card job-card h-100 border-0 shadow-sm hover-lift">
                                    <div class="card-body">
                                        <div class="d-flex justify-content-between align-items-start mb-2">
                                            <h6 class="card-title mb-0">
                                                <a href="../job-details.php?id=<?php echo $job['id']; ?>" class="text-decoration-none">
                                                    <?php echo htmlspecialchars($job['title']); ?>
                                                </a>
                                            </h6>
                                            <span class="badge bg-success"><?php echo $job['match_percentage']; ?>% match</span>
                                        </div>
                                        <p class="text-primary mb-1">
                                            <i class="fas fa-building me-1"></i>
                                            <strong><?php echo htmlspecialchars($job['company_name']); ?></strong>
                                        </p>
                                        <p class="text-muted small mb-2">
                                            <i class="fas fa-map-marker-alt me-1"></i>
                                            <?php echo htmlspecialchars($job['location']); ?>
                                        </p>
                                        <?php if ($job['salary_min'] && $job['salary_max']): ?>
                                        <p class="text-success small mb-2">
                                            <i class="fas fa-money-bill-wave me-1"></i>
                                            ₱<?php echo number_format($job['salary_min']); ?> - ₱<?php echo number_format($job['salary_max']); ?>
                                        </p>
                                        <?php endif; ?>
                                        <div class="d-flex justify-content-between align-items-center">
                                            <small class="text-muted">
                                                <i class="fas fa-calendar me-1"></i>
                                                <?php echo date('M j, Y', strtotime($job['created_at'])); ?>
                                            </small>
                                            <a href="../job-details.php?id=<?php echo $job['id']; ?>" class="btn btn-sm btn-primary">
                                                <i class="fas fa-eye me-1"></i>View Details
                                            </a>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <?php endforeach; ?>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <?php endif; ?>
    </div>
</main>

<?php include __DIR__ . '/../includes/footer.php'; ?>
