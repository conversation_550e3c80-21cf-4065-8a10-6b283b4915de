<?php
require_once __DIR__ . '/../config/config.php';

// Check if user is logged in and is admin
if (!isLoggedIn() || getUserType() !== 'admin') {
    http_response_code(403);
    echo json_encode(['success' => false, 'message' => 'Unauthorized access']);
    exit();
}

try {
    $database = new Database();
    $db = $database->getConnection();
    
    $user_id = (int)($_GET['id'] ?? 0);
    
    if ($user_id <= 0) {
        echo json_encode(['success' => false, 'message' => 'Invalid user ID']);
        exit();
    }
    
    // Get user details with profile information
    $query = "SELECT u.*, 
                     bp.company_name, bp.business_description, bp.business_address, bp.mayors_permit, bp.dti_number,
                     jp.education_level, jp.degree_course, jp.skills, jp.experience_years, jp.location, jp.expected_salary, jp.resume_file
              FROM users u 
              LEFT JOIN business_profiles bp ON u.id = bp.user_id 
              LEFT JOIN jobseeker_profiles jp ON u.id = jp.user_id 
              WHERE u.id = ?";
    
    $stmt = $db->prepare($query);
    $stmt->execute([$user_id]);
    $user = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if (!$user) {
        echo json_encode(['success' => false, 'message' => 'User not found']);
        exit();
    }
    
    // Organize profile data based on user type
    $profile = null;
    if ($user['user_type'] === 'business') {
        $profile = [
            'company_name' => $user['company_name'],
            'business_description' => $user['business_description'],
            'business_address' => $user['business_address'],
            'mayors_permit' => $user['mayors_permit'],
            'dti_number' => $user['dti_number']
        ];
    } elseif ($user['user_type'] === 'jobseeker') {
        $profile = [
            'education_level' => $user['education_level'],
            'degree_course' => $user['degree_course'],
            'skills' => $user['skills'],
            'experience_years' => $user['experience_years'],
            'location' => $user['location'],
            'expected_salary' => $user['expected_salary'],
            'resume_file' => $user['resume_file']
        ];
    }
    
    // Remove profile fields from main user array to avoid duplication
    $user_data = [
        'id' => $user['id'],
        'first_name' => $user['first_name'],
        'last_name' => $user['last_name'],
        'email' => $user['email'],
        'phone' => $user['phone'],
        'user_type' => $user['user_type'],
        'is_verified' => (bool)$user['is_verified'],
        'created_at' => $user['created_at'],
        'updated_at' => $user['updated_at'],
        'profile' => $profile
    ];
    
    echo json_encode([
        'success' => true,
        'user' => $user_data
    ]);
    
} catch (Exception $e) {
    error_log("User details error: " . $e->getMessage());
    echo json_encode(['success' => false, 'message' => 'Database error occurred']);
}
?> 