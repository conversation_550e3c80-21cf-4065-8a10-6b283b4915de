<?php
require_once __DIR__ . '/config/config.php';

try {
    $database = new Database();
    $db = $database->getConnection();
    
    // Get latest approved job posts
    $query = "SELECT jp.*, bp.company_name, bp.business_address 
              FROM job_posts jp 
              JOIN business_profiles bp ON jp.business_id = bp.user_id 
              WHERE jp.status = 'approved' 
              ORDER BY jp.created_at DESC 
              LIMIT 6";
    $stmt = $db->prepare($query);
    $stmt->execute();
    $featured_jobs = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    // Get job statistics
    $query = "SELECT COUNT(*) as total_jobs FROM job_posts WHERE status = 'approved'";
    $stmt = $db->prepare($query);
    $stmt->execute();
    $total_jobs = $stmt->fetch(PDO::FETCH_ASSOC)['total_jobs'];
    
    $query = "SELECT COUNT(*) as total_companies FROM business_profiles bp JOIN users u ON bp.user_id = u.id WHERE u.is_verified = 1";
    $stmt = $db->prepare($query);
    $stmt->execute();
    $total_companies = $stmt->fetch(PDO::FETCH_ASSOC)['total_companies'];
    
    $query = "SELECT COUNT(*) as total_jobseekers FROM users WHERE user_type = 'jobseeker'";
    $stmt = $db->prepare($query);
    $stmt->execute();
    $total_jobseekers = $stmt->fetch(PDO::FETCH_ASSOC)['total_jobseekers'];
    
} catch (Exception $e) {
    error_log("Homepage error: " . $e->getMessage());
    $featured_jobs = [];
    $total_jobs = 0;
    $total_companies = 0;
    $total_jobseekers = 0;
}

$page_title = "Home";
include __DIR__ . '/includes/header.php';
?>

<main>
    <!-- Logout Success Message -->
    <?php if (isset($_GET['logout']) && $_GET['logout'] === 'success'): ?>
    <div class="container mt-4">
        <div class="alert alert-success alert-dismissible fade show" role="alert">
            <i class="fas fa-check-circle me-2"></i>
            You have been successfully logged out.
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    </div>
    <?php endif; ?>

    <!-- Hero Section -->
    <section class="hero-section">
        <div class="container">
            <div class="row align-items-center min-vh-100">
                <div class="col-lg-6">
                    <div class="hero-content fade-in">
                        <h1 class="display-4 fw-bold text-white mb-4">
                            Find Your Dream Job in <span class="text-warning">Midsayap</span>
                        </h1>
                        <p class="lead text-white-50 mb-4">
                            Connect with top employers and discover exciting career opportunities in Midsayap, North Cotabato. Your next career move starts here.
                        </p>
                        <div class="d-flex flex-wrap gap-3">
                            <a href="jobs.php" class="btn btn-warning btn-lg px-4 py-3">
                                <i class="fas fa-search me-2"></i>Browse Jobs
                            </a>
                            <?php if (!isLoggedIn()): ?>
                            <a href="auth/register.php" class="btn btn-outline-light btn-lg px-4 py-3">
                                <i class="fas fa-user-plus me-2"></i>Join Now
                            </a>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
                <div class="col-lg-6">
                    <div class="hero-stats fade-in">
                        <div class="row g-4">
                            <div class="col-md-4">
                                <div class="stat-card text-center">
                                    <div class="stat-number text-warning"><?php echo number_format($total_jobs); ?></div>
                                    <div class="stat-label text-white-50">Active Jobs</div>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="stat-card text-center">
                                    <div class="stat-number text-warning"><?php echo number_format($total_companies); ?></div>
                                    <div class="stat-label text-white-50">Companies</div>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="stat-card text-center">
                                    <div class="stat-number text-warning"><?php echo number_format($total_jobseekers); ?></div>
                                    <div class="stat-label text-white-50">Job Seekers</div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Featured Jobs Section -->
    <section class="py-5 bg-light">
        <div class="container">
            <div class="row">
                <div class="col-12">
                    <div class="text-center mb-5">
                        <h2 class="display-5 fw-bold mb-3">Latest Job Opportunities</h2>
                        <p class="lead text-muted">Discover the newest job openings from top employers in Midsayap</p>
                    </div>
                </div>
            </div>
            
            <?php if (!empty($featured_jobs)): ?>
            <div class="row g-4">
                <?php foreach ($featured_jobs as $job): ?>
                <div class="col-lg-4 col-md-6">
                    <div class="job-card card h-100 shadow-sm border-0">
                        <?php if (!empty($job['job_image'])): ?>
                        <div class="job-image-container">
                            <img src="uploads/job_images/<?php echo htmlspecialchars($job['job_image']); ?>" 
                                 class="card-img-top job-image" alt="<?php echo htmlspecialchars($job['title']); ?>">
                        </div>
                        <?php endif; ?>
                        <div class="card-body d-flex flex-column">
                            <div class="mb-3">
                                <h5 class="card-title mb-2">
                                    <a href="job-details.php?id=<?php echo $job['id']; ?>" class="text-decoration-none text-dark">
                                        <?php echo htmlspecialchars($job['title']); ?>
                                    </a>
                                </h5>
                                <p class="text-primary mb-1">
                                    <i class="fas fa-building me-1"></i>
                                    <?php echo htmlspecialchars($job['company_name']); ?>
                                </p>
                                <p class="text-muted small mb-2">
                                    <i class="fas fa-map-marker-alt me-1"></i>
                                    <?php echo htmlspecialchars($job['location']); ?>
                                </p>
                            </div>
                            
                            <p class="card-text text-muted small mb-3">
                                <?php echo substr(htmlspecialchars($job['description']), 0, 120) . '...'; ?>
                            </p>
                            
                            <div class="mt-auto">
                                <div class="d-flex justify-content-between align-items-center mb-3">
                                    <span class="badge bg-info"><?php echo ucfirst($job['job_type']); ?></span>
                                    <?php if ($job['salary_min'] && $job['salary_max']): ?>
                                    <span class="text-success fw-bold small">
                                        ₱<?php echo number_format($job['salary_min']); ?> - ₱<?php echo number_format($job['salary_max']); ?>
                                    </span>
                                    <?php endif; ?>
                                </div>
                                
                                <div class="d-flex justify-content-between align-items-center">
                                    <small class="text-muted">
                                        <i class="fas fa-clock me-1"></i>
                                        <?php echo date('M j, Y', strtotime($job['created_at'])); ?>
                                    </small>
                                    <a href="job-details.php?id=<?php echo $job['id']; ?>" class="btn btn-primary btn-sm">
                                        View Details
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <?php endforeach; ?>
            </div>
            
            <div class="text-center mt-5">
                <a href="jobs.php" class="btn btn-primary btn-lg">
                    <i class="fas fa-briefcase me-2"></i>View All Jobs
                </a>
            </div>
            <?php else: ?>
            <div class="text-center py-5">
                <i class="fas fa-briefcase fa-4x text-muted mb-4"></i>
                <h4>No Jobs Available</h4>
                <p class="text-muted">Check back later for new job opportunities!</p>
            </div>
            <?php endif; ?>
        </div>
    </section>

    <!-- About Tan-Aw Section -->
    <section class="py-5">
        <div class="container">
            <div class="row align-items-center">
                <div class="col-lg-6">
                    <div class="about-content">
                        <h2 class="display-5 fw-bold mb-4">About Tan-Aw</h2>
                        <p class="lead mb-4">
                            Tan-Aw is Midsayap's premier job portal, connecting talented individuals with leading employers in North Cotabato.
                        </p>
                        <div class="row g-4 mb-4">
                            <div class="col-md-6">
                                <div class="feature-item">
                                    <div class="feature-icon bg-primary text-white rounded-circle d-inline-flex align-items-center justify-content-center mb-3">
                                        <i class="fas fa-users fa-lg"></i>
                                    </div>
                                    <h5>Local Focus</h5>
                                    <p class="text-muted">Dedicated to serving the Midsayap community and surrounding areas.</p>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="feature-item">
                                    <div class="feature-icon bg-success text-white rounded-circle d-inline-flex align-items-center justify-content-center mb-3">
                                        <i class="fas fa-handshake fa-lg"></i>
                                    </div>
                                    <h5>Trusted Platform</h5>
                                    <p class="text-muted">Verified employers and secure application processes for your peace of mind.</p>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="feature-item">
                                    <div class="feature-icon bg-info text-white rounded-circle d-inline-flex align-items-center justify-content-center mb-3">
                                        <i class="fas fa-rocket fa-lg"></i>
                                    </div>
                                    <h5>Career Growth</h5>
                                    <p class="text-muted">Opportunities for professional development and career advancement.</p>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="feature-item">
                                    <div class="feature-icon bg-warning text-white rounded-circle d-inline-flex align-items-center justify-content-center mb-3">
                                        <i class="fas fa-clock fa-lg"></i>
                                    </div>
                                    <h5>Quick Matching</h5>
                                    <p class="text-muted">Advanced algorithms to match you with the perfect job opportunities.</p>
                                </div>
                            </div>
                        </div>
                        <a href="about.php" class="btn btn-outline-primary btn-lg">
                            <i class="fas fa-info-circle me-2"></i>Learn More
                        </a>
                    </div>
                </div>
                <div class="col-lg-6">
                    <div class="about-image text-center">
                        <img src="/placeholder.svg?height=400&width=500" 
                             alt="Tan-Aw Office" class="img-fluid rounded shadow">
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Call to Action Section -->
    <section class="py-5 bg-gradient-primary text-white">
        <div class="container">
            <div class="row">
                <div class="col-lg-8 mx-auto text-center">
                    <h2 class="display-5 fw-bold mb-4">Ready to Start Your Career Journey?</h2>
                    <p class="lead mb-4">
                        Join thousands of job seekers and employers who trust Tan-Aw for their career needs.
                    </p>
                    <div class="d-flex flex-wrap justify-content-center gap-3">
                        <?php if (!isLoggedIn()): ?>
                        <a href="auth/register.php?type=jobseeker" class="btn btn-warning btn-lg px-4 py-3">
                            <i class="fas fa-user me-2"></i>Find Jobs
                        </a>
                        <a href="auth/register.php?type=business" class="btn btn-outline-light btn-lg px-4 py-3">
                            <i class="fas fa-building me-2"></i>Post Jobs
                        </a>
                        <?php else: ?>
                        <a href="<?php echo getUserType(); ?>/dashboard.php" class="btn btn-warning btn-lg px-4 py-3">
                            <i class="fas fa-tachometer-alt me-2"></i>Go to Dashboard
                        </a>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>
    </section>
</main>

<style>
.job-image-container {
    height: 200px;
    overflow: hidden;
}

.job-image {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.3s ease;
}

.job-card:hover .job-image {
    transform: scale(1.05);
}

.feature-icon {
    width: 60px;
    height: 60px;
}

.stat-card {
    padding: 1.5rem;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 15px;
    backdrop-filter: blur(10px);
}

.stat-number {
    font-size: 2.5rem;
    font-weight: bold;
}

.stat-label {
    font-size: 0.9rem;
    text-transform: uppercase;
    letter-spacing: 1px;
}
</style>

<?php include __DIR__ . '/includes/footer.php'; ?>
