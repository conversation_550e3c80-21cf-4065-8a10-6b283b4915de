-- Job Qualification Analysis System
-- This system automatically calculates job qualification percentages based on educational background

-- Create qualification matrix table
CREATE TABLE IF NOT EXISTS qualification_matrix (
    id INT AUTO_INCREMENT PRIMARY KEY,
    course_category VARCHAR(100) NOT NULL,
    job_category VARCHAR(100) NOT NULL,
    qualification_percentage INT NOT NULL CHECK (qualification_percentage >= 0 AND qualification_percentage <= 100),
    reasoning TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    UNIQUE KEY unique_course_job (course_category, job_category)
);

-- Create job categories table
CREATE TABLE IF NOT EXISTS job_categories (
    id INT AUTO_INCREMENT PRIMARY KEY,
    category_name VARCHAR(100) NOT NULL UNIQUE,
    description TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Create course categories table
CREATE TABLE IF NOT EXISTS course_categories (
    id INT AUTO_INCREMENT PRIMARY KEY,
    category_name VARCHAR(100) NOT NULL UNIQUE,
    description TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Create qualification cache table for performance
CREATE TABLE IF NOT EXISTS qualification_cache (
    id INT AUTO_INCREMENT PRIMARY KEY,
    jobseeker_id INT NOT NULL,
    job_id INT NOT NULL,
    qualification_percentage INT NOT NULL,
    calculation_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (jobseeker_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (job_id) REFERENCES job_posts(id) ON DELETE CASCADE,
    UNIQUE KEY unique_jobseeker_job (jobseeker_id, job_id)
);

-- Insert course categories
INSERT IGNORE INTO course_categories (category_name, description) VALUES
('Information Technology', 'Computer Science, IT, Software Engineering, etc.'),
('Business & Management', 'Business Administration, Management, Marketing, etc.'),
('Engineering', 'Civil, Electrical, Mechanical, Computer Engineering, etc.'),
('Education', 'Teaching degrees, Education Management, etc.'),
('Healthcare', 'Nursing, Medical Technology, Pharmacy, etc.'),
('Arts & Humanities', 'Literature, History, Philosophy, etc.'),
('Science', 'Biology, Chemistry, Physics, Mathematics, etc.'),
('Vocational', 'Technical courses, Trade skills, etc.'),
('High School', 'High school graduates'),
('Other', 'Other educational backgrounds');

-- Insert job categories
INSERT IGNORE INTO job_categories (category_name, description) VALUES
('Information Technology', 'Software development, IT support, system administration'),
('Business & Finance', 'Accounting, finance, business management, administration'),
('Sales & Marketing', 'Sales, marketing, customer service, retail'),
('Engineering', 'Civil, electrical, mechanical, software engineering'),
('Education', 'Teaching, training, educational administration'),
('Healthcare', 'Nursing, medical technology, healthcare administration'),
('Manufacturing', 'Production, quality control, industrial work'),
('Service Industry', 'Food service, hospitality, customer service'),
('Skilled Trades', 'Construction, automotive, electrical work'),
('Administrative', 'Office work, data entry, clerical positions'),
('Creative & Design', 'Graphic design, content creation, multimedia'),
('Other', 'Other job categories');

-- Insert qualification matrix (course category -> job category -> percentage)
INSERT IGNORE INTO qualification_matrix (course_category, job_category, qualification_percentage, reasoning) VALUES
-- Information Technology graduates
('Information Technology', 'Information Technology', 100, 'Perfect match - direct field alignment'),
('Information Technology', 'Business & Finance', 70, 'IT skills valuable for business systems and data analysis'),
('Information Technology', 'Sales & Marketing', 60, 'Technical knowledge helps in tech product sales'),
('Information Technology', 'Engineering', 80, 'Strong technical foundation applicable to engineering'),
('Information Technology', 'Education', 65, 'Can teach IT subjects and use technology in education'),
('Information Technology', 'Healthcare', 50, 'IT skills useful for healthcare systems and data management'),
('Information Technology', 'Manufacturing', 55, 'Technical skills applicable to manufacturing systems'),
('Information Technology', 'Service Industry', 45, 'Basic computer literacy and problem-solving skills'),
('Information Technology', 'Skilled Trades', 40, 'Some technical skills transferable'),
('Information Technology', 'Administrative', 75, 'Strong computer and software skills'),
('Information Technology', 'Creative & Design', 70, 'Technical skills valuable for digital design'),
('Information Technology', 'Other', 50, 'General technical and problem-solving skills'),

-- Business & Management graduates
('Business & Management', 'Information Technology', 40, 'Business process knowledge valuable for IT projects'),
('Business & Management', 'Business & Finance', 100, 'Perfect match - direct field alignment'),
('Business & Management', 'Sales & Marketing', 90, 'Strong business foundation for sales and marketing'),
('Business & Management', 'Engineering', 30, 'Limited technical background for engineering roles'),
('Business & Management', 'Education', 70, 'Business knowledge valuable for business education'),
('Business & Management', 'Healthcare', 60, 'Business skills applicable to healthcare administration'),
('Business & Management', 'Manufacturing', 65, 'Business process and management skills valuable'),
('Business & Management', 'Service Industry', 80, 'Strong customer service and management skills'),
('Business & Management', 'Skilled Trades', 25, 'Limited technical skills for trade work'),
('Business & Management', 'Administrative', 85, 'Strong administrative and organizational skills'),
('Business & Management', 'Creative & Design', 50, 'Business knowledge helps with project management'),
('Business & Management', 'Other', 60, 'General business and management skills'),

-- Engineering graduates
('Engineering', 'Information Technology', 85, 'Strong technical and problem-solving skills'),
('Engineering', 'Business & Finance', 50, 'Analytical skills valuable for business analysis'),
('Engineering', 'Sales & Marketing', 40, 'Technical knowledge helps in technical product sales'),
('Engineering', 'Engineering', 100, 'Perfect match - direct field alignment'),
('Engineering', 'Education', 60, 'Can teach engineering subjects and technical courses'),
('Engineering', 'Healthcare', 45, 'Some technical skills applicable to medical technology'),
('Engineering', 'Manufacturing', 90, 'Strong technical skills for manufacturing processes'),
('Engineering', 'Service Industry', 35, 'Limited customer service background'),
('Engineering', 'Skilled Trades', 70, 'Technical skills transferable to trade work'),
('Engineering', 'Administrative', 55, 'Analytical and organizational skills'),
('Engineering', 'Creative & Design', 45, 'Some technical skills applicable to design'),
('Engineering', 'Other', 50, 'Strong analytical and problem-solving skills'),

-- Education graduates
('Education', 'Information Technology', 30, 'Limited technical background'),
('Education', 'Business & Finance', 50, 'Communication and organizational skills valuable'),
('Education', 'Sales & Marketing', 70, 'Strong communication and presentation skills'),
('Education', 'Engineering', 25, 'Limited technical background'),
('Education', 'Education', 100, 'Perfect match - direct field alignment'),
('Education', 'Healthcare', 60, 'Communication skills valuable for patient education'),
('Education', 'Manufacturing', 40, 'Limited technical background'),
('Education', 'Service Industry', 75, 'Strong communication and customer service skills'),
('Education', 'Skilled Trades', 30, 'Limited technical background'),
('Education', 'Administrative', 80, 'Strong organizational and communication skills'),
('Education', 'Creative & Design', 60, 'Communication skills valuable for content creation'),
('Education', 'Other', 55, 'Strong communication and organizational skills'),

-- Healthcare graduates
('Healthcare', 'Information Technology', 35, 'Limited technical background'),
('Healthcare', 'Business & Finance', 45, 'Some organizational skills'),
('Healthcare', 'Sales & Marketing', 60, 'Communication skills valuable for healthcare sales'),
('Healthcare', 'Engineering', 25, 'Limited technical background'),
('Healthcare', 'Education', 70, 'Can teach healthcare subjects and patient education'),
('Healthcare', 'Healthcare', 100, 'Perfect match - direct field alignment'),
('Healthcare', 'Manufacturing', 40, 'Limited technical background'),
('Healthcare', 'Service Industry', 65, 'Strong patient care and communication skills'),
('Healthcare', 'Skilled Trades', 25, 'Limited technical background'),
('Healthcare', 'Administrative', 60, 'Organizational skills from healthcare work'),
('Healthcare', 'Creative & Design', 40, 'Limited creative background'),
('Healthcare', 'Other', 50, 'Strong care and communication skills'),

-- Arts & Humanities graduates
('Arts & Humanities', 'Information Technology', 25, 'Limited technical background'),
('Arts & Humanities', 'Business & Finance', 45, 'Some analytical and communication skills'),
('Arts & Humanities', 'Sales & Marketing', 75, 'Strong communication and creative skills'),
('Arts & Humanities', 'Engineering', 20, 'Limited technical background'),
('Arts & Humanities', 'Education', 85, 'Strong communication and teaching skills'),
('Arts & Humanities', 'Healthcare', 50, 'Communication skills valuable'),
('Arts & Humanities', 'Manufacturing', 30, 'Limited technical background'),
('Arts & Humanities', 'Service Industry', 70, 'Strong communication and customer service skills'),
('Arts & Humanities', 'Skilled Trades', 25, 'Limited technical background'),
('Arts & Humanities', 'Administrative', 65, 'Strong communication and organizational skills'),
('Arts & Humanities', 'Creative & Design', 90, 'Strong creative and artistic skills'),
('Arts & Humanities', 'Other', 55, 'Strong communication and creative skills'),

-- Science graduates
('Science', 'Information Technology', 70, 'Strong analytical and technical skills'),
('Science', 'Business & Finance', 55, 'Analytical skills valuable for business analysis'),
('Science', 'Sales & Marketing', 50, 'Some communication skills'),
('Science', 'Engineering', 75, 'Strong technical and analytical skills'),
('Science', 'Education', 80, 'Can teach science subjects effectively'),
('Science', 'Healthcare', 85, 'Strong scientific background for healthcare'),
('Science', 'Manufacturing', 60, 'Technical and analytical skills valuable'),
('Science', 'Service Industry', 45, 'Limited customer service background'),
('Science', 'Skilled Trades', 40, 'Some technical skills'),
('Science', 'Administrative', 60, 'Analytical and organizational skills'),
('Science', 'Creative & Design', 50, 'Some analytical skills applicable'),
('Science', 'Other', 55, 'Strong analytical and problem-solving skills'),

-- Vocational graduates
('Vocational', 'Information Technology', 60, 'Technical skills from vocational training'),
('Vocational', 'Business & Finance', 40, 'Some organizational skills'),
('Vocational', 'Sales & Marketing', 55, 'Some communication skills'),
('Vocational', 'Engineering', 50, 'Some technical skills from vocational training'),
('Vocational', 'Education', 45, 'Can teach vocational subjects'),
('Vocational', 'Healthcare', 50, 'Some technical skills applicable'),
('Vocational', 'Manufacturing', 75, 'Strong technical skills from vocational training'),
('Vocational', 'Service Industry', 60, 'Practical skills valuable for service work'),
('Vocational', 'Skilled Trades', 85, 'Strong technical skills from vocational training'),
('Vocational', 'Administrative', 50, 'Some organizational skills'),
('Vocational', 'Creative & Design', 45, 'Some technical skills applicable'),
('Vocational', 'Other', 55, 'Practical skills from vocational training'),

-- High School graduates
('High School', 'Information Technology', 30, 'Basic computer literacy'),
('High School', 'Business & Finance', 35, 'Basic organizational skills'),
('High School', 'Sales & Marketing', 50, 'Basic communication skills'),
('High School', 'Engineering', 20, 'Limited technical background'),
('High School', 'Education', 40, 'Basic communication skills'),
('High School', 'Healthcare', 35, 'Basic care skills'),
('High School', 'Manufacturing', 45, 'Basic technical skills'),
('High School', 'Service Industry', 55, 'Basic customer service skills'),
('High School', 'Skilled Trades', 40, 'Basic technical skills'),
('High School', 'Administrative', 45, 'Basic organizational skills'),
('High School', 'Creative & Design', 40, 'Basic creative skills'),
('High School', 'Other', 40, 'Basic general skills'),

-- Other graduates
('Other', 'Information Technology', 40, 'General technical skills'),
('Other', 'Business & Finance', 45, 'General business skills'),
('Other', 'Sales & Marketing', 50, 'General communication skills'),
('Other', 'Engineering', 30, 'Limited technical background'),
('Other', 'Education', 50, 'General communication skills'),
('Other', 'Healthcare', 40, 'General care skills'),
('Other', 'Manufacturing', 45, 'General technical skills'),
('Other', 'Service Industry', 50, 'General service skills'),
('Other', 'Skilled Trades', 35, 'Limited technical skills'),
('Other', 'Administrative', 50, 'General organizational skills'),
('Other', 'Creative & Design', 45, 'General creative skills'),
('Other', 'Other', 50, 'General skills applicable to various fields');

-- Add indexes for better performance
CREATE INDEX IF NOT EXISTS idx_qualification_matrix_course ON qualification_matrix(course_category);
CREATE INDEX IF NOT EXISTS idx_qualification_matrix_job ON qualification_matrix(job_category);
CREATE INDEX IF NOT EXISTS idx_qualification_cache_jobseeker ON qualification_cache(jobseeker_id);
CREATE INDEX IF NOT EXISTS idx_qualification_cache_job ON qualification_cache(job_id);
CREATE INDEX IF NOT EXISTS idx_qualification_cache_date ON qualification_cache(calculation_date); 