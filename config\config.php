<?php
// Start session if not already started
if (session_status() == PHP_SESSION_NONE) {
    session_start();
}

// Development mode
define('DEVELOPMENT_MODE', false);

// Database configuration - Updated for better compatibility across different environments
define('DB_HOST', 'localhost');
define('DB_NAME', 'tan-aw-job-portal');
define('DB_USER', 'root');
define('DB_PASS', '');

// Alternative database configurations for different environments
// You can uncomment and modify these based on your setup
// define('DB_HOST', '127.0.0.1'); // Alternative to localhost
// define('DB_NAME', 'tan_aw_job_portal'); // Alternative database name
// define('DB_USER', 'your_username'); // Your MySQL username
// define('DB_PASS', 'your_password'); // Your MySQL password

// Site configuration
$protocol = isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on' ? 'https' : 'http';
$host = $_SERVER['HTTP_HOST'] ?? 'localhost';

// Get the project root path by finding the position of 'tan-aw-job-portal' in the script name
$script_name = $_SERVER['SCRIPT_NAME'] ?? '';
$project_name = 'tan-aw-job-portal';

// Find the project root path
if (strpos($script_name, $project_name) !== false) {
    $path = '/' . $project_name;
} else {
    // Fallback: use current directory path
    $path = dirname($script_name);
    if ($path !== '/') {
        $path = rtrim($path, '/');
    }
}

define('SITE_URL', $protocol . '://' . $host . $path);

// Upload paths
define('UPLOAD_PATH', __DIR__ . '/../uploads/');
define('UPLOAD_URL', SITE_URL . '/uploads/');

// Include database class
require_once __DIR__ . '/database.php';

// Auto-setup system removed - database should be manually configured
// if (!class_exists('AutoDatabaseSetup')) {
//     require_once __DIR__ . '/auto-database-setup.php';
// }

// Auto-setup database if needed (only runs once per session and only if not in auto-setup mode)
// if (!isset($_GET['step']) && !isset($_POST['auto_setup'])) {
//     autoSetupDatabase();
// }

// Utility functions
function sanitize($data) {
    return htmlspecialchars(strip_tags(trim($data)));
}

function redirect($url) {
    // Handle relative URLs
    if (!filter_var($url, FILTER_VALIDATE_URL)) {
        // If it starts with '/', it's absolute path from site root
        if (strpos($url, '/') === 0) {
            $url = SITE_URL . $url;
        } else {
            // Relative path - build from current directory
            $current_dir = dirname($_SERVER['PHP_SELF'] ?? '');
            if ($current_dir === '/') {
                $url = SITE_URL . '/' . $url;
            } else {
                $url = SITE_URL . $current_dir . '/' . $url;
            }
        }
    }
    
    // Debug: Log the redirect URL
    error_log("Redirecting to: " . $url);
    
    header("Location: " . $url);
    exit();
}

function isLoggedIn() {
    return isset($_SESSION['user_id']) && !empty($_SESSION['user_id']);
}

function getUserType() {
    return $_SESSION['user_type'] ?? null;
}

function calculateSkillMatch($jobSkills, $userSkills) {
    if (empty($jobSkills) || empty($userSkills)) {
        return 0;
    }
    
    // Clean and normalize skills
    $jobSkillsArray = array_map('trim', array_map('strtolower', explode(',', $jobSkills)));
    $userSkillsArray = array_map('trim', array_map('strtolower', explode(',', $userSkills)));
    
    // Remove empty entries
    $jobSkillsArray = array_filter($jobSkillsArray, function($skill) {
        return !empty($skill);
    });
    $userSkillsArray = array_filter($userSkillsArray, function($skill) {
        return !empty($skill);
    });
    
    // If no valid skills, return 0
    if (empty($jobSkillsArray) || empty($userSkillsArray)) {
        return 0;
    }
    
    // Find exact matches
    $exactMatches = array_intersect($jobSkillsArray, $userSkillsArray);
    
    // Find partial matches (skills that contain or are contained in other skills)
    $partialMatches = [];
    foreach ($jobSkillsArray as $jobSkill) {
        foreach ($userSkillsArray as $userSkill) {
            if (strpos($jobSkill, $userSkill) !== false || strpos($userSkill, $jobSkill) !== false) {
                if (!in_array($jobSkill, $exactMatches)) {
                    $partialMatches[] = $jobSkill;
                }
            }
        }
    }
    
    // Calculate total matches (exact + partial, but don't count duplicates)
    $totalMatches = count(array_unique(array_merge($exactMatches, $partialMatches)));
    
    // Calculate percentage based on job skills
    $percentage = round(($totalMatches / count($jobSkillsArray)) * 100);
    
    // Cap at 100%
    return min(100, $percentage);
}

function updateJobSlots($job_id, $action = 'decrease') {
    try {
        $database = new Database();
        $db = $database->getConnection();
        
        // Get current slots
        $query = "SELECT slots_available FROM job_posts WHERE id = ?";
        $stmt = $db->prepare($query);
        $stmt->execute([$job_id]);
        $job = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if (!$job) {
            return false;
        }
        
        $current_slots = $job['slots_available'];
        
        if ($action === 'decrease' && $current_slots > 0) {
            $new_slots = $current_slots - 1;
        } elseif ($action === 'increase' && $current_slots >= 0) {
            $new_slots = $current_slots + 1;
        } else {
            return false;
        }
        
        // Update slots
        $query = "UPDATE job_posts SET slots_available = ? WHERE id = ?";
        $stmt = $db->prepare($query);
        $stmt->execute([$new_slots, $job_id]);
        
        return true;
    } catch (Exception $e) {
        error_log("Error updating job slots: " . $e->getMessage());
        return false;
    }
}

// Error reporting for development
error_reporting(E_ALL);
ini_set('display_errors', 1);
?>
