// @ts-check
const { test, expect } = require('@playwright/test');

test.describe('Comment System Responsive Tests', () => {
  test.beforeEach(async ({ page }) => {
    await page.goto('/job-details.php?id=1');
    await page.waitForLoadState('networkidle');
  });

  test('should display properly on mobile devices', async ({ page }) => {
    // Set mobile viewport
    await page.setViewportSize({ width: 375, height: 667 });
    
    await page.waitForSelector('.comment-item', { timeout: 5000 });
    
    // Check if comments adapt to mobile layout
    const commentsList = page.locator('.comments-list');
    const alignItems = await commentsList.evaluate(el => 
      window.getComputedStyle(el).alignItems
    );
    
    // On mobile, comments should stretch full width
    expect(alignItems).toBe('stretch');
    
    // Check comment width on mobile
    const firstComment = page.locator('.comment-item').first();
    const commentWidth = await firstComment.evaluate(el => 
      window.getComputedStyle(el).maxWidth
    );
    
    expect(commentWidth).toBe('100%');
  });

  test('should handle reply forms on mobile', async ({ page }) => {
    await page.setViewportSize({ width: 375, height: 667 });
    
    await page.waitForSelector('.reply-btn', { timeout: 5000 });
    const replyButton = page.locator('.reply-btn').first();
    
    await replyButton.click();
    
    const replyForm = page.locator('.reply-form').first();
    await expect(replyForm).toBeVisible();
    
    // Check if reply form is properly sized on mobile
    const textarea = replyForm.locator('textarea');
    const textareaWidth = await textarea.boundingBox();
    
    expect(textareaWidth.width).toBeLessThan(375); // Should fit in mobile viewport
  });

  test('should show proper button sizing on mobile', async ({ page }) => {
    await page.setViewportSize({ width: 375, height: 667 });
    
    await page.waitForSelector('.comment-actions', { timeout: 5000 });
    const actionButtons = page.locator('.comment-actions .btn');
    
    if (await actionButtons.count() > 0) {
      const firstButton = actionButtons.first();
      
      // Check button font size on mobile
      const fontSize = await firstButton.evaluate(el => 
        window.getComputedStyle(el).fontSize
      );
      
      // Should be smaller on mobile
      expect(fontSize).toBe('12px');
      
      // Check button padding
      const padding = await firstButton.evaluate(el => 
        window.getComputedStyle(el).padding
      );
      
      expect(padding).toBe('4.8px 9.6px');
    }
  });

  test('should handle tablet layout', async ({ page }) => {
    // Set tablet viewport
    await page.setViewportSize({ width: 768, height: 1024 });
    
    await page.waitForSelector('.comment-item', { timeout: 5000 });
    
    const firstComment = page.locator('.comment-item').first();
    const commentMaxWidth = await firstComment.evaluate(el => 
      window.getComputedStyle(el).maxWidth
    );
    
    // On tablet, should maintain max-width but be responsive
    expect(commentMaxWidth).toBe('750px');
  });

  test('should show proper reply indentation on mobile', async ({ page }) => {
    await page.setViewportSize({ width: 375, height: 667 });
    
    const repliesContainer = page.locator('.replies');
    
    if (await repliesContainer.count() > 0) {
      // Check mobile reply indentation
      const paddingLeft = await repliesContainer.evaluate(el => 
        window.getComputedStyle(el).paddingLeft
      );
      
      // Should be reduced on mobile
      expect(paddingLeft).toBe('16px');
    }
  });

  test('should handle comment form on mobile', async ({ page }) => {
    await page.setViewportSize({ width: 375, height: 667 });
    
    const commentForm = page.locator('.comment-form');
    
    if (await commentForm.count() > 0) {
      // Check form width on mobile
      const formMaxWidth = await commentForm.evaluate(el => 
        window.getComputedStyle(el).maxWidth
      );
      
      expect(formMaxWidth).toBe('100%');
      
      // Check textarea behavior on mobile
      const textarea = commentForm.locator('textarea');
      await textarea.click();
      
      // Should be focusable and properly sized
      await expect(textarea).toBeFocused();
      
      const textareaBox = await textarea.boundingBox();
      expect(textareaBox.width).toBeLessThan(375);
    }
  });

  test('should maintain touch targets on mobile', async ({ page }) => {
    await page.setViewportSize({ width: 375, height: 667 });
    
    await page.waitForSelector('.reply-btn', { timeout: 5000 });
    const replyButton = page.locator('.reply-btn').first();
    
    // Check if button is large enough for touch
    const buttonBox = await replyButton.boundingBox();
    
    // Minimum touch target should be 44px (iOS guidelines)
    expect(buttonBox.height).toBeGreaterThanOrEqual(32); // Adjusted for small buttons
    expect(buttonBox.width).toBeGreaterThanOrEqual(60);
  });

  test('should handle landscape orientation on mobile', async ({ page }) => {
    // Set mobile landscape viewport
    await page.setViewportSize({ width: 667, height: 375 });
    
    await page.waitForSelector('.comment-item', { timeout: 5000 });
    
    const commentsList = page.locator('.comments-list');
    
    // Should still be properly aligned
    const alignItems = await commentsList.evaluate(el => 
      window.getComputedStyle(el).alignItems
    );
    
    expect(alignItems).toBe('stretch');
  });

  test('should show proper avatar sizing across devices', async ({ page }) => {
    const viewports = [
      { width: 375, height: 667, name: 'mobile' },
      { width: 768, height: 1024, name: 'tablet' },
      { width: 1200, height: 800, name: 'desktop' }
    ];
    
    for (const viewport of viewports) {
      await page.setViewportSize(viewport);
      
      await page.waitForSelector('.avatar', { timeout: 5000 });
      const avatar = page.locator('.avatar').first();
      
      const avatarSize = await avatar.evaluate(el => ({
        width: window.getComputedStyle(el).width,
        height: window.getComputedStyle(el).height
      }));
      
      // Avatar should maintain consistent size
      expect(avatarSize.width).toBe('40px');
      expect(avatarSize.height).toBe('40px');
    }
  });

  test('should handle text wrapping on narrow screens', async ({ page }) => {
    await page.setViewportSize({ width: 320, height: 568 }); // Very narrow screen
    
    await page.waitForSelector('.comment-content', { timeout: 5000 });
    const commentContent = page.locator('.comment-content').first();
    
    // Check if text wraps properly
    const wordWrap = await commentContent.evaluate(el => 
      window.getComputedStyle(el).wordWrap
    );
    
    expect(wordWrap).toBe('break-word');
    
    // Check if content doesn't overflow
    const contentBox = await commentContent.boundingBox();
    expect(contentBox.width).toBeLessThan(320);
  });

  test('should maintain functionality across screen sizes', async ({ page }) => {
    const viewports = [
      { width: 375, height: 667 },
      { width: 768, height: 1024 },
      { width: 1200, height: 800 }
    ];
    
    for (const viewport of viewports) {
      await page.setViewportSize(viewport);
      
      // Test reply functionality at each size
      await page.waitForSelector('.reply-btn', { timeout: 5000 });
      const replyButton = page.locator('.reply-btn').first();
      
      await replyButton.click();
      
      const replyForm = page.locator('.reply-form').first();
      await expect(replyForm).toBeVisible();
      
      // Close the form
      const cancelButton = replyForm.locator('.cancel-reply');
      await cancelButton.click();
      
      await expect(replyForm).not.toBeVisible();
    }
  });
});
