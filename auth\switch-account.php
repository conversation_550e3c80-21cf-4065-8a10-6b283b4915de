<?php
require_once __DIR__ . '/../config/config.php';

// Check if user is logged in
if (!isLoggedIn()) {
    redirect('login.php');
}

$target_type = sanitize($_GET['type'] ?? '');
$current_type = getUserType();

// Validate target type
if (!in_array($target_type, ['jobseeker', 'business', 'admin'])) {
    redirect('../index.php');
}

// Don't allow switching to admin unless already admin
if ($target_type === 'admin' && $current_type !== 'admin') {
    redirect('../index.php');
}

// Don't switch if already the target type
if ($current_type === $target_type) {
    redirect('../' . $target_type . '/dashboard.php');
}

try {
    $database = new Database();
    $db = $database->getConnection();
    
    // Check if user has the target account type
    $query = "SELECT id, user_type, is_verified FROM users WHERE email = ? AND user_type = ?";
    $stmt = $db->prepare($query);
    $stmt->execute([$_SESSION['email'], $target_type]);
    $target_account = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if ($target_account) {
        // Check if business account is verified
        if ($target_type === 'business' && !$target_account['is_verified']) {
            $_SESSION['error'] = 'Your business account is pending admin approval.';
            redirect('../index.php');
        }
        
        // Switch to existing account
        $_SESSION['user_id'] = $target_account['id'];
        $_SESSION['user_type'] = $target_account['user_type'];
        
        redirect('../' . $target_type . '/dashboard.php');
    } else {
        // Redirect to registration for new account type
        redirect('register.php?type=' . $target_type);
    }
    
} catch (Exception $e) {
    error_log("Account switching error: " . $e->getMessage());
    $_SESSION['error'] = 'An error occurred while switching accounts.';
    redirect('../index.php');
}
?>
