// @ts-check
const { test, expect } = require('@playwright/test');

test.describe('Comment System Tests', () => {
  test.beforeEach(async ({ page }) => {
    // Navigate to a job details page with comments
    // Note: You may need to adjust this URL based on your actual job IDs
    await page.goto('/job-details.php?id=1');
    await page.waitForLoadState('networkidle');
  });

  test('should display comments section with proper styling', async ({ page }) => {
    // Check if comments section exists
    const commentsSection = page.locator('.comments-section');
    await expect(commentsSection).toBeVisible();

    // Check if comments header is visible
    const commentsHeader = page.locator('.card-header h5');
    await expect(commentsHeader).toContainText('Comments');

    // Check if comments are right-aligned
    const commentsList = page.locator('.comments-list');
    await expect(commentsList).toHaveCSS('align-items', 'flex-end');
  });

  test('should display individual comments with proper structure', async ({ page }) => {
    // Wait for comments to load
    await page.waitForSelector('.comment-item', { timeout: 5000 });

    const firstComment = page.locator('.comment-item').first();
    
    // Check comment structure
    await expect(firstComment).toBeVisible();
    await expect(firstComment.locator('.comment-header')).toBeVisible();
    await expect(firstComment.locator('.comment-author')).toBeVisible();
    await expect(firstComment.locator('.avatar')).toBeVisible();
    await expect(firstComment.locator('.comment-content')).toBeVisible();
    await expect(firstComment.locator('.comment-actions')).toBeVisible();

    // Check if comment has proper styling
    await expect(firstComment).toHaveCSS('border-radius', '16px');
    await expect(firstComment).toHaveCSS('max-width', '750px');
  });

  test('should show hover effects on comments', async ({ page }) => {
    await page.waitForSelector('.comment-item', { timeout: 5000 });
    
    const firstComment = page.locator('.comment-item').first();
    
    // Get initial transform value
    const initialTransform = await firstComment.evaluate(el => 
      window.getComputedStyle(el).transform
    );

    // Hover over the comment
    await firstComment.hover();
    
    // Wait for animation to complete
    await page.waitForTimeout(500);
    
    // Check if transform has changed (indicating hover effect)
    const hoverTransform = await firstComment.evaluate(el => 
      window.getComputedStyle(el).transform
    );
    
    expect(hoverTransform).not.toBe(initialTransform);
  });

  test('should display reply functionality', async ({ page }) => {
    await page.waitForSelector('.reply-btn', { timeout: 5000 });
    
    const replyButton = page.locator('.reply-btn').first();
    await expect(replyButton).toBeVisible();
    await expect(replyButton).toContainText('Reply');

    // Click reply button
    await replyButton.click();
    
    // Check if reply form appears
    const replyForm = page.locator('.reply-form').first();
    await expect(replyForm).toBeVisible();
    
    // Check reply form structure
    await expect(replyForm.locator('textarea')).toBeVisible();
    await expect(replyForm.locator('button[type="submit"]')).toContainText('Reply');
    await expect(replyForm.locator('.cancel-reply')).toContainText('Cancel');
  });

  test('should handle reply form interactions', async ({ page }) => {
    await page.waitForSelector('.reply-btn', { timeout: 5000 });
    
    const replyButton = page.locator('.reply-btn').first();
    await replyButton.click();
    
    const replyForm = page.locator('.reply-form').first();
    const textarea = replyForm.locator('textarea');
    const cancelButton = replyForm.locator('.cancel-reply');
    
    // Type in textarea
    await textarea.fill('This is a test reply');
    await expect(textarea).toHaveValue('This is a test reply');
    
    // Test cancel functionality
    await cancelButton.click();
    await expect(replyForm).not.toBeVisible();
  });

  test('should display replies with proper nesting', async ({ page }) => {
    // Check if replies exist
    const repliesContainer = page.locator('.replies');
    if (await repliesContainer.count() > 0) {
      const firstReply = repliesContainer.locator('.reply-item').first();
      
      await expect(firstReply).toBeVisible();
      await expect(firstReply).toHaveCSS('border-radius', '12px');
      
      // Check if reply is properly indented
      const repliesSection = firstReply.locator('..');
      await expect(repliesSection).toHaveCSS('padding-left', '32px');
    }
  });

  test('should show like button functionality', async ({ page }) => {
    await page.waitForSelector('.like-btn', { timeout: 5000 });
    
    const likeButton = page.locator('.like-btn').first();
    await expect(likeButton).toBeVisible();
    await expect(likeButton).toContainText('Like');
    
    // Click like button
    await likeButton.click();
    
    // Check if button state changes
    await expect(likeButton).toHaveClass(/liked/);
    
    // Check if icon changes from far to fas
    const icon = likeButton.locator('i');
    await expect(icon).toHaveClass(/fas/);
  });

  test('should display comment form for logged-in users', async ({ page }) => {
    // Check if comment form exists
    const commentForm = page.locator('.comment-form');
    
    // This test assumes user is logged in - you may need to implement login first
    if (await commentForm.count() > 0) {
      await expect(commentForm).toBeVisible();
      await expect(commentForm.locator('textarea')).toBeVisible();
      await expect(commentForm.locator('button[type="submit"]')).toContainText('Post Comment');
      
      // Check form styling
      await expect(commentForm).toHaveCSS('border-radius', '16px');
      await expect(commentForm).toHaveCSS('max-width', '800px');
    }
  });

  test('should handle textarea auto-resize', async ({ page }) => {
    const commentForm = page.locator('.comment-form');
    
    if (await commentForm.count() > 0) {
      const textarea = commentForm.locator('textarea');
      
      // Get initial height
      const initialHeight = await textarea.evaluate(el => el.offsetHeight);
      
      // Type multiple lines
      await textarea.fill('Line 1\nLine 2\nLine 3\nLine 4\nLine 5');
      
      // Wait for auto-resize
      await page.waitForTimeout(300);
      
      // Check if height increased
      const newHeight = await textarea.evaluate(el => el.offsetHeight);
      expect(newHeight).toBeGreaterThan(initialHeight);
    }
  });

  test('should show proper animations on comment load', async ({ page }) => {
    // Reload page to see animation
    await page.reload();
    await page.waitForLoadState('networkidle');
    
    // Check if comments have animation class
    const comments = page.locator('.comment-item');
    if (await comments.count() > 0) {
      const firstComment = comments.first();
      
      // Check if animation is applied
      const animationName = await firstComment.evaluate(el => 
        window.getComputedStyle(el).animationName
      );
      
      expect(animationName).toBe('slideInRight');
    }
  });
});
