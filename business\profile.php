<?php
require_once __DIR__ . '/../config/config.php';

// Check if user is logged in and is business
if (!isLoggedIn() || getUserType() !== 'business') {
    redirect('../auth/login.php');
}

$success = '';
$error = '';

try {
    $database = new Database();
    $db = $database->getConnection();
    $user_id = $_SESSION['user_id'];

    // Get current business data
    $query = "SELECT u.*, bp.* FROM users u 
              LEFT JOIN business_profiles bp ON u.id = bp.user_id 
              WHERE u.id = ? AND u.user_type = 'business'";
    $stmt = $db->prepare($query);
    $stmt->execute([$user_id]);
    $business = $stmt->fetch(PDO::FETCH_ASSOC);

    if (!$business) {
        redirect('../auth/login.php');
    }

    // Handle form submission
    if ($_SERVER['REQUEST_METHOD'] === 'POST') {
        $first_name = sanitize($_POST['first_name'] ?? '');
        $last_name = sanitize($_POST['last_name'] ?? '');
        $phone = sanitize($_POST['phone'] ?? '');
        $company_name = sanitize($_POST['company_name'] ?? '');
        $business_description = sanitize($_POST['business_description'] ?? '');
        $business_address = sanitize($_POST['business_address'] ?? '');
        $mayors_permit = sanitize($_POST['mayors_permit'] ?? '');
        $dti_number = sanitize($_POST['dti_number'] ?? '');

        // Validation
        if (empty($first_name) || empty($last_name) || empty($phone) || empty($company_name)) {
            $error = 'Please fill in all required fields.';
        } elseif (strlen($phone) !== 11 || !is_numeric($phone)) {
            $error = 'Phone number must be exactly 11 digits.';
        } else {
            try {
                $db->beginTransaction();

                // Update user table
                $query = "UPDATE users SET first_name = ?, last_name = ?, phone = ? WHERE id = ?";
                $stmt = $db->prepare($query);
                $stmt->execute([$first_name, $last_name, $phone, $user_id]);

                // Update business profile
                $query = "UPDATE business_profiles 
                          SET company_name = ?, business_description = ?, business_address = ?, mayors_permit = ?, dti_number = ? 
                          WHERE user_id = ?";
                $stmt = $db->prepare($query);
                $stmt->execute([$company_name, $business_description, $business_address, $mayors_permit, $dti_number, $user_id]);

                $db->commit();
                $success = 'Profile updated successfully!';

                // Update session data
                $_SESSION['first_name'] = $first_name;
                $_SESSION['last_name'] = $last_name;

                // Refresh business data
                $query = "SELECT u.*, bp.* FROM users u 
                          LEFT JOIN business_profiles bp ON u.id = bp.user_id 
                          WHERE u.id = ? AND u.user_type = 'business'";
                $stmt = $db->prepare($query);
                $stmt->execute([$user_id]);
                $business = $stmt->fetch(PDO::FETCH_ASSOC);

            } catch (Exception $e) {
                $db->rollback();
                $error = 'Failed to update profile. Please try again.';
                error_log("Business profile update error: " . $e->getMessage());
            }
        }
    }

} catch (Exception $e) {
    error_log("Business profile page error: " . $e->getMessage());
    $error = "An error occurred while loading your profile.";
}

$page_title = "Business Profile";
include __DIR__ . '/../includes/header.php';
?>

<main class="py-4">
    <div class="container">
        <div class="row">
            <div class="col-lg-8 mx-auto">
                <div class="card shadow">
                    <div class="card-header">
                        <h4 class="mb-0"><i class="fas fa-building me-2"></i>Business Profile</h4>
                    </div>
                    <div class="card-body">
                        <?php if ($error): ?>
                        <div class="alert alert-danger alert-dismissible fade show" role="alert">
                            <?php echo htmlspecialchars($error); ?>
                            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                        </div>
                        <?php endif; ?>

                        <?php if ($success): ?>
                        <div class="alert alert-success alert-dismissible fade show" role="alert">
                            <?php echo htmlspecialchars($success); ?>
                            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                        </div>
                        <?php endif; ?>

                        <?php if (!$business['is_verified']): ?>
                        <div class="alert alert-warning" role="alert">
                            <h5 class="alert-heading"><i class="fas fa-exclamation-triangle me-2"></i>Account Pending Verification</h5>
                            <p>Your business account is currently pending admin approval. Please ensure all information is accurate and complete.</p>
                        </div>
                        <?php endif; ?>

                        <form method="POST">
                            <!-- Personal Information -->
                            <div class="mb-4">
                                <h5 class="border-bottom pb-2 mb-3">Personal Information</h5>
                                <div class="row">
                                    <div class="col-md-6 mb-3">
                                        <label for="first_name" class="form-label">First Name <span class="text-danger">*</span></label>
                                        <input type="text" class="form-control" id="first_name" name="first_name" 
                                               value="<?php echo htmlspecialchars($business['first_name'] ?? ''); ?>" required>
                                    </div>
                                    <div class="col-md-6 mb-3">
                                        <label for="last_name" class="form-label">Last Name <span class="text-danger">*</span></label>
                                        <input type="text" class="form-control" id="last_name" name="last_name" 
                                               value="<?php echo htmlspecialchars($business['last_name'] ?? ''); ?>" required>
                                    </div>
                                </div>
                                <div class="row">
                                    <div class="col-md-6 mb-3">
                                        <label for="email" class="form-label">Email Address</label>
                                        <input type="email" class="form-control" id="email" 
                                               value="<?php echo htmlspecialchars($business['email'] ?? ''); ?>" disabled>
                                        <small class="form-text text-muted">Email cannot be changed</small>
                                    </div>
                                    <div class="col-md-6 mb-3">
                                        <label for="phone" class="form-label">Phone Number <span class="text-danger">*</span></label>
                                        <div class="input-group">
                                            <span class="input-group-text">+63</span>
                                            <input type="tel" class="form-control" id="phone" name="phone" 
                                                   placeholder="9123456789" maxlength="11" pattern="[0-9]{11}" 
                                                   value="<?php echo htmlspecialchars($business['phone'] ?? ''); ?>" required>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Business Information -->
                            <div class="mb-4">
                                <h5 class="border-bottom pb-2 mb-3">Business Information</h5>
                                <div class="mb-3">
                                    <label for="company_name" class="form-label">Company Name <span class="text-danger">*</span></label>
                                    <input type="text" class="form-control" id="company_name" name="company_name" 
                                           value="<?php echo htmlspecialchars($business['company_name'] ?? ''); ?>" required>
                                </div>
                                <div class="mb-3">
                                    <label for="business_description" class="form-label">Business Description</label>
                                    <textarea class="form-control" id="business_description" name="business_description" rows="3"><?php echo htmlspecialchars($business['business_description'] ?? ''); ?></textarea>
                                </div>
                                <div class="mb-3">
                                    <label for="business_address" class="form-label">Business Address</label>
                                    <textarea class="form-control" id="business_address" name="business_address" rows="2"><?php echo htmlspecialchars($business['business_address'] ?? ''); ?></textarea>
                                </div>
                                <div class="row">
                                    <div class="col-md-6 mb-3">
                                        <label for="mayors_permit" class="form-label">Mayor's Permit Number</label>
                                        <input type="text" class="form-control" id="mayors_permit" name="mayors_permit" 
                                               value="<?php echo htmlspecialchars($business['mayors_permit'] ?? ''); ?>">
                                    </div>
                                    <div class="col-md-6 mb-3">
                                        <label for="dti_number" class="form-label">DTI Number</label>
                                        <input type="text" class="form-control" id="dti_number" name="dti_number" 
                                               value="<?php echo htmlspecialchars($business['dti_number'] ?? ''); ?>">
                                    </div>
                                </div>
                            </div>

                            <div class="d-flex justify-content-between">
                                <a href="dashboard.php" class="btn btn-secondary">
                                    <i class="fas fa-arrow-left me-1"></i>Back to Dashboard
                                </a>
                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-save me-1"></i>Save Changes
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>
</main>

<script>
// Phone number validation
document.getElementById('phone').addEventListener('input', function(e) {
    let value = e.target.value.replace(/\D/g, '');
    if (value.length > 11) {
        value = value.slice(0, 11);
    }
    e.target.value = value;
});
</script>

<?php include __DIR__ . '/../includes/footer.php'; ?>
