{"name": "tan-aw-job-portal-tests", "version": "1.0.0", "description": "Playwright tests for Tan-aw Job Portal comment functionality", "main": "index.js", "scripts": {"test": "playwright test", "test:headed": "playwright test --headed", "test:debug": "playwright test --debug", "test:ui": "playwright test --ui", "test:comments": "playwright test comments.spec.js", "test:comments-mobile": "playwright test comments.spec.js --project='Mobile Chrome'", "test:comments-dark": "playwright test comments-dark-mode.spec.js", "report": "playwright show-report", "install": "playwright install"}, "keywords": ["playwright", "testing", "e2e", "comments", "job-portal"], "author": "Tan-aw Job Portal Team", "license": "MIT", "devDependencies": {"@playwright/test": "^1.40.0"}}