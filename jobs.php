<?php
require_once __DIR__ . '/config/config.php';
require_once __DIR__ . '/lib/QualificationAnalyzer.php';

// Get search parameters
$search = sanitize($_GET['search'] ?? '');
$location = sanitize($_GET['location'] ?? '');
$job_type = sanitize($_GET['job_type'] ?? '');
$salary_min = (int)($_GET['salary_min'] ?? 0);
$qualification_filter = (int)($_GET['qualification'] ?? 0); // New filter for qualification percentage
$slots_filter = (int)($_GET['slots_filter'] ?? 0); // New filter for slots availability

// Pagination
$page = max(1, (int)($_GET['page'] ?? 1));
$per_page = 12;
$offset = ($page - 1) * $per_page;

// Build query
$where_conditions = ["jp.status = 'approved'"];
$params = [];

if (!empty($search)) {
    $where_conditions[] = "(jp.title LIKE ? OR jp.description LIKE ? OR bp.company_name LIKE ? OR jp.skills_required LIKE ?)";
    $search_param = "%$search%";
    $params = array_merge($params, [$search_param, $search_param, $search_param, $search_param]);
}

if (!empty($location)) {
    $where_conditions[] = "jp.location LIKE ?";
    $params[] = "%$location%";
}

if (!empty($job_type)) {
    $where_conditions[] = "jp.job_type = ?";
    $params[] = $job_type;
}

if ($salary_min > 0) {
    $where_conditions[] = "jp.salary_min >= ?";
    $params[] = $salary_min;
}

if ($slots_filter > 0) {
    $where_conditions[] = "jp.slots_available >= ?";
    $params[] = $slots_filter;
}

$where_clause = implode(' AND ', $where_conditions);

try {
    $database = new Database();
    $db = $database->getConnection();
    
    // Initialize qualification analyzer
    $qualificationAnalyzer = new QualificationAnalyzer($db);
    
    // Get total count for pagination
    $count_query = "SELECT COUNT(*) as total 
                    FROM job_posts jp 
                    JOIN business_profiles bp ON jp.business_id = bp.user_id 
                    WHERE $where_clause";
    $count_stmt = $db->prepare($count_query);
    $count_stmt->execute($params);
    $total_jobs = $count_stmt->fetch(PDO::FETCH_ASSOC)['total'];
    $total_pages = ceil($total_jobs / $per_page);
    
    // Get jobs with pagination
    $query = "SELECT jp.*, bp.company_name, bp.business_address, bp.business_description
              FROM job_posts jp 
              JOIN business_profiles bp ON jp.business_id = bp.user_id 
              WHERE $where_clause 
              ORDER BY jp.created_at DESC
              LIMIT $per_page OFFSET $offset";
    
    $stmt = $db->prepare($query);
    $stmt->execute($params);
    $jobs = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    // Calculate qualifications for logged-in job seekers
    $job_qualifications = [];
    if (isLoggedIn() && getUserType() === 'jobseeker') {
        $user_id = $_SESSION['user_id'];
        foreach ($jobs as $job) {
            $qualification = $qualificationAnalyzer->calculateQualification($user_id, $job['id']);
            $job_qualifications[$job['id']] = $qualification;
        }
        
        // Filter by qualification percentage if specified
        if ($qualification_filter > 0) {
            $filtered_jobs = [];
            foreach ($jobs as $job) {
                if ($job_qualifications[$job['id']] >= $qualification_filter) {
                    $filtered_jobs[] = $job;
                }
            }
            $jobs = $filtered_jobs;
        }
    }
    
} catch (Exception $e) {
    error_log("Jobs page error: " . $e->getMessage());
    $jobs = [];
    $total_jobs = 0;
    $total_pages = 0;
    $job_qualifications = [];
}

$page_title = "Browse Jobs";
include __DIR__ . '/includes/header.php';
?>

<main class="py-5">
    <div class="container">
        <!-- Search Form -->
        <div class="row mb-5">
            <div class="col-12">
                <div class="card shadow-sm search-form">
                    <div class="card-body">
                        <h4 class="card-title mb-4">
                            <i class="fas fa-search me-2"></i>Find Your Perfect Job
                        </h4>
                        <form method="GET" action="jobs.php" class="row g-3">
                            <div class="col-md-3">
                                <label for="search" class="form-label">Keywords</label>
                                <input type="text" class="form-control" id="search" name="search" 
                                       value="<?php echo htmlspecialchars($search); ?>" 
                                       placeholder="Job title, company, skills...">
                            </div>
                            <div class="col-md-2">
                                <label for="location" class="form-label">Location</label>
                                <input type="text" class="form-control" id="location" name="location" 
                                       value="<?php echo htmlspecialchars($location); ?>" 
                                       placeholder="City, province...">
                            </div>
                            <div class="col-md-2">
                                <label for="job_type" class="form-label">Job Type</label>
                                <select class="form-select" id="job_type" name="job_type">
                                    <option value="">All Types</option>
                                    <option value="full-time" <?php echo $job_type === 'full-time' ? 'selected' : ''; ?>>Full-time</option>
                                    <option value="part-time" <?php echo $job_type === 'part-time' ? 'selected' : ''; ?>>Part-time</option>
                                    <option value="contract" <?php echo $job_type === 'contract' ? 'selected' : ''; ?>>Contract</option>
                                    <option value="internship" <?php echo $job_type === 'internship' ? 'selected' : ''; ?>>Internship</option>
                                </select>
                            </div>
                            <div class="col-md-2">
                                <label for="salary_min" class="form-label">Min Salary</label>
                                <select class="form-select" id="salary_min" name="salary_min">
                                    <option value="">Any Salary</option>
                                    <option value="15000" <?php echo $salary_min === 15000 ? 'selected' : ''; ?>>₱15,000+</option>
                                    <option value="20000" <?php echo $salary_min === 20000 ? 'selected' : ''; ?>>₱20,000+</option>
                                    <option value="25000" <?php echo $salary_min === 25000 ? 'selected' : ''; ?>>₱25,000+</option>
                                    <option value="30000" <?php echo $salary_min === 30000 ? 'selected' : ''; ?>>₱30,000+</option>
                                </select>
                            </div>
                            <?php if (isLoggedIn() && getUserType() === 'jobseeker'): ?>
                            <div class="col-md-2">
                                <label for="qualification" class="form-label">Min Qualification</label>
                                <select class="form-select" id="qualification" name="qualification">
                                    <option value="">Any Match</option>
                                    <option value="80" <?php echo $qualification_filter === 80 ? 'selected' : ''; ?>>80%+ Match</option>
                                    <option value="70" <?php echo $qualification_filter === 70 ? 'selected' : ''; ?>>70%+ Match</option>
                                    <option value="60" <?php echo $qualification_filter === 60 ? 'selected' : ''; ?>>60%+ Match</option>
                                    <option value="50" <?php echo $qualification_filter === 50 ? 'selected' : ''; ?>>50%+ Match</option>
                                </select>
                            </div>
                            <?php endif; ?>
                            <div class="col-md-2">
                                <label for="slots_filter" class="form-label">Slots Available</label>
                                <select class="form-select" id="slots_filter" name="slots_filter">
                                    <option value="">Any Slots</option>
                                    <option value="1" <?php echo isset($_GET['slots_filter']) && $_GET['slots_filter'] === '1' ? 'selected' : ''; ?>>1+ Slots</option>
                                    <option value="2" <?php echo isset($_GET['slots_filter']) && $_GET['slots_filter'] === '2' ? 'selected' : ''; ?>>2+ Slots</option>
                                    <option value="5" <?php echo isset($_GET['slots_filter']) && $_GET['slots_filter'] === '5' ? 'selected' : ''; ?>>5+ Slots</option>
                                </select>
                            </div>
                            <div class="col-md-1">
                                <label class="form-label">&nbsp;</label>
                                <button type="submit" class="btn btn-primary w-100">
                                    <i class="fas fa-search"></i>
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>

        <!-- Results -->
        <div class="row">
            <div class="col-12">
                <div class="d-flex justify-content-between align-items-center mb-4">
                    <h3>
                        <?php if (!empty($search) || !empty($location) || !empty($job_type) || $salary_min > 0 || $slots_filter > 0): ?>
                            Search Results (<?php echo count($jobs); ?> jobs found)
                        <?php else: ?>
                            All Jobs (<?php echo count($jobs); ?> available)
                        <?php endif; ?>
                    </h3>
                    <?php if (!empty($search) || !empty($location) || !empty($job_type) || $salary_min > 0 || $slots_filter > 0): ?>
                    <a href="jobs.php" class="btn btn-outline-secondary">
                        <i class="fas fa-times me-1"></i>Clear Filters
                    </a>
                    <?php endif; ?>
                </div>
            </div>
        </div>

        <?php if (empty($jobs)): ?>
        <div class="row">
            <div class="col-12">
                <div class="text-center py-5">
                    <i class="fas fa-search fa-4x text-muted mb-4"></i>
                    <h4>No jobs found</h4>
                    <p class="text-muted">Try adjusting your search criteria or browse all available jobs.</p>
                    <a href="jobs.php" class="btn btn-primary">View All Jobs</a>
                </div>
            </div>
        </div>
        <?php else: ?>
        <div class="row">
            <?php foreach ($jobs as $job): ?>
            <div class="col-lg-6 mb-4">
                <div class="card job-card h-100 shadow-sm hover-lift">
                    <div class="card-body">
                        <div class="d-flex justify-content-between align-items-start mb-3">
                            <h5 class="card-title mb-0">
                                <a href="job-details.php?id=<?php echo $job['id']; ?>" class="text-decoration-none text-primary">
                                    <?php echo htmlspecialchars($job['title']); ?>
                                </a>
                            </h5>
                            <div class="d-flex align-items-center gap-2">
                                <?php if (isLoggedIn() && getUserType() === 'jobseeker' && isset($job_qualifications[$job['id']])): ?>
                                <?php 
                                $qualification = $job_qualifications[$job['id']];
                                $color = $qualificationAnalyzer->getQualificationColor($qualification);
                                $text = $qualificationAnalyzer->getQualificationText($qualification);
                                $icon = $qualificationAnalyzer->getQualificationIcon($qualification);
                                ?>
                                <div class="text-end">
                                    <div class="badge bg-<?php echo $color; ?> fs-6">
                                        <i class="<?php echo $icon; ?> me-1"></i><?php echo $qualification; ?>%
                                    </div>
                                    <div class="small text-muted"><?php echo $text; ?></div>
                                </div>
                                <?php endif; ?>
                                <span class="badge bg-primary"><?php echo ucfirst(str_replace('-', ' ', $job['job_type'])); ?></span>
                            </div>
                        </div>
                        
                        <div class="company-info mb-3">
                            <p class="text-primary mb-1">
                                <i class="fas fa-building me-1"></i>
                                <strong><?php echo htmlspecialchars($job['company_name']); ?></strong>
                            </p>
                            <?php if (!empty($job['business_address'])): ?>
                            <p class="text-muted small mb-0">
                                <i class="fas fa-map-marker-alt me-1"></i>
                                <?php echo htmlspecialchars($job['business_address']); ?>
                            </p>
                            <?php endif; ?>
                        </div>
                        
                        <p class="card-text text-muted mb-3">
                            <?php 
                            $description = strip_tags($job['description']);
                            echo strlen($description) > 150 ? substr($description, 0, 150) . '...' : $description; 
                            ?>
                        </p>
                        
                        <div class="row text-muted small mb-3">
                            <div class="col-6">
                                <i class="fas fa-map-marker-alt me-1"></i>
                                <?php echo htmlspecialchars($job['location']); ?>
                            </div>
                            <div class="col-6 text-end">
                                <i class="fas fa-calendar me-1"></i>
                                <?php echo date('M j, Y', strtotime($job['created_at'])); ?>
                            </div>
                        </div>
                        
                        <div class="row text-muted small mb-3">
                            <div class="col-6">
                                <i class="fas fa-users me-1"></i>
                                <?php echo $job['slots_available']; ?> slot(s) available
                            </div>
                            <div class="col-6 text-end">
                                <i class="fas fa-clock me-1"></i>
                                <?php echo ucfirst(str_replace('-', ' ', $job['job_type'])); ?>
                            </div>
                        </div>
                        
                        <?php if ($job['salary_min'] && $job['salary_max']): ?>
                        <div class="mb-3">
                            <span class="text-success fw-bold">
                                ₱<?php echo number_format($job['salary_min']); ?> - ₱<?php echo number_format($job['salary_max']); ?>
                            </span>
                            <small class="text-muted">/month</small>
                        </div>
                        <?php endif; ?>
                        
                        <?php if ($job['skills_required']): ?>
                        <div class="mb-3">
                            <?php 
                            $skills = array_slice(explode(',', $job['skills_required']), 0, 3);
                            foreach ($skills as $skill): 
                            ?>
                            <span class="badge bg-light text-dark me-1"><?php echo trim(htmlspecialchars($skill)); ?></span>
                            <?php endforeach; ?>
                            <?php if (count(explode(',', $job['skills_required'])) > 3): ?>
                            <small class="text-muted">+<?php echo count(explode(',', $job['skills_required'])) - 3; ?> more</small>
                            <?php endif; ?>
                        </div>
                        <?php endif; ?>
                        
                        <div class="d-flex gap-2">
                            <a href="job-details.php?id=<?php echo $job['id']; ?>" class="btn btn-outline-primary flex-fill">
                                <i class="fas fa-eye me-1"></i>View Details
                            </a>
                            <?php if ($job['slots_available'] <= 0): ?>
                            <span class="btn btn-danger disabled">
                                <i class="fas fa-times me-1"></i>Filled
                            </span>
                            <?php elseif ($job['slots_available'] <= 2): ?>
                            <span class="btn btn-warning disabled">
                                <i class="fas fa-clock me-1"></i>Limited
                            </span>
                            <?php else: ?>
                            <?php if (isLoggedIn() && getUserType() === 'jobseeker'): ?>
                            <a href="auth/login.php" class="btn btn-primary">
                                <i class="fas fa-sign-in-alt me-1"></i>Login
                            </a>
                            <?php else: ?>
                            <a href="auth/login.php" class="btn btn-primary">
                                <i class="fas fa-sign-in-alt me-1"></i>Login
                            </a>
                            <?php endif; ?>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
            </div>
            <?php endforeach; ?>
        </div>
        <?php endif; ?>
        
        <!-- Pagination -->
        <?php if ($total_pages > 1): ?>
        <div class="row">
            <div class="col-12">
                <nav aria-label="Job listings pagination">
                    <ul class="pagination justify-content-center">
                        <?php if ($page > 1): ?>
                        <li class="page-item">
                            <a class="page-link" href="?<?php echo http_build_query(array_merge($_GET, ['page' => $page - 1])); ?>">
                                <i class="fas fa-chevron-left"></i> Previous
                            </a>
                        </li>
                        <?php endif; ?>
                        
                        <?php for ($i = max(1, $page - 2); $i <= min($total_pages, $page + 2); $i++): ?>
                        <li class="page-item <?php echo $i === $page ? 'active' : ''; ?>">
                            <a class="page-link" href="?<?php echo http_build_query(array_merge($_GET, ['page' => $i])); ?>">
                                <?php echo $i; ?>
                            </a>
                        </li>
                        <?php endfor; ?>
                        
                        <?php if ($page < $total_pages): ?>
                        <li class="page-item">
                            <a class="page-link" href="?<?php echo http_build_query(array_merge($_GET, ['page' => $page + 1])); ?>">
                                Next <i class="fas fa-chevron-right"></i>
                            </a>
                        </li>
                        <?php endif; ?>
                    </ul>
                </nav>
            </div>
        </div>
        <?php endif; ?>
    </div>
</main>

<?php include __DIR__ . '/includes/footer.php'; ?>
