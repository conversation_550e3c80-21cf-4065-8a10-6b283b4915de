-- Create database
CREATE DATABASE IF NOT EXISTS tan_aw_job_portal;
USE tan_aw_job_portal;

-- Users table
CREATE TABLE IF NOT EXISTS users (
    id INT AUTO_INCREMENT PRIMARY KEY,
    first_name VA<PERSON><PERSON><PERSON>(100) NOT NULL,
    last_name VA<PERSON><PERSON><PERSON>(100) NOT NULL,
    email VARCHAR(255) UNIQUE NOT NULL,
    phone VARCHAR(20),
    password VARCHAR(255) NOT NULL,
    user_type <PERSON>NU<PERSON>('admin', 'business', 'jobseeker') NOT NULL,
    is_verified BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- Business profiles table
CREATE TABLE IF NOT EXISTS business_profiles (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    company_name VARCHAR(255) NOT NULL,
    business_description TEXT,
    business_address TEXT,
    mayors_permit VARCHAR(100),
    dti_number VARCHAR(100),
    is_approved BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
);

-- Jobseeker profiles table
CREATE TABLE IF NOT EXISTS jobseeker_profiles (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    education_level VARCHAR(100),
    degree_course VARCHAR(255),
    skills TEXT,
    experience_years INT DEFAULT 0,
    location VARCHAR(255),
    expected_salary DECIMAL(10,2),
    resume_file VARCHAR(255),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
);

-- Degrees table
CREATE TABLE IF NOT EXISTS degrees (
    id INT AUTO_INCREMENT PRIMARY KEY,
    level VARCHAR(50) NOT NULL,
    category VARCHAR(100) NOT NULL,
    course_name VARCHAR(255) NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Job posts table
CREATE TABLE IF NOT EXISTS job_posts (
    id INT AUTO_INCREMENT PRIMARY KEY,
    business_id INT NOT NULL,
    title VARCHAR(255) NOT NULL,
    description TEXT NOT NULL,
    requirements TEXT,
    location VARCHAR(255) NOT NULL,
    job_type ENUM('full-time', 'part-time', 'contract', 'internship') NOT NULL,
    salary_min DECIMAL(10,2),
    salary_max DECIMAL(10,2),
    slots_available INT DEFAULT 1,
    skills_required TEXT,
    job_image VARCHAR(255),
    status ENUM('pending', 'approved', 'rejected', 'archived') DEFAULT 'pending',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (business_id) REFERENCES users(id) ON DELETE CASCADE
);

-- Job applications table
CREATE TABLE IF NOT EXISTS job_applications (
    id INT AUTO_INCREMENT PRIMARY KEY,
    job_id INT NOT NULL,
    user_id INT NOT NULL,
    status ENUM('pending', 'accepted', 'rejected', 'interview') DEFAULT 'pending',
    cover_letter TEXT,
    inquiry TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (job_id) REFERENCES job_posts(id) ON DELETE CASCADE,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    UNIQUE KEY unique_application (job_id, user_id)
);

-- Job comments table
CREATE TABLE IF NOT EXISTS job_comments (
    id INT AUTO_INCREMENT PRIMARY KEY,
    job_id INT NOT NULL,
    user_id INT NOT NULL,
    comment TEXT NOT NULL,
    parent_id INT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (job_id) REFERENCES job_posts(id) ON DELETE CASCADE,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (parent_id) REFERENCES job_comments(id) ON DELETE CASCADE
);

-- Jobseeker posts table
CREATE TABLE IF NOT EXISTS jobseeker_posts (
    id INT AUTO_INCREMENT PRIMARY KEY,
    jobseeker_id INT NOT NULL,
    title VARCHAR(255) NOT NULL,
    description TEXT NOT NULL,
    skills TEXT,
    experience_years INT DEFAULT 0,
    availability ENUM('immediate', 'within_week', 'within_month', 'flexible') DEFAULT 'flexible',
    expected_salary DECIMAL(10,2),
    location VARCHAR(255),
    status ENUM('active', 'hired', 'inactive') DEFAULT 'active',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (jobseeker_id) REFERENCES users(id) ON DELETE CASCADE
);

-- Job inquiries table
CREATE TABLE IF NOT EXISTS job_inquiries (
    id INT AUTO_INCREMENT PRIMARY KEY,
    job_id INT NOT NULL,
    user_id INT NOT NULL,
    inquiry TEXT NOT NULL,
    response TEXT,
    status ENUM('pending', 'answered') DEFAULT 'pending',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (job_id) REFERENCES job_posts(id) ON DELETE CASCADE,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
);

-- Announcements table
CREATE TABLE IF NOT EXISTS announcements (
    id INT AUTO_INCREMENT PRIMARY KEY,
    admin_id INT NOT NULL,
    title VARCHAR(255) NOT NULL,
    content TEXT NOT NULL,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (admin_id) REFERENCES users(id) ON DELETE CASCADE
);

-- Insert default admin user
INSERT IGNORE INTO users (first_name, last_name, email, password, user_type, is_verified) 
VALUES ('Admin', 'User', '<EMAIL>', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'admin', 1);

-- Insert sample degrees
INSERT IGNORE INTO degrees (level, category, course_name) VALUES
('Bachelor', 'Engineering', 'Bachelor of Science in Computer Engineering'),
('Bachelor', 'Engineering', 'Bachelor of Science in Civil Engineering'),
('Bachelor', 'Engineering', 'Bachelor of Science in Electrical Engineering'),
('Bachelor', 'Engineering', 'Bachelor of Science in Mechanical Engineering'),
('Bachelor', 'Information Technology', 'Bachelor of Science in Information Technology'),
('Bachelor', 'Information Technology', 'Bachelor of Science in Computer Science'),
('Bachelor', 'Business', 'Bachelor of Science in Business Administration'),
('Bachelor', 'Business', 'Bachelor of Science in Accountancy'),
('Bachelor', 'Education', 'Bachelor of Elementary Education'),
('Bachelor', 'Education', 'Bachelor of Secondary Education'),
('Vocational', 'Technical', 'Computer Programming'),
('Vocational', 'Technical', 'Automotive Technology'),
('Vocational', 'Technical', 'Electrical Installation and Maintenance'),
('Vocational', 'Culinary', 'Culinary Arts'),
('Vocational', 'Beauty Care', 'Beauty Care and Wellness'),
('High School', 'General', 'High School Graduate'),
('Master', 'Engineering', 'Master of Science in Engineering'),
('Master', 'Business', 'Master of Business Administration'),
('Master', 'Education', 'Master of Arts in Education'),
('Doctorate', 'Engineering', 'Doctor of Philosophy in Engineering'),
('Doctorate', 'Business', 'Doctor of Business Administration'),
('Doctorate', 'Education', 'Doctor of Philosophy in Education');

-- Insert sample business user
INSERT IGNORE INTO users (first_name, last_name, email, phone, password, user_type, is_verified) 
VALUES ('John', 'Business', '<EMAIL>', '***********', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'business', 1);

-- Insert sample business profile
INSERT IGNORE INTO business_profiles (user_id, company_name, business_description, business_address, mayors_permit, dti_number, is_approved) 
SELECT id, 'Sample Company Inc.', 'A leading technology company in Midsayap', 'Midsayap, North Cotabato', 'MP-2024-001', 'DTI-001-2024', 1 
FROM users WHERE email = '<EMAIL>';

-- Insert sample jobseeker user
INSERT IGNORE INTO users (first_name, last_name, email, phone, password, user_type, is_verified) 
VALUES ('Jane', 'Jobseeker', '<EMAIL>', '***********', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'jobseeker', 1);

-- Insert sample jobseeker profile
INSERT IGNORE INTO jobseeker_profiles (user_id, education_level, degree_course, skills, experience_years, location, expected_salary) 
SELECT id, 'Bachelor', 'Bachelor of Science in Information Technology', 'PHP, JavaScript, HTML, CSS, MySQL', 2, 'Midsayap, Cotabato', 25000.00 
FROM users WHERE email = '<EMAIL>';

-- Create uploads directories (this will be handled by PHP)
-- The PHP application will create these directories as needed
