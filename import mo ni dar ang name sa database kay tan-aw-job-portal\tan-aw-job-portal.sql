-- p<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> SQL Dump
-- version 5.2.1
-- https://www.phpmyadmin.net/
--
-- Host: 127.0.0.1
-- Generation Time: Jul 31, 2025 at 04:44 AM
-- Server version: 10.4.32-MariaDB
-- PHP Version: 8.2.12

SET SQL_MODE = "NO_AUTO_VALUE_ON_ZERO";
START TRANSACTION;
SET time_zone = "+00:00";


/*!40101 SET @OLD_CHARACTER_SET_CLIENT=@@CHARACTER_SET_CLIENT */;
/*!40101 SET @OLD_CHARACTER_SET_RESULTS=@@CHARACTER_SET_RESULTS */;
/*!40101 SET @OLD_COLLATION_CONNECTION=@@COLLATION_CONNECTION */;
/*!40101 SET NAMES utf8mb4 */;

--
-- Database: `tan-aw-job-portal`
--

-- --------------------------------------------------------

--
-- Table structure for table `business_profiles`
--

CREATE TABLE `business_profiles` (
  `id` int(11) NOT NULL,
  `user_id` int(11) NOT NULL,
  `company_name` varchar(255) NOT NULL,
  `business_description` text DEFAULT NULL,
  `business_address` text DEFAULT NULL,
  `mayors_permit` varchar(255) DEFAULT NULL,
  `dti_number` varchar(255) DEFAULT NULL,
  `is_approved` tinyint(1) DEFAULT 0,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `business_profiles`
--

INSERT INTO `business_profiles` (`id`, `user_id`, `company_name`, `business_description`, `business_address`, `mayors_permit`, `dti_number`, `is_approved`, `created_at`, `updated_at`) VALUES
(1, 2, 'Tech Solutions Inc.', 'Leading technology solutions provider', '123 Tech Street, Cebu City', 'MP-2024-001', 'DTI-001-2024', 1, '2025-07-24 07:20:59', '2025-07-24 07:20:59'),
(2, 5, 'Test Company Inc.', '', '', '', '', 0, '2025-07-27 02:55:53', '2025-07-27 02:55:53'),
(3, 11, 'Chowking', 'FastFood', 'Poblacion 6', '**********', '4365768564554', 1, '2025-07-28 07:23:58', '2025-07-28 07:24:50');

-- --------------------------------------------------------

--
-- Table structure for table `course_categories`
--

CREATE TABLE `course_categories` (
  `id` int(11) NOT NULL,
  `category_name` varchar(100) NOT NULL,
  `description` text DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `course_categories`
--

INSERT INTO `course_categories` (`id`, `category_name`, `description`, `created_at`) VALUES
(1, 'Information Technology', 'Computer Science, IT, Software Engineering, etc.', '2025-07-28 10:32:08'),
(2, 'Business & Management', 'Business Administration, Management, Marketing, etc.', '2025-07-28 10:32:08'),
(3, 'Engineering', 'Civil, Electrical, Mechanical, Computer Engineering, etc.', '2025-07-28 10:32:08'),
(4, 'Education', 'Teaching degrees, Education Management, etc.', '2025-07-28 10:32:08'),
(5, 'Healthcare', 'Nursing, Medical Technology, Pharmacy, etc.', '2025-07-28 10:32:08'),
(6, 'Arts & Humanities', 'Literature, History, Philosophy, etc.', '2025-07-28 10:32:08'),
(7, 'Science', 'Biology, Chemistry, Physics, Mathematics, etc.', '2025-07-28 10:32:08'),
(8, 'Vocational', 'Technical courses, Trade skills, etc.', '2025-07-28 10:32:08'),
(9, 'High School', 'High school graduates', '2025-07-28 10:32:08'),
(10, 'Other', 'Other educational backgrounds', '2025-07-28 10:32:08');

-- --------------------------------------------------------

--
-- Table structure for table `degrees`
--

CREATE TABLE `degrees` (
  `id` int(11) NOT NULL,
  `level` varchar(50) NOT NULL,
  `category` varchar(100) NOT NULL,
  `course_name` varchar(255) NOT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `degrees`
--

INSERT INTO `degrees` (`id`, `level`, `category`, `course_name`, `created_at`) VALUES
(1, 'High School', 'General Education', 'High School Graduate', '2025-07-24 07:20:59'),
(2, 'Vocational', 'Information Technology', 'Computer Programming', '2025-07-24 07:20:59'),
(3, 'Vocational', 'Information Technology', 'Computer Systems Servicing', '2025-07-24 07:20:59'),
(4, 'Vocational', 'Business', 'Office Administration', '2025-07-24 07:20:59'),
(5, 'Vocational', 'Automotive', 'Automotive Servicing', '2025-07-24 07:20:59'),
(6, 'Bachelor', 'Information Technology', 'Bachelor of Science in Information Technology', '2025-07-24 07:20:59'),
(7, 'Bachelor', 'Information Technology', 'Bachelor of Science in Computer Science', '2025-07-24 07:20:59'),
(8, 'Bachelor', 'Business', 'Bachelor of Science in Business Administration', '2025-07-24 07:20:59'),
(9, 'Bachelor', 'Engineering', 'Bachelor of Science in Civil Engineering', '2025-07-24 07:20:59'),
(10, 'Bachelor', 'Engineering', 'Bachelor of Science in Electrical Engineering', '2025-07-24 07:20:59'),
(11, 'Bachelor', 'Education', 'Bachelor of Elementary Education', '2025-07-24 07:20:59'),
(12, 'Bachelor', 'Education', 'Bachelor of Secondary Education', '2025-07-24 07:20:59'),
(13, 'Bachelor', 'Nursing', 'Bachelor of Science in Nursing', '2025-07-24 07:20:59'),
(14, 'Master', 'Business', 'Master of Business Administration', '2025-07-24 07:20:59'),
(15, 'Master', 'Information Technology', 'Master of Science in Information Technology', '2025-07-24 07:20:59'),
(16, 'Doctorate', 'Education', 'Doctor of Philosophy in Education', '2025-07-24 07:20:59'),
(17, 'Doctorate', 'Business', 'Doctor of Business Administration', '2025-07-24 07:20:59');

-- --------------------------------------------------------

--
-- Table structure for table `jobseeker_posts`
--

CREATE TABLE `jobseeker_posts` (
  `id` int(11) NOT NULL,
  `jobseeker_id` int(11) NOT NULL,
  `title` varchar(255) NOT NULL,
  `description` text NOT NULL,
  `skills` text DEFAULT NULL,
  `availability` enum('immediately','within_week','within_month','flexible') DEFAULT 'flexible',
  `preferred_salary_min` decimal(10,2) DEFAULT NULL,
  `preferred_salary_max` decimal(10,2) DEFAULT NULL,
  `preferred_location` varchar(255) DEFAULT NULL,
  `is_active` tinyint(1) DEFAULT 1,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Table structure for table `jobseeker_profiles`
--

CREATE TABLE `jobseeker_profiles` (
  `id` int(11) NOT NULL,
  `user_id` int(11) NOT NULL,
  `education_level` varchar(100) DEFAULT NULL,
  `degree_course` varchar(255) DEFAULT NULL,
  `skills` text DEFAULT NULL,
  `experience_years` int(11) DEFAULT 0,
  `location` varchar(255) DEFAULT NULL,
  `expected_salary` decimal(10,2) DEFAULT NULL,
  `resume_file` varchar(255) DEFAULT NULL,
  `resume_path` varchar(500) DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `jobseeker_profiles`
--

INSERT INTO `jobseeker_profiles` (`id`, `user_id`, `education_level`, `degree_course`, `skills`, `experience_years`, `location`, `expected_salary`, `resume_file`, `resume_path`, `created_at`, `updated_at`) VALUES
(1, 3, '', '', 'PHP, JavaScript, HTML, CSS, MySQL', 2, 'Midsayap, Cotabato', 20000.00, 'resume_3_1753760751.pdf', NULL, '2025-07-24 07:20:59', '2025-07-29 03:45:51'),
(2, 4, 'Bachelor', 'Bachelor of Science in Information Technology', NULL, 0, 'Midsayap, Cotabato', 20000.00, NULL, NULL, '2025-07-24 07:25:26', '2025-07-29 03:45:35'),
(3, 6, 'Bachelor', 'bsit', NULL, 0, 'Midsayap, Cotabato', 20000.00, NULL, NULL, '2025-07-27 02:57:15', '2025-07-29 03:45:35'),
(4, 7, 'Bachelor', 'bsitt', NULL, 0, 'Midsayap, Cotabato', 20000.00, NULL, NULL, '2025-07-27 02:59:22', '2025-07-29 03:45:35');

-- --------------------------------------------------------

--
-- Table structure for table `job_applications`
--

CREATE TABLE `job_applications` (
  `id` int(11) NOT NULL,
  `job_id` int(11) NOT NULL,
  `jobseeker_id` int(11) NOT NULL,
  `status` enum('pending','reviewed','accepted','rejected') DEFAULT 'pending',
  `cover_letter` text DEFAULT NULL,
  `applied_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Table structure for table `job_categories`
--

CREATE TABLE `job_categories` (
  `id` int(11) NOT NULL,
  `category_name` varchar(100) NOT NULL,
  `description` text DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `job_categories`
--

INSERT INTO `job_categories` (`id`, `category_name`, `description`, `created_at`) VALUES
(1, 'Information Technology', 'Software development, IT support, system administration', '2025-07-28 10:32:08'),
(2, 'Business & Finance', 'Accounting, finance, business management, administration', '2025-07-28 10:32:08'),
(3, 'Sales & Marketing', 'Sales, marketing, customer service, retail', '2025-07-28 10:32:08'),
(4, 'Engineering', 'Civil, electrical, mechanical, software engineering', '2025-07-28 10:32:08'),
(5, 'Education', 'Teaching, training, educational administration', '2025-07-28 10:32:08'),
(6, 'Healthcare', 'Nursing, medical technology, healthcare administration', '2025-07-28 10:32:08'),
(7, 'Manufacturing', 'Production, quality control, industrial work', '2025-07-28 10:32:08'),
(8, 'Service Industry', 'Food service, hospitality, customer service', '2025-07-28 10:32:08'),
(9, 'Skilled Trades', 'Construction, automotive, electrical work', '2025-07-28 10:32:08'),
(10, 'Administrative', 'Office work, data entry, clerical positions', '2025-07-28 10:32:08'),
(11, 'Creative & Design', 'Graphic design, content creation, multimedia', '2025-07-28 10:32:08'),
(12, 'Other', 'Other job categories', '2025-07-28 10:32:08');

-- --------------------------------------------------------

--
-- Table structure for table `job_comments`
--

CREATE TABLE `job_comments` (
  `id` int(11) NOT NULL,
  `job_id` int(11) NOT NULL,
  `user_id` int(11) NOT NULL,
  `comment` text NOT NULL,
  `parent_id` int(11) DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `job_comments`
--

INSERT INTO `job_comments` (`id`, `job_id`, `user_id`, `comment`, `parent_id`, `created_at`) VALUES
(3, 1, 7, 'i want it', NULL, '2025-07-27 03:05:34'),
(5, 1, 1, 'yo', 3, '2025-07-29 03:22:35'),
(6, 1, 1, 'nice', NULL, '2025-07-29 03:23:44'),
(7, 1, 1, 'hmmmm', NULL, '2025-07-29 03:24:59');

-- --------------------------------------------------------

--
-- Table structure for table `job_inquiries`
--

CREATE TABLE `job_inquiries` (
  `id` int(11) NOT NULL,
  `job_id` int(11) NOT NULL,
  `user_id` int(11) NOT NULL,
  `inquiry_text` text NOT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Table structure for table `job_posts`
--

CREATE TABLE `job_posts` (
  `id` int(11) NOT NULL,
  `business_id` int(11) NOT NULL,
  `title` varchar(255) NOT NULL,
  `description` text NOT NULL,
  `requirements` text DEFAULT NULL,
  `salary_min` decimal(10,2) DEFAULT NULL,
  `salary_max` decimal(10,2) DEFAULT NULL,
  `location` varchar(255) DEFAULT NULL,
  `job_type` enum('full-time','part-time','contract','internship') DEFAULT 'full-time',
  `skills_required` text DEFAULT NULL,
  `is_active` tinyint(1) DEFAULT 1,
  `job_image` varchar(500) DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  `status` enum('pending','approved','rejected','archived') DEFAULT 'pending',
  `slots_available` int(11) DEFAULT 1
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `job_posts`
--

INSERT INTO `job_posts` (`id`, `business_id`, `title`, `description`, `requirements`, `salary_min`, `salary_max`, `location`, `job_type`, `skills_required`, `is_active`, `job_image`, `created_at`, `updated_at`, `status`, `slots_available`) VALUES
(1, 2, 'Web Developer', 'We are looking for a skilled web developer to join our team.', 'Bachelor degree in IT or related field, 2+ years experience', 25000.00, 35000.00, 'Cebu City', 'full-time', 'PHP, JavaScript, HTML, CSS, MySQL', 1, NULL, '2025-07-24 07:20:59', '2025-07-27 00:17:28', 'approved', 1),
(2, 2, 'Graphic Designer', 'Creative graphic designer needed for marketing materials.', 'Portfolio required, experience with Adobe Creative Suite', 20000.00, 30000.00, 'Cebu City', 'full-time', 'Photoshop, Illustrator, InDesign', 1, NULL, '2025-07-24 07:20:59', '2025-07-24 07:20:59', 'pending', 1),
(3, 11, 'Cashier', 'Cashier', 'Accounting related course', 8.00, 800.00, 'Poblacion 6', 'part-time', 'Experienced Cashier', 1, '6887263f2d879.jpg', '2025-07-28 07:26:55', '2025-07-28 07:27:38', 'approved', 1);

-- --------------------------------------------------------

--
-- Table structure for table `qualification_cache`
--

CREATE TABLE `qualification_cache` (
  `id` int(11) NOT NULL,
  `jobseeker_id` int(11) NOT NULL,
  `job_id` int(11) NOT NULL,
  `qualification_percentage` int(11) NOT NULL,
  `calculation_date` timestamp NOT NULL DEFAULT current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `qualification_cache`
--

INSERT INTO `qualification_cache` (`id`, `jobseeker_id`, `job_id`, `qualification_percentage`, `calculation_date`) VALUES
(1, 3, 3, 70, '2025-07-28 10:36:13'),
(2, 3, 1, 100, '2025-07-28 10:36:13');

-- --------------------------------------------------------

--
-- Table structure for table `qualification_matrix`
--

CREATE TABLE `qualification_matrix` (
  `id` int(11) NOT NULL,
  `course_category` varchar(100) NOT NULL,
  `job_category` varchar(100) NOT NULL,
  `qualification_percentage` int(11) NOT NULL CHECK (`qualification_percentage` >= 0 and `qualification_percentage` <= 100),
  `reasoning` text DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `qualification_matrix`
--

INSERT INTO `qualification_matrix` (`id`, `course_category`, `job_category`, `qualification_percentage`, `reasoning`, `created_at`, `updated_at`) VALUES
(1, 'Information Technology', 'Information Technology', 100, 'Perfect match - direct field alignment', '2025-07-28 10:32:08', '2025-07-28 10:32:08'),
(2, 'Information Technology', 'Business & Finance', 70, 'IT skills valuable for business systems and data analysis', '2025-07-28 10:32:08', '2025-07-28 10:32:08'),
(3, 'Information Technology', 'Sales & Marketing', 60, 'Technical knowledge helps in tech product sales', '2025-07-28 10:32:08', '2025-07-28 10:32:08'),
(4, 'Information Technology', 'Engineering', 80, 'Strong technical foundation applicable to engineering', '2025-07-28 10:32:08', '2025-07-28 10:32:08'),
(5, 'Information Technology', 'Education', 65, 'Can teach IT subjects and use technology in education', '2025-07-28 10:32:08', '2025-07-28 10:32:08'),
(6, 'Information Technology', 'Healthcare', 50, 'IT skills useful for healthcare systems and data management', '2025-07-28 10:32:08', '2025-07-28 10:32:08'),
(7, 'Information Technology', 'Manufacturing', 55, 'Technical skills applicable to manufacturing systems', '2025-07-28 10:32:08', '2025-07-28 10:32:08'),
(8, 'Information Technology', 'Service Industry', 45, 'Basic computer literacy and problem-solving skills', '2025-07-28 10:32:08', '2025-07-28 10:32:08'),
(9, 'Information Technology', 'Skilled Trades', 40, 'Some technical skills transferable', '2025-07-28 10:32:08', '2025-07-28 10:32:08'),
(10, 'Information Technology', 'Administrative', 75, 'Strong computer and software skills', '2025-07-28 10:32:08', '2025-07-28 10:32:08'),
(11, 'Information Technology', 'Creative & Design', 70, 'Technical skills valuable for digital design', '2025-07-28 10:32:08', '2025-07-28 10:32:08'),
(12, 'Information Technology', 'Other', 50, 'General technical and problem-solving skills', '2025-07-28 10:32:08', '2025-07-28 10:32:08'),
(13, 'Business & Management', 'Information Technology', 40, 'Business process knowledge valuable for IT projects', '2025-07-28 10:32:08', '2025-07-28 10:32:08'),
(14, 'Business & Management', 'Business & Finance', 100, 'Perfect match - direct field alignment', '2025-07-28 10:32:08', '2025-07-28 10:32:08'),
(15, 'Business & Management', 'Sales & Marketing', 90, 'Strong business foundation for sales and marketing', '2025-07-28 10:32:08', '2025-07-28 10:32:08'),
(16, 'Business & Management', 'Engineering', 30, 'Limited technical background for engineering roles', '2025-07-28 10:32:08', '2025-07-28 10:32:08'),
(17, 'Business & Management', 'Education', 70, 'Business knowledge valuable for business education', '2025-07-28 10:32:08', '2025-07-28 10:32:08'),
(18, 'Business & Management', 'Healthcare', 60, 'Business skills applicable to healthcare administration', '2025-07-28 10:32:08', '2025-07-28 10:32:08'),
(19, 'Business & Management', 'Manufacturing', 65, 'Business process and management skills valuable', '2025-07-28 10:32:08', '2025-07-28 10:32:08'),
(20, 'Business & Management', 'Service Industry', 80, 'Strong customer service and management skills', '2025-07-28 10:32:08', '2025-07-28 10:32:08'),
(21, 'Business & Management', 'Skilled Trades', 25, 'Limited technical skills for trade work', '2025-07-28 10:32:08', '2025-07-28 10:32:08'),
(22, 'Business & Management', 'Administrative', 85, 'Strong administrative and organizational skills', '2025-07-28 10:32:08', '2025-07-28 10:32:08'),
(23, 'Business & Management', 'Creative & Design', 50, 'Business knowledge helps with project management', '2025-07-28 10:32:08', '2025-07-28 10:32:08'),
(24, 'Business & Management', 'Other', 60, 'General business and management skills', '2025-07-28 10:32:08', '2025-07-28 10:32:08'),
(25, 'Engineering', 'Information Technology', 85, 'Strong technical and problem-solving skills', '2025-07-28 10:32:08', '2025-07-28 10:32:08'),
(26, 'Engineering', 'Business & Finance', 50, 'Analytical skills valuable for business analysis', '2025-07-28 10:32:08', '2025-07-28 10:32:08'),
(27, 'Engineering', 'Sales & Marketing', 40, 'Technical knowledge helps in technical product sales', '2025-07-28 10:32:08', '2025-07-28 10:32:08'),
(28, 'Engineering', 'Engineering', 100, 'Perfect match - direct field alignment', '2025-07-28 10:32:08', '2025-07-28 10:32:08'),
(29, 'Engineering', 'Education', 60, 'Can teach engineering subjects and technical courses', '2025-07-28 10:32:08', '2025-07-28 10:32:08'),
(30, 'Engineering', 'Healthcare', 45, 'Some technical skills applicable to medical technology', '2025-07-28 10:32:08', '2025-07-28 10:32:08'),
(31, 'Engineering', 'Manufacturing', 90, 'Strong technical skills for manufacturing processes', '2025-07-28 10:32:08', '2025-07-28 10:32:08'),
(32, 'Engineering', 'Service Industry', 35, 'Limited customer service background', '2025-07-28 10:32:08', '2025-07-28 10:32:08'),
(33, 'Engineering', 'Skilled Trades', 70, 'Technical skills transferable to trade work', '2025-07-28 10:32:08', '2025-07-28 10:32:08'),
(34, 'Engineering', 'Administrative', 55, 'Analytical and organizational skills', '2025-07-28 10:32:08', '2025-07-28 10:32:08'),
(35, 'Engineering', 'Creative & Design', 45, 'Some technical skills applicable to design', '2025-07-28 10:32:08', '2025-07-28 10:32:08'),
(36, 'Engineering', 'Other', 50, 'Strong analytical and problem-solving skills', '2025-07-28 10:32:08', '2025-07-28 10:32:08'),
(37, 'Education', 'Information Technology', 30, 'Limited technical background', '2025-07-28 10:32:08', '2025-07-28 10:32:08'),
(38, 'Education', 'Business & Finance', 50, 'Communication and organizational skills valuable', '2025-07-28 10:32:08', '2025-07-28 10:32:08'),
(39, 'Education', 'Sales & Marketing', 70, 'Strong communication and presentation skills', '2025-07-28 10:32:08', '2025-07-28 10:32:08'),
(40, 'Education', 'Engineering', 25, 'Limited technical background', '2025-07-28 10:32:08', '2025-07-28 10:32:08'),
(41, 'Education', 'Education', 100, 'Perfect match - direct field alignment', '2025-07-28 10:32:08', '2025-07-28 10:32:08'),
(42, 'Education', 'Healthcare', 60, 'Communication skills valuable for patient education', '2025-07-28 10:32:08', '2025-07-28 10:32:08'),
(43, 'Education', 'Manufacturing', 40, 'Limited technical background', '2025-07-28 10:32:08', '2025-07-28 10:32:08'),
(44, 'Education', 'Service Industry', 75, 'Strong communication and customer service skills', '2025-07-28 10:32:08', '2025-07-28 10:32:08'),
(45, 'Education', 'Skilled Trades', 30, 'Limited technical background', '2025-07-28 10:32:08', '2025-07-28 10:32:08'),
(46, 'Education', 'Administrative', 80, 'Strong organizational and communication skills', '2025-07-28 10:32:08', '2025-07-28 10:32:08'),
(47, 'Education', 'Creative & Design', 60, 'Communication skills valuable for content creation', '2025-07-28 10:32:08', '2025-07-28 10:32:08'),
(48, 'Education', 'Other', 55, 'Strong communication and organizational skills', '2025-07-28 10:32:08', '2025-07-28 10:32:08'),
(49, 'Healthcare', 'Information Technology', 35, 'Limited technical background', '2025-07-28 10:32:08', '2025-07-28 10:32:08'),
(50, 'Healthcare', 'Business & Finance', 45, 'Some organizational skills', '2025-07-28 10:32:08', '2025-07-28 10:32:08'),
(51, 'Healthcare', 'Sales & Marketing', 60, 'Communication skills valuable for healthcare sales', '2025-07-28 10:32:08', '2025-07-28 10:32:08'),
(52, 'Healthcare', 'Engineering', 25, 'Limited technical background', '2025-07-28 10:32:08', '2025-07-28 10:32:08'),
(53, 'Healthcare', 'Education', 70, 'Can teach healthcare subjects and patient education', '2025-07-28 10:32:08', '2025-07-28 10:32:08'),
(54, 'Healthcare', 'Healthcare', 100, 'Perfect match - direct field alignment', '2025-07-28 10:32:08', '2025-07-28 10:32:08'),
(55, 'Healthcare', 'Manufacturing', 40, 'Limited technical background', '2025-07-28 10:32:08', '2025-07-28 10:32:08'),
(56, 'Healthcare', 'Service Industry', 65, 'Strong patient care and communication skills', '2025-07-28 10:32:08', '2025-07-28 10:32:08'),
(57, 'Healthcare', 'Skilled Trades', 25, 'Limited technical background', '2025-07-28 10:32:08', '2025-07-28 10:32:08'),
(58, 'Healthcare', 'Administrative', 60, 'Organizational skills from healthcare work', '2025-07-28 10:32:08', '2025-07-28 10:32:08'),
(59, 'Healthcare', 'Creative & Design', 40, 'Limited creative background', '2025-07-28 10:32:08', '2025-07-28 10:32:08'),
(60, 'Healthcare', 'Other', 50, 'Strong care and communication skills', '2025-07-28 10:32:08', '2025-07-28 10:32:08'),
(61, 'Arts & Humanities', 'Information Technology', 25, 'Limited technical background', '2025-07-28 10:32:08', '2025-07-28 10:32:08'),
(62, 'Arts & Humanities', 'Business & Finance', 45, 'Some analytical and communication skills', '2025-07-28 10:32:08', '2025-07-28 10:32:08'),
(63, 'Arts & Humanities', 'Sales & Marketing', 75, 'Strong communication and creative skills', '2025-07-28 10:32:08', '2025-07-28 10:32:08'),
(64, 'Arts & Humanities', 'Engineering', 20, 'Limited technical background', '2025-07-28 10:32:08', '2025-07-28 10:32:08'),
(65, 'Arts & Humanities', 'Education', 85, 'Strong communication and teaching skills', '2025-07-28 10:32:08', '2025-07-28 10:32:08'),
(66, 'Arts & Humanities', 'Healthcare', 50, 'Communication skills valuable', '2025-07-28 10:32:08', '2025-07-28 10:32:08'),
(67, 'Arts & Humanities', 'Manufacturing', 30, 'Limited technical background', '2025-07-28 10:32:08', '2025-07-28 10:32:08'),
(68, 'Arts & Humanities', 'Service Industry', 70, 'Strong communication and customer service skills', '2025-07-28 10:32:08', '2025-07-28 10:32:08'),
(69, 'Arts & Humanities', 'Skilled Trades', 25, 'Limited technical background', '2025-07-28 10:32:08', '2025-07-28 10:32:08'),
(70, 'Arts & Humanities', 'Administrative', 65, 'Strong communication and organizational skills', '2025-07-28 10:32:08', '2025-07-28 10:32:08'),
(71, 'Arts & Humanities', 'Creative & Design', 90, 'Strong creative and artistic skills', '2025-07-28 10:32:08', '2025-07-28 10:32:08'),
(72, 'Arts & Humanities', 'Other', 55, 'Strong communication and creative skills', '2025-07-28 10:32:08', '2025-07-28 10:32:08'),
(73, 'Science', 'Information Technology', 70, 'Strong analytical and technical skills', '2025-07-28 10:32:08', '2025-07-28 10:32:08'),
(74, 'Science', 'Business & Finance', 55, 'Analytical skills valuable for business analysis', '2025-07-28 10:32:08', '2025-07-28 10:32:08'),
(75, 'Science', 'Sales & Marketing', 50, 'Some communication skills', '2025-07-28 10:32:08', '2025-07-28 10:32:08'),
(76, 'Science', 'Engineering', 75, 'Strong technical and analytical skills', '2025-07-28 10:32:08', '2025-07-28 10:32:08'),
(77, 'Science', 'Education', 80, 'Can teach science subjects effectively', '2025-07-28 10:32:08', '2025-07-28 10:32:08'),
(78, 'Science', 'Healthcare', 85, 'Strong scientific background for healthcare', '2025-07-28 10:32:08', '2025-07-28 10:32:08'),
(79, 'Science', 'Manufacturing', 60, 'Technical and analytical skills valuable', '2025-07-28 10:32:08', '2025-07-28 10:32:08'),
(80, 'Science', 'Service Industry', 45, 'Limited customer service background', '2025-07-28 10:32:08', '2025-07-28 10:32:08'),
(81, 'Science', 'Skilled Trades', 40, 'Some technical skills', '2025-07-28 10:32:08', '2025-07-28 10:32:08'),
(82, 'Science', 'Administrative', 60, 'Analytical and organizational skills', '2025-07-28 10:32:08', '2025-07-28 10:32:08'),
(83, 'Science', 'Creative & Design', 50, 'Some analytical skills applicable', '2025-07-28 10:32:08', '2025-07-28 10:32:08'),
(84, 'Science', 'Other', 55, 'Strong analytical and problem-solving skills', '2025-07-28 10:32:08', '2025-07-28 10:32:08'),
(85, 'Vocational', 'Information Technology', 60, 'Technical skills from vocational training', '2025-07-28 10:32:08', '2025-07-28 10:32:08'),
(86, 'Vocational', 'Business & Finance', 40, 'Some organizational skills', '2025-07-28 10:32:08', '2025-07-28 10:32:08'),
(87, 'Vocational', 'Sales & Marketing', 55, 'Some communication skills', '2025-07-28 10:32:08', '2025-07-28 10:32:08'),
(88, 'Vocational', 'Engineering', 50, 'Some technical skills from vocational training', '2025-07-28 10:32:08', '2025-07-28 10:32:08'),
(89, 'Vocational', 'Education', 45, 'Can teach vocational subjects', '2025-07-28 10:32:08', '2025-07-28 10:32:08'),
(90, 'Vocational', 'Healthcare', 50, 'Some technical skills applicable', '2025-07-28 10:32:08', '2025-07-28 10:32:08'),
(91, 'Vocational', 'Manufacturing', 75, 'Strong technical skills from vocational training', '2025-07-28 10:32:08', '2025-07-28 10:32:08'),
(92, 'Vocational', 'Service Industry', 60, 'Practical skills valuable for service work', '2025-07-28 10:32:08', '2025-07-28 10:32:08'),
(93, 'Vocational', 'Skilled Trades', 85, 'Strong technical skills from vocational training', '2025-07-28 10:32:08', '2025-07-28 10:32:08'),
(94, 'Vocational', 'Administrative', 50, 'Some organizational skills', '2025-07-28 10:32:08', '2025-07-28 10:32:08'),
(95, 'Vocational', 'Creative & Design', 45, 'Some technical skills applicable', '2025-07-28 10:32:08', '2025-07-28 10:32:08'),
(96, 'Vocational', 'Other', 55, 'Practical skills from vocational training', '2025-07-28 10:32:08', '2025-07-28 10:32:08'),
(97, 'High School', 'Information Technology', 30, 'Basic computer literacy', '2025-07-28 10:32:08', '2025-07-28 10:32:08'),
(98, 'High School', 'Business & Finance', 35, 'Basic organizational skills', '2025-07-28 10:32:08', '2025-07-28 10:32:08'),
(99, 'High School', 'Sales & Marketing', 50, 'Basic communication skills', '2025-07-28 10:32:08', '2025-07-28 10:32:08'),
(100, 'High School', 'Engineering', 20, 'Limited technical background', '2025-07-28 10:32:08', '2025-07-28 10:32:08'),
(101, 'High School', 'Education', 40, 'Basic communication skills', '2025-07-28 10:32:08', '2025-07-28 10:32:08'),
(102, 'High School', 'Healthcare', 35, 'Basic care skills', '2025-07-28 10:32:08', '2025-07-28 10:32:08'),
(103, 'High School', 'Manufacturing', 45, 'Basic technical skills', '2025-07-28 10:32:08', '2025-07-28 10:32:08'),
(104, 'High School', 'Service Industry', 55, 'Basic customer service skills', '2025-07-28 10:32:08', '2025-07-28 10:32:08'),
(105, 'High School', 'Skilled Trades', 40, 'Basic technical skills', '2025-07-28 10:32:08', '2025-07-28 10:32:08'),
(106, 'High School', 'Administrative', 45, 'Basic organizational skills', '2025-07-28 10:32:08', '2025-07-28 10:32:08'),
(107, 'High School', 'Creative & Design', 40, 'Basic creative skills', '2025-07-28 10:32:08', '2025-07-28 10:32:08'),
(108, 'High School', 'Other', 40, 'Basic general skills', '2025-07-28 10:32:08', '2025-07-28 10:32:08'),
(109, 'Other', 'Information Technology', 40, 'General technical skills', '2025-07-28 10:32:08', '2025-07-28 10:32:08'),
(110, 'Other', 'Business & Finance', 45, 'General business skills', '2025-07-28 10:32:08', '2025-07-28 10:32:08'),
(111, 'Other', 'Sales & Marketing', 50, 'General communication skills', '2025-07-28 10:32:08', '2025-07-28 10:32:08'),
(112, 'Other', 'Engineering', 30, 'Limited technical background', '2025-07-28 10:32:08', '2025-07-28 10:32:08'),
(113, 'Other', 'Education', 50, 'General communication skills', '2025-07-28 10:32:08', '2025-07-28 10:32:08'),
(114, 'Other', 'Healthcare', 40, 'General care skills', '2025-07-28 10:32:08', '2025-07-28 10:32:08'),
(115, 'Other', 'Manufacturing', 45, 'General technical skills', '2025-07-28 10:32:08', '2025-07-28 10:32:08'),
(116, 'Other', 'Service Industry', 50, 'General service skills', '2025-07-28 10:32:08', '2025-07-28 10:32:08'),
(117, 'Other', 'Skilled Trades', 35, 'Limited technical skills', '2025-07-28 10:32:08', '2025-07-28 10:32:08'),
(118, 'Other', 'Administrative', 50, 'General organizational skills', '2025-07-28 10:32:08', '2025-07-28 10:32:08'),
(119, 'Other', 'Creative & Design', 45, 'General creative skills', '2025-07-28 10:32:08', '2025-07-28 10:32:08'),
(120, 'Other', 'Other', 50, 'General skills applicable to various fields', '2025-07-28 10:32:08', '2025-07-28 10:32:08');

-- --------------------------------------------------------

--
-- Table structure for table `users`
--

CREATE TABLE `users` (
  `id` int(11) NOT NULL,
  `first_name` varchar(100) NOT NULL,
  `last_name` varchar(100) NOT NULL,
  `email` varchar(255) NOT NULL,
  `phone` varchar(20) NOT NULL,
  `password` varchar(255) NOT NULL,
  `user_type` enum('admin','business','jobseeker') NOT NULL,
  `is_verified` tinyint(1) DEFAULT 0,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `users`
--

INSERT INTO `users` (`id`, `first_name`, `last_name`, `email`, `phone`, `password`, `user_type`, `is_verified`, `created_at`, `updated_at`) VALUES
(1, 'Admin', 'User', '<EMAIL>', '***********', 'password', 'admin', 1, '2025-07-24 07:20:59', '2025-07-28 02:57:09'),
(2, 'Business', 'Owner', '<EMAIL>', '***********', 'password', 'business', 1, '2025-07-24 07:20:59', '2025-07-28 02:57:09'),
(3, 'Job', 'Seeker', '<EMAIL>', '***********', 'password', 'jobseeker', 1, '2025-07-24 07:20:59', '2025-07-28 02:57:09'),
(4, 'Darylle', 'Tandug', '<EMAIL>', '***********', 'password', 'jobseeker', 1, '2025-07-24 07:25:26', '2025-07-28 02:57:09'),
(5, 'Jane', 'Smith', '<EMAIL>', '***********', 'password', 'business', 0, '2025-07-27 02:55:53', '2025-07-28 02:57:09'),
(6, 'John', 'Doe', '<EMAIL>', '***********', 'password', 'jobseeker', 1, '2025-07-27 02:57:15', '2025-07-28 02:57:10'),
(7, 'Kevenzyrel', 'Pascioles', '<EMAIL>', '***********', 'password', 'jobseeker', 1, '2025-07-27 02:59:22', '2025-07-28 02:57:10'),
(8, 'Admin', 'Admin', '<EMAIL>', '***********', 'admintanaw123', 'admin', NULL, '2025-07-28 02:52:47', '2025-07-28 02:52:47'),
(11, 'Marlon', 'Causing', '<EMAIL>', '***********', 'Password123', 'business', 1, '2025-07-28 07:23:58', '2025-07-28 07:24:50');

--
-- Indexes for dumped tables
--

--
-- Indexes for table `business_profiles`
--
ALTER TABLE `business_profiles`
  ADD PRIMARY KEY (`id`),
  ADD KEY `user_id` (`user_id`);

--
-- Indexes for table `course_categories`
--
ALTER TABLE `course_categories`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `category_name` (`category_name`);

--
-- Indexes for table `degrees`
--
ALTER TABLE `degrees`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `jobseeker_posts`
--
ALTER TABLE `jobseeker_posts`
  ADD PRIMARY KEY (`id`),
  ADD KEY `jobseeker_id` (`jobseeker_id`);

--
-- Indexes for table `jobseeker_profiles`
--
ALTER TABLE `jobseeker_profiles`
  ADD PRIMARY KEY (`id`),
  ADD KEY `user_id` (`user_id`);

--
-- Indexes for table `job_applications`
--
ALTER TABLE `job_applications`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `unique_application` (`job_id`,`jobseeker_id`),
  ADD KEY `jobseeker_id` (`jobseeker_id`);

--
-- Indexes for table `job_categories`
--
ALTER TABLE `job_categories`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `category_name` (`category_name`);

--
-- Indexes for table `job_comments`
--
ALTER TABLE `job_comments`
  ADD PRIMARY KEY (`id`),
  ADD KEY `job_id` (`job_id`),
  ADD KEY `user_id` (`user_id`),
  ADD KEY `parent_id` (`parent_id`);

--
-- Indexes for table `job_inquiries`
--
ALTER TABLE `job_inquiries`
  ADD PRIMARY KEY (`id`),
  ADD KEY `job_id` (`job_id`),
  ADD KEY `user_id` (`user_id`);

--
-- Indexes for table `job_posts`
--
ALTER TABLE `job_posts`
  ADD PRIMARY KEY (`id`),
  ADD KEY `business_id` (`business_id`);

--
-- Indexes for table `qualification_cache`
--
ALTER TABLE `qualification_cache`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `unique_jobseeker_job` (`jobseeker_id`,`job_id`),
  ADD KEY `idx_qualification_cache_jobseeker` (`jobseeker_id`),
  ADD KEY `idx_qualification_cache_job` (`job_id`),
  ADD KEY `idx_qualification_cache_date` (`calculation_date`);

--
-- Indexes for table `qualification_matrix`
--
ALTER TABLE `qualification_matrix`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `unique_course_job` (`course_category`,`job_category`),
  ADD KEY `idx_qualification_matrix_course` (`course_category`),
  ADD KEY `idx_qualification_matrix_job` (`job_category`);

--
-- Indexes for table `users`
--
ALTER TABLE `users`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `email` (`email`);

--
-- AUTO_INCREMENT for dumped tables
--

--
-- AUTO_INCREMENT for table `business_profiles`
--
ALTER TABLE `business_profiles`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=4;

--
-- AUTO_INCREMENT for table `course_categories`
--
ALTER TABLE `course_categories`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=12;

--
-- AUTO_INCREMENT for table `degrees`
--
ALTER TABLE `degrees`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=18;

--
-- AUTO_INCREMENT for table `jobseeker_posts`
--
ALTER TABLE `jobseeker_posts`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `jobseeker_profiles`
--
ALTER TABLE `jobseeker_profiles`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=5;

--
-- AUTO_INCREMENT for table `job_applications`
--
ALTER TABLE `job_applications`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `job_categories`
--
ALTER TABLE `job_categories`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=14;

--
-- AUTO_INCREMENT for table `job_comments`
--
ALTER TABLE `job_comments`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=8;

--
-- AUTO_INCREMENT for table `job_inquiries`
--
ALTER TABLE `job_inquiries`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `job_posts`
--
ALTER TABLE `job_posts`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=4;

--
-- AUTO_INCREMENT for table `qualification_cache`
--
ALTER TABLE `qualification_cache`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=3;

--
-- AUTO_INCREMENT for table `qualification_matrix`
--
ALTER TABLE `qualification_matrix`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=122;

--
-- AUTO_INCREMENT for table `users`
--
ALTER TABLE `users`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=12;

--
-- Constraints for dumped tables
--

--
-- Constraints for table `business_profiles`
--
ALTER TABLE `business_profiles`
  ADD CONSTRAINT `business_profiles_ibfk_1` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE;

--
-- Constraints for table `jobseeker_posts`
--
ALTER TABLE `jobseeker_posts`
  ADD CONSTRAINT `jobseeker_posts_ibfk_1` FOREIGN KEY (`jobseeker_id`) REFERENCES `users` (`id`) ON DELETE CASCADE;

--
-- Constraints for table `jobseeker_profiles`
--
ALTER TABLE `jobseeker_profiles`
  ADD CONSTRAINT `jobseeker_profiles_ibfk_1` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE;

--
-- Constraints for table `job_applications`
--
ALTER TABLE `job_applications`
  ADD CONSTRAINT `job_applications_ibfk_1` FOREIGN KEY (`job_id`) REFERENCES `job_posts` (`id`) ON DELETE CASCADE,
  ADD CONSTRAINT `job_applications_ibfk_2` FOREIGN KEY (`jobseeker_id`) REFERENCES `users` (`id`) ON DELETE CASCADE;

--
-- Constraints for table `job_comments`
--
ALTER TABLE `job_comments`
  ADD CONSTRAINT `job_comments_ibfk_1` FOREIGN KEY (`job_id`) REFERENCES `job_posts` (`id`) ON DELETE CASCADE,
  ADD CONSTRAINT `job_comments_ibfk_2` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE,
  ADD CONSTRAINT `job_comments_ibfk_3` FOREIGN KEY (`parent_id`) REFERENCES `job_comments` (`id`) ON DELETE CASCADE;

--
-- Constraints for table `job_inquiries`
--
ALTER TABLE `job_inquiries`
  ADD CONSTRAINT `job_inquiries_ibfk_1` FOREIGN KEY (`job_id`) REFERENCES `job_posts` (`id`) ON DELETE CASCADE,
  ADD CONSTRAINT `job_inquiries_ibfk_2` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE;

--
-- Constraints for table `job_posts`
--
ALTER TABLE `job_posts`
  ADD CONSTRAINT `job_posts_ibfk_1` FOREIGN KEY (`business_id`) REFERENCES `users` (`id`) ON DELETE CASCADE;

--
-- Constraints for table `qualification_cache`
--
ALTER TABLE `qualification_cache`
  ADD CONSTRAINT `qualification_cache_ibfk_1` FOREIGN KEY (`jobseeker_id`) REFERENCES `users` (`id`) ON DELETE CASCADE,
  ADD CONSTRAINT `qualification_cache_ibfk_2` FOREIGN KEY (`job_id`) REFERENCES `job_posts` (`id`) ON DELETE CASCADE;
COMMIT;

/*!40101 SET CHARACTER_SET_CLIENT=@OLD_CHARACTER_SET_CLIENT */;
/*!40101 SET CHARACTER_SET_RESULTS=@OLD_CHARACTER_SET_RESULTS */;
/*!40101 SET COLLATION_CONNECTION=@OLD_COLLATION_CONNECTION */;
