<?php
require_once __DIR__ . '/../config/config.php';

// Check if user is logged in and is admin
if (!isLoggedIn() || getUserType() !== 'admin') {
    redirect('/auth/login.php');
}

try {
    $database = new Database();
    $db = $database->getConnection();
    
    // Initialize variables
    $jobs = [];
    $total_jobs = 0;
    $status_filter = sanitize($_GET['status'] ?? '');
    $search = sanitize($_GET['search'] ?? '');
    $page = max(1, (int)($_GET['page'] ?? 1));
    $per_page = 20;
    $offset = ($page - 1) * $per_page;
    
    // Build query
    try {
        $where_conditions = [];
        $params = [];
        
        if (!empty($status_filter)) {
            $where_conditions[] = "jp.status = ?";
            $params[] = $status_filter;
        }
        
        if (!empty($search)) {
            $where_conditions[] = "(jp.title LIKE ? OR bp.company_name LIKE ? OR jp.location LIKE ?)";
            $search_term = "%$search%";
            $params[] = $search_term;
            $params[] = $search_term;
            $params[] = $search_term;
        }
        
        $where_clause = !empty($where_conditions) ? "WHERE " . implode(" AND ", $where_conditions) : "";
        
        // Get total count
        $count_query = "SELECT COUNT(*) as total FROM job_posts jp 
                        JOIN business_profiles bp ON jp.business_id = bp.user_id 
                        $where_clause";
        $stmt = $db->prepare($count_query);
        $stmt->execute($params);
        $result = $stmt->fetch(PDO::FETCH_ASSOC);
        $total_jobs = $result ? (int)$result['total'] : 0;
        
        // Get jobs with pagination
        $query = "SELECT jp.*, bp.company_name, bp.business_address, u.first_name, u.last_name, u.email
                  FROM job_posts jp 
                  JOIN business_profiles bp ON jp.business_id = bp.user_id 
                  JOIN users u ON bp.user_id = u.id 
                  $where_clause 
                  ORDER BY jp.created_at DESC 
                  LIMIT ? OFFSET ?";
        
        $params[] = $per_page;
        $params[] = $offset;
        
        $stmt = $db->prepare($query);
        $stmt->execute($params);
        $jobs = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
    } catch (Exception $e) {
        error_log("Jobs query error: " . $e->getMessage());
        $jobs = [];
        $total_jobs = 0;
    }
    
    // Calculate pagination
    $total_pages = ceil($total_jobs / $per_page);
    
} catch (Exception $e) {
    error_log("Admin jobs page error: " . $e->getMessage());
    $_SESSION['error'] = "Database error occurred.";
    $jobs = [];
    $total_jobs = 0;
    $total_pages = 0;
}

$page_title = "Job Management";
include __DIR__ . '/../includes/header.php';
?>

<main class="py-4">
    <div class="container-fluid">
        <!-- Success/Error Messages -->
        <?php if (isset($_SESSION['success'])): ?>
        <div class="alert alert-success alert-dismissible fade show" role="alert">
            <i class="fas fa-check-circle me-2"></i>
            <?php echo htmlspecialchars($_SESSION['success']); unset($_SESSION['success']); ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
        <?php endif; ?>

        <?php if (isset($_SESSION['error'])): ?>
        <div class="alert alert-danger alert-dismissible fade show" role="alert">
            <i class="fas fa-exclamation-circle me-2"></i>
            <?php echo htmlspecialchars($_SESSION['error']); unset($_SESSION['error']); ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
        <?php endif; ?>

        <!-- Header -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h1 class="h3 mb-0">Job Management</h1>
                        <p class="text-muted">
                            <?php if (!empty($status_filter)): ?>
                                Showing <?php echo ucfirst($status_filter); ?> Jobs
                            <?php else: ?>
                                All Jobs
                            <?php endif; ?>
                            (<?php echo number_format($total_jobs); ?> total)
                        </p>
                    </div>
                    <div class="d-flex gap-2">
                        <a href="dashboard.php" class="btn btn-outline-primary">
                            <i class="fas fa-arrow-left me-1"></i>Back to Dashboard
                        </a>
                    </div>
                </div>
            </div>
        </div>

        <!-- Filters -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-body">
                        <form method="GET" class="row g-3">
                            <div class="col-md-4">
                                <label for="search" class="form-label">Search Jobs</label>
                                <input type="text" class="form-control" id="search" name="search" 
                                       placeholder="Job title, company, or location" value="<?php echo htmlspecialchars($search); ?>">
                            </div>
                            <div class="col-md-3">
                                <label for="status" class="form-label">Status</label>
                                <select class="form-select" id="status" name="status">
                                    <option value="">All Status</option>
                                    <option value="pending" <?php echo $status_filter === 'pending' ? 'selected' : ''; ?>>Pending</option>
                                    <option value="approved" <?php echo $status_filter === 'approved' ? 'selected' : ''; ?>>Approved</option>
                                    <option value="rejected" <?php echo $status_filter === 'rejected' ? 'selected' : ''; ?>>Rejected</option>
                                </select>
                            </div>
                            <div class="col-md-3 d-flex align-items-end">
                                <button type="submit" class="btn btn-primary me-2">
                                    <i class="fas fa-search"></i> Filter
                                </button>
                                <a href="jobs.php" class="btn btn-outline-secondary">Clear</a>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>

        <!-- Jobs Table -->
        <div class="row">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">Jobs (<?php echo count($jobs); ?> of <?php echo number_format($total_jobs); ?>)</h5>
                    </div>
                    <div class="card-body">
                        <?php if (empty($jobs)): ?>
                            <div class="text-center py-5">
                                <i class="fas fa-briefcase fa-3x text-muted mb-3"></i>
                                <h5>No Jobs Found</h5>
                                <p class="text-muted">
                                    <?php if (!empty($status_filter) || !empty($search)): ?>
                                        Try adjusting your filters or <a href="jobs.php">view all jobs</a>.
                                    <?php else: ?>
                                        No jobs have been posted yet.
                                    <?php endif; ?>
                                </p>
                            </div>
                        <?php else: ?>
                            <div class="table-responsive">
                                <table class="table table-hover">
                                    <thead>
                                        <tr>
                                            <th>Job Details</th>
                                            <th>Company</th>
                                            <th>Salary</th>
                                            <th>Status</th>
                                            <th>Posted</th>
                                            <th>Actions</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <?php foreach ($jobs as $job): ?>
                                        <tr>
                                            <td>
                                                <div>
                                                    <strong><?php echo htmlspecialchars($job['title']); ?></strong>
                                                    <br>
                                                    <small class="text-muted">
                                                        <i class="fas fa-map-marker-alt me-1"></i><?php echo htmlspecialchars($job['location']); ?>
                                                        <span class="mx-2">•</span>
                                                        <span class="badge bg-info"><?php echo ucfirst(str_replace('-', ' ', $job['job_type'])); ?></span>
                                                    </small>
                                                    <br>
                                                    <small class="text-muted">
                                                        <?php echo htmlspecialchars(substr($job['description'], 0, 100)) . (strlen($job['description']) > 100 ? '...' : ''); ?>
                                                    </small>
                                                </div>
                                            </td>
                                            <td>
                                                <strong><?php echo htmlspecialchars($job['company_name']); ?></strong>
                                                <br>
                                                <small class="text-muted">
                                                    <?php echo htmlspecialchars($job['first_name'] . ' ' . $job['last_name']); ?>
                                                </small>
                                                <?php if (!empty($job['business_address'])): ?>
                                                <br>
                                                <small class="text-muted">
                                                    <i class="fas fa-map-marker-alt me-1"></i><?php echo htmlspecialchars($job['business_address']); ?>
                                                </small>
                                                <?php endif; ?>
                                            </td>
                                            <td>
                                                <?php if ($job['salary_min'] && $job['salary_max']): ?>
                                                    <span class="text-success">
                                                        ₱<?php echo number_format($job['salary_min']); ?> - ₱<?php echo number_format($job['salary_max']); ?>
                                                    </span>
                                                <?php else: ?>
                                                    <span class="text-muted">Not specified</span>
                                                <?php endif; ?>
                                            </td>
                                            <td>
                                                <?php
                                                $status_colors = [
                                                    'pending' => 'warning',
                                                    'approved' => 'success',
                                                    'rejected' => 'danger'
                                                ];
                                                $status_icons = [
                                                    'pending' => 'clock',
                                                    'approved' => 'check',
                                                    'rejected' => 'times'
                                                ];
                                                ?>
                                                <span class="badge bg-<?php echo $status_colors[$job['status']] ?? 'secondary'; ?>">
                                                    <i class="fas fa-<?php echo $status_icons[$job['status']] ?? 'question'; ?> me-1"></i>
                                                    <?php echo ucfirst($job['status']); ?>
                                                </span>
                                            </td>
                                            <td>
                                                <small class="text-muted">
                                                    <?php echo date('M j, Y', strtotime($job['created_at'])); ?>
                                                    <br>
                                                    <?php echo date('g:i A', strtotime($job['created_at'])); ?>
                                                </small>
                                            </td>
                                            <td>
                                                <div class="btn-group btn-group-sm">
                                                    <a href="../job-details.php?id=<?php echo $job['id']; ?>" 
                                                       class="btn btn-outline-primary" target="_blank" title="View Job">
                                                        <i class="fas fa-eye"></i>
                                                    </a>
                                                    <?php if ($job['status'] === 'pending'): ?>
                                                    <button class="btn btn-outline-success" 
                                                            onclick="approveJob(<?php echo $job['id']; ?>)" 
                                                            title="Approve">
                                                        <i class="fas fa-check"></i>
                                                    </button>
                                                    <button class="btn btn-outline-danger" 
                                                            onclick="rejectJob(<?php echo $job['id']; ?>)" 
                                                            title="Reject">
                                                        <i class="fas fa-times"></i>
                                                    </button>
                                                    <?php endif; ?>
                                                    <button class="btn btn-outline-danger" 
                                                            onclick="deleteJob(<?php echo $job['id']; ?>)" 
                                                            title="Delete">
                                                        <i class="fas fa-trash"></i>
                                                    </button>
                                                </div>
                                            </td>
                                        </tr>
                                        <?php endforeach; ?>
                                    </tbody>
                                </table>
                            </div>

                            <!-- Pagination -->
                            <?php if ($total_pages > 1): ?>
                            <nav aria-label="Jobs pagination" class="mt-4">
                                <ul class="pagination justify-content-center">
                                    <?php if ($page > 1): ?>
                                    <li class="page-item">
                                        <a class="page-link" href="?<?php echo http_build_query(array_merge($_GET, ['page' => $page - 1])); ?>">
                                            <i class="fas fa-chevron-left"></i> Previous
                                        </a>
                                    </li>
                                    <?php endif; ?>
                                    
                                    <?php for ($i = max(1, $page - 2); $i <= min($total_pages, $page + 2); $i++): ?>
                                    <li class="page-item <?php echo $i === $page ? 'active' : ''; ?>">
                                        <a class="page-link" href="?<?php echo http_build_query(array_merge($_GET, ['page' => $i])); ?>">
                                            <?php echo $i; ?>
                                        </a>
                                    </li>
                                    <?php endfor; ?>
                                    
                                    <?php if ($page < $total_pages): ?>
                                    <li class="page-item">
                                        <a class="page-link" href="?<?php echo http_build_query(array_merge($_GET, ['page' => $page + 1])); ?>">
                                            Next <i class="fas fa-chevron-right"></i>
                                        </a>
                                    </li>
                                    <?php endif; ?>
                                </ul>
                            </nav>
                            <?php endif; ?>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>
    </div>
</main>

<script>
function approveJob(jobId) {
    if (confirm('Are you sure you want to approve this job post?')) {
        // Implement approval logic
        alert('Job approval - to be implemented for job ID: ' + jobId);
    }
}

function rejectJob(jobId) {
    if (confirm('Are you sure you want to reject this job post?')) {
        // Implement rejection logic
        alert('Job rejection - to be implemented for job ID: ' + jobId);
    }
}

function deleteJob(jobId) {
    if (confirm('Are you sure you want to delete this job post? This action cannot be undone.')) {
        // Implement deletion logic
        alert('Job deletion - to be implemented for job ID: ' + jobId);
    }
}
</script>

<?php include __DIR__ . '/../includes/footer.php'; ?> 