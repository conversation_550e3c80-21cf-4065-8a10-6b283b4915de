-- Add DTI number to business profiles
ALTER TABLE business_profiles ADD COLUMN dti_number VARCHAR(255) AFTER mayors_permit;

-- Update existing records to have empty DTI numbers
UPDATE business_profiles SET dti_number = '' WHERE dti_number IS NULL;

-- Create sample data for testing
INSERT IGNORE INTO users (email, password, user_type, first_name, last_name, phone, is_verified) VALUES
('<EMAIL>', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'business', 'John', 'Doe', '***********', 0),
('<EMAIL>', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'business', '<PERSON>', '<PERSON>', '***********', 1),
('<EMAIL>', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'jobseeker', '<PERSON>', '<PERSON>', '***********', 1),
('<EMAIL>', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'jobseeker', 'Sarah', '<PERSON>', '***********', 1);

-- Insert business profiles for sample businesses
INSERT IGNORE INTO business_profiles (user_id, company_name, business_description, mayors_permit, dti_number, business_address, industry, company_size, is_approved) 
SELECT u.id, 'Tech Solutions Inc', 'Leading technology solutions provider', 'MP-2024-001', 'DTI-2024-001', 'Midsayap, Cotabato', 'Technology', '10-50', 0
FROM users u WHERE u.email = '<EMAIL>';

INSERT IGNORE INTO business_profiles (user_id, company_name, business_description, mayors_permit, dti_number, business_address, industry, company_size, is_approved) 
SELECT u.id, 'Green Foods Corp', 'Organic food processing company', 'MP-2024-002', 'DTI-2024-002', 'Midsayap, Cotabato', 'Food & Beverage', '50-100', 1
FROM users u WHERE u.email = '<EMAIL>';

-- Insert job seeker profiles
INSERT IGNORE INTO jobseeker_profiles (user_id, skills, experience_years, education_level, location, expected_salary) 
SELECT u.id, 'PHP, JavaScript, HTML, CSS, MySQL', 3, 'Bachelor\'s Degree', 'Midsayap, Cotabato', 25000.00
FROM users u WHERE u.email = '<EMAIL>';

INSERT IGNORE INTO jobseeker_profiles (user_id, skills, experience_years, education_level, location, expected_salary) 
SELECT u.id, 'Marketing, Social Media, Content Writing', 2, 'Bachelor\'s Degree', 'Midsayap, Cotabato', 20000.00
FROM users u WHERE u.email = '<EMAIL>';

-- Insert sample job posts
INSERT IGNORE INTO job_posts (business_id, title, description, requirements, skills_required, salary_min, salary_max, location, job_type, slots_available, status) 
SELECT u.id, 'Web Developer', 'We are looking for a skilled web developer to join our team.', 'Bachelor\'s degree in Computer Science or related field', 'PHP, JavaScript, HTML, CSS, MySQL', 20000.00, 30000.00, 'Midsayap, Cotabato', 'full-time', 2, 'pending'
FROM users u WHERE u.email = '<EMAIL>';

INSERT IGNORE INTO job_posts (business_id, title, description, requirements, skills_required, salary_min, salary_max, location, job_type, slots_available, status) 
SELECT u.id, 'Marketing Specialist', 'Join our marketing team to help grow our brand.', 'Bachelor\'s degree in Marketing or related field', 'Marketing, Social Media, Content Writing', 18000.00, 25000.00, 'Midsayap, Cotabato', 'full-time', 1, 'approved'
FROM users u WHERE u.email = '<EMAIL>';
