<?php
require_once __DIR__ . '/config/config.php';
require_once __DIR__ . '/lib/QualificationAnalyzer.php';

$page_title = "Qualification System Test";
include __DIR__ . '/includes/header.php';
?>

<main class="py-5">
    <div class="container">
        <div class="row">
            <div class="col-12">
                <div class="card shadow-sm">
                    <div class="card-header">
                        <h3><i class="fas fa-test me-2"></i>Qualification System Test</h3>
                    </div>
                    <div class="card-body">
                        <?php
                        try {
                            $database = new Database();
                            $db = $database->getConnection();
                            $qualificationAnalyzer = new QualificationAnalyzer($db);
                            
                            echo "<h4>Database Connection: ✅ Success</h4>";
                            
                            // Test course category detection
                            echo "<h5>Course Category Detection Test:</h5>";
                            $test_courses = [
                                'Bachelor of Science in Information Technology',
                                'Bachelor of Science in Business Administration',
                                'Bachelor of Science in Civil Engineering',
                                'Bachelor of Science in Nursing',
                                'High School Graduate'
                            ];
                            
                            foreach ($test_courses as $course) {
                                $category = $qualificationAnalyzer->getCourseCategory($course);
                                echo "<p><strong>$course</strong> → <span class='badge bg-info'>$category</span></p>";
                            }
                            
                            // Test job category detection
                            echo "<h5>Job Category Detection Test:</h5>";
                            $test_jobs = [
                                'Web Developer' => ['requirements' => 'PHP, JavaScript', 'skills' => 'HTML, CSS'],
                                'Sales Representative' => ['requirements' => 'Sales experience', 'skills' => 'Communication'],
                                'Civil Engineer' => ['requirements' => 'Engineering degree', 'skills' => 'AutoCAD'],
                                'Nurse' => ['requirements' => 'Nursing degree', 'skills' => 'Patient care'],
                                'Cashier' => ['requirements' => 'Customer service', 'skills' => 'Cash handling']
                            ];
                            
                            foreach ($test_jobs as $job_title => $job_data) {
                                $category = $qualificationAnalyzer->getJobCategory($job_title, $job_data['requirements'], $job_data['skills']);
                                echo "<p><strong>$job_title</strong> → <span class='badge bg-success'>$category</span></p>";
                            }
                            
                            // Test qualification calculation
                            echo "<h5>Qualification Calculation Test:</h5>";
                            
                            // Get a sample job seeker and job
                            $query = "SELECT jp.user_id, jp.degree_course FROM jobseeker_profiles jp LIMIT 1";
                            $stmt = $db->prepare($query);
                            $stmt->execute();
                            $jobseeker = $stmt->fetch(PDO::FETCH_ASSOC);
                            
                            $query = "SELECT jp.id, jp.title, jp.requirements, jp.skills_required FROM job_posts jp WHERE jp.status = 'approved' LIMIT 1";
                            $stmt = $db->prepare($query);
                            $stmt->execute();
                            $job = $stmt->fetch(PDO::FETCH_ASSOC);
                            
                            if ($jobseeker && $job) {
                                $qualification = $qualificationAnalyzer->calculateQualification($jobseeker['user_id'], $job['id']);
                                $color = $qualificationAnalyzer->getQualificationColor($qualification);
                                $text = $qualificationAnalyzer->getQualificationText($qualification);
                                
                                echo "<div class='alert alert-info'>";
                                echo "<h6>Sample Calculation:</h6>";
                                echo "<p><strong>Job Seeker:</strong> " . htmlspecialchars($jobseeker['degree_course']) . "</p>";
                                echo "<p><strong>Job:</strong> " . htmlspecialchars($job['title']) . "</p>";
                                echo "<p><strong>Qualification:</strong> <span class='badge bg-$color'>$qualification%</span> - $text</p>";
                                echo "</div>";
                            }
                            
                            // Test database tables
                            echo "<h5>Database Tables Check:</h5>";
                            $tables = ['qualification_matrix', 'course_categories', 'job_categories', 'qualification_cache'];
                            
                            foreach ($tables as $table) {
                                $query = "SELECT COUNT(*) as count FROM $table";
                                $stmt = $db->prepare($query);
                                $stmt->execute();
                                $result = $stmt->fetch(PDO::FETCH_ASSOC);
                                $count = $result['count'];
                                
                                echo "<p><strong>$table:</strong> <span class='badge bg-primary'>$count records</span></p>";
                            }
                            
                            echo "<div class='alert alert-success mt-4'>";
                            echo "<h6>✅ Qualification System Status: OPERATIONAL</h6>";
                            echo "<p>The qualification analysis system is working correctly!</p>";
                            echo "</div>";
                            
                        } catch (Exception $e) {
                            echo "<div class='alert alert-danger'>";
                            echo "<h6>❌ Error:</h6>";
                            echo "<p>" . htmlspecialchars($e->getMessage()) . "</p>";
                            echo "</div>";
                        }
                        ?>
                        
                        <div class="mt-4">
                            <a href="jobs.php" class="btn btn-primary">
                                <i class="fas fa-search me-1"></i>Test on Jobs Page
                            </a>
                            <a href="admin/qualification-matrix.php" class="btn btn-secondary">
                                <i class="fas fa-cog me-1"></i>Manage Matrix
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</main>

<?php include __DIR__ . '/includes/footer.php'; ?> 