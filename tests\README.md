# Tan-aw Job Portal - Comment System Tests

This directory contains Playwright end-to-end tests for the enhanced comment system functionality.

## Test Structure

### Test Files

- **`comments.spec.js`** - Core comment functionality tests
  - Comment display and styling
  - Reply functionality
  - Like button interactions
  - Form handling
  - Animations

- **`comments-dark-mode.spec.js`** - Dark mode specific tests
  - Theme switching
  - Dark mode styling verification
  - Contrast and readability checks
  - Functionality preservation in dark mode

- **`comments-responsive.spec.js`** - Responsive design tests
  - Mobile layout adaptation
  - Tablet and desktop layouts
  - Touch target sizing
  - Text wrapping and overflow handling

### Configuration Files

- **`playwright.config.js`** - Main Playwright configuration
- **`package.json`** - Test dependencies and scripts

## Setup Instructions

### Prerequisites

1. **Node.js** (version 16 or higher)
2. **PHP** with a local server running the job portal
3. **Database** with test data (at least one job post with comments)

### Installation

1. Navigate to the tests directory:
   ```bash
   cd tests
   ```

2. Install dependencies:
   ```bash
   npm install
   ```

3. Install Playwright browsers:
   ```bash
   npm run install
   ```

### Running Tests

#### All Tests
```bash
npm test
```

#### Specific Test Suites
```bash
# Comment functionality tests
npm run test:comments

# Dark mode tests
npm run test:comments-dark

# Mobile-specific tests
npm run test:comments-mobile
```

#### Interactive Mode
```bash
# Run tests with browser visible
npm run test:headed

# Debug mode (step through tests)
npm run test:debug

# UI mode (interactive test runner)
npm run test:ui
```

#### View Test Reports
```bash
npm run report
```

## Test Coverage

### Core Functionality
- ✅ Comment display and structure
- ✅ Right-aligned layout
- ✅ Reply system
- ✅ Like button functionality
- ✅ Form interactions
- ✅ Auto-resizing textareas

### Visual Design
- ✅ Modern styling verification
- ✅ Hover effects
- ✅ Animations and transitions
- ✅ Proper spacing and typography
- ✅ Visual hierarchy

### Dark Mode
- ✅ Theme switching
- ✅ Color scheme verification
- ✅ Contrast ratios
- ✅ Functionality preservation
- ✅ Smooth transitions

### Responsive Design
- ✅ Mobile layout (375px and below)
- ✅ Tablet layout (768px)
- ✅ Desktop layout (1200px+)
- ✅ Touch target sizing
- ✅ Text wrapping
- ✅ Cross-device functionality

### Browser Compatibility
- ✅ Chrome/Chromium
- ✅ Firefox
- ✅ Safari/WebKit
- ✅ Mobile Chrome
- ✅ Mobile Safari

## Test Data Requirements

For tests to run successfully, ensure your test database has:

1. **At least one job post** with ID=1
2. **Comments on that job post** (for testing display)
3. **Replies to comments** (for testing nested functionality)
4. **User authentication** (for testing logged-in features)

## Customization

### Adjusting Test URLs

If your job IDs are different, update the URLs in test files:

```javascript
// In each test file, change:
await page.goto('/job-details.php?id=1');
// To your actual job ID:
await page.goto('/job-details.php?id=YOUR_JOB_ID');
```

### Adding New Tests

1. Create a new `.spec.js` file in the tests directory
2. Follow the existing pattern:
   ```javascript
   const { test, expect } = require('@playwright/test');
   
   test.describe('Your Test Suite', () => {
     test.beforeEach(async ({ page }) => {
       await page.goto('/your-page.php');
     });
     
     test('should do something', async ({ page }) => {
       // Your test code
     });
   });
   ```

### Environment Configuration

You can customize the test environment by modifying `playwright.config.js`:

- **Base URL**: Change `baseURL` for different environments
- **Browsers**: Add/remove browser configurations
- **Timeouts**: Adjust test timeouts
- **Screenshots**: Configure screenshot capture
- **Videos**: Configure video recording

## Troubleshooting

### Common Issues

1. **Tests failing due to missing elements**
   - Ensure your database has test data
   - Check if the job ID exists
   - Verify user authentication state

2. **Timeout errors**
   - Increase timeout values in test configuration
   - Check if your local server is running
   - Verify network connectivity

3. **Animation tests failing**
   - Ensure CSS animations are enabled
   - Check if reduced motion is disabled
   - Verify animation timing

### Debug Mode

Use debug mode to step through failing tests:
```bash
npm run test:debug -- --grep "failing test name"
```

### Screenshots and Videos

Failed tests automatically capture:
- Screenshots at the point of failure
- Videos of the entire test run
- Browser traces for detailed debugging

These are saved in the `test-results` directory.

## Contributing

When adding new comment features:

1. **Write tests first** (TDD approach)
2. **Test across all supported browsers**
3. **Include responsive design tests**
4. **Test both light and dark modes**
5. **Verify accessibility compliance**

## Performance Considerations

Tests are configured to:
- Run in parallel for faster execution
- Use network idle state for reliable loading
- Capture minimal artifacts to save space
- Retry failed tests automatically

For CI/CD environments, tests are optimized for:
- Headless execution
- Reduced parallelism
- Comprehensive error reporting
- Artifact collection
