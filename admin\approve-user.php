<?php
require_once __DIR__ . '/../config/config.php';

// Check if user is logged in and is admin
if (!isLoggedIn() || getUserType() !== 'admin') {
    http_response_code(403);
    echo json_encode(['success' => false, 'message' => 'Unauthorized access']);
    exit();
}

// Check if it's a POST request
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode(['success' => false, 'message' => 'Method not allowed']);
    exit();
}

try {
    $database = new Database();
    $db = $database->getConnection();
    
    // Get JSON input
    $input = json_decode(file_get_contents('php://input'), true);
    $user_id = (int)($input['user_id'] ?? 0);
    
    if ($user_id <= 0) {
        echo json_encode(['success' => false, 'message' => 'Invalid user ID']);
        exit();
    }
    
    // Check if user exists and is a business
    $query = "SELECT user_type FROM users WHERE id = ?";
    $stmt = $db->prepare($query);
    $stmt->execute([$user_id]);
    $user = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if (!$user) {
        echo json_encode(['success' => false, 'message' => 'User not found']);
        exit();
    }
    
    if ($user['user_type'] !== 'business') {
        echo json_encode(['success' => false, 'message' => 'Only business accounts can be approved']);
        exit();
    }
    
    $db->beginTransaction();
    
    try {
        // Update user verification status
        $query = "UPDATE users SET is_verified = 1 WHERE id = ? AND user_type = 'business'";
        $stmt = $db->prepare($query);
        $stmt->execute([$user_id]);
        
        // Update business profile approval status
        $query = "UPDATE business_profiles SET is_approved = 1 WHERE user_id = ?";
        $stmt = $db->prepare($query);
        $stmt->execute([$user_id]);
        
        $db->commit();
        
        echo json_encode(['success' => true, 'message' => 'User approved successfully']);
        
    } catch (Exception $e) {
        $db->rollback();
        throw $e;
    }
    
} catch (Exception $e) {
    error_log("User approval error: " . $e->getMessage());
    echo json_encode(['success' => false, 'message' => 'Failed to approve user']);
}
?> 