-- Simple fix for jobseeker_profiles table
-- This script only adds columns that don't already exist

USE `tan-aw-job-portal`;

-- Add expected_salary field if it doesn't exist
ALTER TABLE jobseeker_profiles 
ADD COLUMN IF NOT EXISTS expected_salary DECIMAL(10,2) DEFAULT NULL AFTER location;

-- Add resume_file field if it doesn't exist  
ALTER TABLE jobseeker_profiles 
ADD COLUMN IF NOT EXISTS resume_file VARCHAR(255) DEFAULT NULL AFTER expected_salary;

-- Update existing records with sample data (optional)
UPDATE jobseeker_profiles 
SET location = 'Midsayap, Cotabato', 
    expected_salary = 25000.00 
WHERE location IS NULL AND user_id IN (3, 4, 6, 7);

-- Show the current table structure
DESCRIBE jobseeker_profiles; 