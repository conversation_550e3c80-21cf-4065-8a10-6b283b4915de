<?php
require_once __DIR__ . '/config/config.php';

// Check if user is logged in and is business
if (!isLoggedIn() || getUserType() !== 'business') {
    redirect('/auth/login.php');
}

$user_id = (int)($_GET['user_id'] ?? 0);
$error = '';
$jobseeker = null;

if ($user_id <= 0) {
    $error = 'Invalid user ID.';
} else {
    try {
        $database = new Database();
        $db = $database->getConnection();
        
        // Get job seeker profile with user information
        $query = "SELECT u.*, jp.* 
                  FROM users u 
                  LEFT JOIN jobseeker_profiles jp ON u.id = jp.user_id 
                  WHERE u.id = ? AND u.user_type = 'jobseeker'";
        $stmt = $db->prepare($query);
        $stmt->execute([$user_id]);
        $jobseeker = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if (!$jobseeker) {
            $error = 'Job seeker profile not found.';
        }
        
    } catch (Exception $e) {
        error_log("Job seeker profile view error: " . $e->getMessage());
        $error = 'An error occurred while loading the profile.';
    }
}

$page_title = $jobseeker ? htmlspecialchars($jobseeker['first_name'] . ' ' . $jobseeker['last_name']) . ' - Profile' : 'Job Seeker Profile';
include __DIR__ . '/includes/header.php';
?>

<main class="py-4">
    <div class="container">
        <?php if ($error): ?>
        <div class="alert alert-danger alert-dismissible fade show" role="alert">
            <i class="fas fa-exclamation-triangle me-2"></i><?php echo htmlspecialchars($error); ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
        <?php endif; ?>

        <?php if ($error && !$jobseeker): ?>
        <div class="row">
            <div class="col-12">
                <div class="alert alert-danger" role="alert">
                    <h4 class="alert-heading">Error</h4>
                    <p><?php echo htmlspecialchars($error); ?></p>
                    <hr>
                    <a href="javascript:history.back()" class="btn btn-primary">Go Back</a>
                </div>
            </div>
        </div>
        <?php elseif ($jobseeker): ?>
        <div class="row">
            <!-- Profile Information -->
            <div class="col-lg-8">
                <div class="card shadow mb-4">
                    <div class="card-header">
                        <div class="d-flex justify-content-between align-items-center">
                            <h4 class="mb-0">
                                <i class="fas fa-user me-2"></i>Job Seeker Profile
                            </h4>
                            <a href="javascript:history.back()" class="btn btn-outline-secondary btn-sm">
                                <i class="fas fa-arrow-left me-1"></i>Back
                            </a>
                        </div>
                    </div>
                    <div class="card-body">
                        <!-- Basic Information -->
                        <div class="row mb-4">
                            <div class="col-md-6">
                                <h5 class="text-primary mb-3">Personal Information</h5>
                                <div class="mb-3">
                                    <small class="text-muted d-block">Full Name</small>
                                    <span class="fw-bold"><?php echo htmlspecialchars($jobseeker['first_name'] . ' ' . $jobseeker['last_name']); ?></span>
                                </div>
                                <div class="mb-3">
                                    <small class="text-muted d-block">Email</small>
                                    <span><i class="fas fa-envelope me-1"></i><?php echo htmlspecialchars($jobseeker['email']); ?></span>
                                </div>
                                <?php if (!empty($jobseeker['phone'])): ?>
                                <div class="mb-3">
                                    <small class="text-muted d-block">Phone</small>
                                    <span><i class="fas fa-phone me-1"></i><?php echo htmlspecialchars($jobseeker['phone']); ?></span>
                                </div>
                                <?php endif; ?>
                                <?php if (!empty($jobseeker['location'])): ?>
                                <div class="mb-3">
                                    <small class="text-muted d-block">Location</small>
                                    <span><i class="fas fa-map-marker-alt me-1"></i><?php echo htmlspecialchars($jobseeker['location']); ?></span>
                                </div>
                                <?php endif; ?>
                            </div>
                            <div class="col-md-6">
                                <h5 class="text-primary mb-3">Professional Information</h5>
                                <?php if (!empty($jobseeker['education_level'])): ?>
                                <div class="mb-3">
                                    <small class="text-muted d-block">Education Level</small>
                                    <span><?php echo htmlspecialchars($jobseeker['education_level']); ?></span>
                                </div>
                                <?php endif; ?>
                                <?php if (!empty($jobseeker['degree_course'])): ?>
                                <div class="mb-3">
                                    <small class="text-muted d-block">Degree/Course</small>
                                    <span><?php echo htmlspecialchars($jobseeker['degree_course']); ?></span>
                                </div>
                                <?php endif; ?>
                                <div class="mb-3">
                                    <small class="text-muted d-block">Years of Experience</small>
                                    <span><?php echo $jobseeker['experience_years'] ?? 0; ?> year(s)</span>
                                </div>
                                <?php if (!empty($jobseeker['expected_salary'])): ?>
                                <div class="mb-3">
                                    <small class="text-muted d-block">Expected Salary</small>
                                    <span class="text-success fw-bold">₱<?php echo number_format($jobseeker['expected_salary']); ?></span>
                                </div>
                                <?php endif; ?>
                            </div>
                        </div>

                        <!-- Skills Section -->
                        <?php if (!empty($jobseeker['skills'])): ?>
                        <div class="mb-4">
                            <h5 class="text-primary mb-3">Skills & Expertise</h5>
                            <div>
                                <?php 
                                $skills = explode(',', $jobseeker['skills']);
                                foreach ($skills as $skill): 
                                    $skill = trim($skill);
                                    if (!empty($skill)):
                                ?>
                                <span class="badge bg-primary me-1 mb-1"><?php echo htmlspecialchars($skill); ?></span>
                                <?php 
                                    endif;
                                endforeach; 
                                ?>
                            </div>
                        </div>
                        <?php endif; ?>

                        <!-- Resume Section -->
                        <?php if (!empty($jobseeker['resume_file'])): ?>
                        <div class="mb-4">
                            <h5 class="text-primary mb-3">Resume</h5>
                            <div class="d-flex align-items-center">
                                <i class="fas fa-file-pdf fa-2x text-danger me-3"></i>
                                <div class="flex-grow-1">
                                    <h6 class="mb-1">Resume Document</h6>
                                    <small class="text-muted">PDF format</small>
                                </div>
                                <a href="uploads/resumes/<?php echo htmlspecialchars($jobseeker['resume_file']); ?>" 
                                   class="btn btn-primary" target="_blank">
                                    <i class="fas fa-download me-1"></i>View Resume
                                </a>
                            </div>
                        </div>
                        <?php else: ?>
                        <div class="mb-4">
                            <h5 class="text-primary mb-3">Resume</h5>
                            <div class="alert alert-info">
                                <i class="fas fa-info-circle me-2"></i>
                                No resume uploaded yet.
                            </div>
                        </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>

            <!-- Contact Information Sidebar -->
            <div class="col-lg-4">
                <div class="card shadow">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="fas fa-address-card me-2"></i>Contact Information
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="mb-3">
                            <small class="text-muted d-block">Email Address</small>
                            <span><i class="fas fa-envelope me-1"></i><?php echo htmlspecialchars($jobseeker['email']); ?></span>
                        </div>
                        
                        <?php if (!empty($jobseeker['phone'])): ?>
                        <div class="mb-3">
                            <small class="text-muted d-block">Phone Number</small>
                            <span><i class="fas fa-phone me-1"></i><?php echo htmlspecialchars($jobseeker['phone']); ?></span>
                        </div>
                        <?php endif; ?>
                        
                        <?php if (!empty($jobseeker['location'])): ?>
                        <div class="mb-3">
                            <small class="text-muted d-block">Location</small>
                            <span><i class="fas fa-map-marker-alt me-1"></i><?php echo htmlspecialchars($jobseeker['location']); ?></span>
                        </div>
                        <?php endif; ?>
                        
                        <hr>
                        
                        <div class="text-center">
                            <a href="mailto:<?php echo htmlspecialchars($jobseeker['email']); ?>" class="btn btn-primary w-100 mb-2">
                                <i class="fas fa-envelope me-1"></i>Send Email
                            </a>
                            <?php if (!empty($jobseeker['phone'])): ?>
                            <a href="tel:<?php echo htmlspecialchars($jobseeker['phone']); ?>" class="btn btn-outline-primary w-100">
                                <i class="fas fa-phone me-1"></i>Call
                            </a>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <?php endif; ?>
    </div>
</main>

<?php include __DIR__ . '/includes/footer.php'; ?> 