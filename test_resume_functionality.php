<?php
require_once __DIR__ . '/config/config.php';

// Test script to verify resume functionality
echo "<h2>Resume Functionality Test</h2>";

try {
    $database = new Database();
    $db = $database->getConnection();
    
    // Test 1: Check if resume_file column exists
    echo "<h3>Test 1: Database Structure</h3>";
    $query = "DESCRIBE jobseeker_profiles";
    $stmt = $db->prepare($query);
    $stmt->execute();
    $columns = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    $hasResumeFile = false;
    foreach ($columns as $column) {
        if ($column['Field'] === 'resume_file') {
            $hasResumeFile = true;
            break;
        }
    }
    
    if ($hasResumeFile) {
        echo "<p style='color: green;'>✓ resume_file column exists</p>";
    } else {
        echo "<p style='color: red;'>✗ resume_file column missing</p>";
    }
    
    // Test 2: Check jobseeker profiles with resumes
    echo "<h3>Test 2: Jobseeker Profiles with Resumes</h3>";
    $query = "SELECT u.first_name, u.last_name, jp.resume_file 
              FROM users u 
              JOIN jobseeker_profiles jp ON u.id = jp.user_id 
              WHERE u.user_type = 'jobseeker'";
    $stmt = $db->prepare($query);
    $stmt->execute();
    $jobseekers = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    if (empty($jobseekers)) {
        echo "<p>No jobseekers found</p>";
    } else {
        echo "<table border='1' style='border-collapse: collapse;'>";
        echo "<tr><th>Name</th><th>Resume File</th><th>File Exists</th></tr>";
        
        foreach ($jobseekers as $jobseeker) {
            $resumePath = __DIR__ . '/uploads/resumes/' . $jobseeker['resume_file'];
            $fileExists = !empty($jobseeker['resume_file']) && file_exists($resumePath);
            
            echo "<tr>";
            echo "<td>{$jobseeker['first_name']} {$jobseeker['last_name']}</td>";
            echo "<td>" . ($jobseeker['resume_file'] ?: 'No resume') . "</td>";
            echo "<td>" . ($fileExists ? '✓' : '✗') . "</td>";
            echo "</tr>";
        }
        echo "</table>";
    }
    
    // Test 3: Check uploads directory
    echo "<h3>Test 3: Uploads Directory</h3>";
    $uploadsDir = __DIR__ . '/uploads/resumes/';
    if (is_dir($uploadsDir)) {
        echo "<p style='color: green;'>✓ Uploads directory exists: $uploadsDir</p>";
        
        $files = scandir($uploadsDir);
        $resumeFiles = array_filter($files, function($file) {
            return $file !== '.' && $file !== '..' && pathinfo($file, PATHINFO_EXTENSION) === 'pdf';
        });
        
        echo "<p>Resume files found: " . count($resumeFiles) . "</p>";
        if (!empty($resumeFiles)) {
            echo "<ul>";
            foreach ($resumeFiles as $file) {
                echo "<li>$file</li>";
            }
            echo "</ul>";
        }
    } else {
        echo "<p style='color: red;'>✗ Uploads directory missing: $uploadsDir</p>";
    }
    
    // Test 4: Test admin user details endpoint
    echo "<h3>Test 4: Admin User Details Endpoint</h3>";
    $query = "SELECT id FROM users WHERE user_type = 'jobseeker' LIMIT 1";
    $stmt = $db->prepare($query);
    $stmt->execute();
    $jobseeker = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if ($jobseeker) {
        $testUrl = "http://localhost/tan-aw-job-portal/admin/user-details.php?id={$jobseeker['id']}";
        echo "<p>Test URL: <a href='$testUrl' target='_blank'>$testUrl</a></p>";
        echo "<p>Note: This will only work if you're logged in as admin</p>";
    } else {
        echo "<p>No jobseekers found to test with</p>";
    }
    
} catch (Exception $e) {
    echo "<p style='color: red;'>Error: " . $e->getMessage() . "</p>";
}
?>

<style>
body { font-family: Arial, sans-serif; margin: 20px; }
table { margin: 10px 0; }
th, td { padding: 8px; text-align: left; }
</style> 