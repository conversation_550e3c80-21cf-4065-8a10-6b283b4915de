-- Add resume upload to jobseeker profiles
ALTER TABLE jobseeker_profiles ADD COLUMN IF NOT EXISTS resume_file VARCHAR(255) AFTER expected_salary;

-- Create job seeker posts table (for employees to post their availability)
CREATE TABLE IF NOT EXISTS jobseeker_posts (
    id INT AUTO_INCREMENT PRIMARY KEY,
    jobseeker_id INT NOT NULL,
    title VARCHAR(255) NOT NULL,
    description TEXT NOT NULL,
    skills_offered TEXT,
    experience_years INT DEFAULT 0,
    availability VARCHAR(50) DEFAULT 'full-time',
    expected_salary_min DECIMAL(10,2),
    expected_salary_max DECIMAL(10,2),
    location VARCHAR(255),
    status ENUM('active', 'inactive', 'hired') DEFAULT 'active',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (jobseeker_id) REFERENCES users(id) ON DELETE CASCADE
);

-- Add inquiry/comment to job applications
ALTER TABLE job_applications ADD COLUMN IF NOT EXISTS inquiry_message TEXT AFTER cover_letter;

-- Create inquiries table for job posts
CREATE TABLE IF NOT EXISTS job_inquiries (
    id INT AUTO_INCREMENT PRIMARY KEY,
    job_id INT NOT NULL,
    inquirer_id INT NOT NULL,
    inquirer_type ENUM('jobseeker', 'business', 'admin') NOT NULL,
    message TEXT NOT NULL,
    response TEXT,
    status ENUM('pending', 'responded', 'closed') DEFAULT 'pending',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (job_id) REFERENCES job_posts(id) ON DELETE CASCADE,
    FOREIGN KEY (inquirer_id) REFERENCES users(id) ON DELETE CASCADE
);

-- Add indexes for better performance
CREATE INDEX IF NOT EXISTS idx_jobseeker_posts_status ON jobseeker_posts(status);
CREATE INDEX IF NOT EXISTS idx_job_inquiries_job_id ON job_inquiries(job_id);
CREATE INDEX IF NOT EXISTS idx_job_inquiries_status ON job_inquiries(status);
