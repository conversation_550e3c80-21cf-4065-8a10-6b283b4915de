-- Add job_image column to job_posts table
ALTER TABLE job_posts ADD COLUMN job_image VARCHAR(255) NULL AFTER description;

-- Create job_comments table if it doesn't exist
CREATE TABLE IF NOT EXISTS job_comments (
    id INT AUTO_INCREMENT PRIMARY KEY,
    job_id INT NOT NULL,
    user_id INT NOT NULL,
    comment TEXT NOT NULL,
    parent_id INT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIG<PERSON> KEY (job_id) REFERENCES job_posts(id) ON DELETE CASCADE,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIG<PERSON> KEY (parent_id) REFERENCES job_comments(id) ON DELETE CASCADE
);

-- <PERSON><PERSON> uploads directories (this will be handled by <PERSON><PERSON>)
-- Make sure uploads/job_images/ directory exists
