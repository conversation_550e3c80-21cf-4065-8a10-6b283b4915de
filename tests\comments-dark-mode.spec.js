// @ts-check
const { test, expect } = require('@playwright/test');

test.describe('Comment System Dark Mode Tests', () => {
  test.beforeEach(async ({ page }) => {
    // Navigate to job details page
    await page.goto('/job-details.php?id=1');
    await page.waitForLoadState('networkidle');
  });

  test('should toggle to dark mode and verify comment styling', async ({ page }) => {
    // Find and click the theme toggle button
    const themeToggle = page.locator('#themeToggle');
    
    if (await themeToggle.count() > 0) {
      await themeToggle.click();
      
      // Wait for theme transition
      await page.waitForTimeout(500);
      
      // Check if dark theme is applied to body
      const body = page.locator('body');
      const dataTheme = await body.getAttribute('data-theme');
      expect(dataTheme).toBe('dark');
      
      // Check comment styling in dark mode
      await page.waitForSelector('.comment-item', { timeout: 5000 });
      const firstComment = page.locator('.comment-item').first();
      
      // Verify dark mode colors are applied
      const backgroundColor = await firstComment.evaluate(el => 
        window.getComputedStyle(el).backgroundColor
      );
      
      // Dark mode should have darker background
      expect(backgroundColor).not.toBe('rgb(255, 255, 255)');
    }
  });

  test('should maintain comment functionality in dark mode', async ({ page }) => {
    // Switch to dark mode
    const themeToggle = page.locator('#themeToggle');
    if (await themeToggle.count() > 0) {
      await themeToggle.click();
      await page.waitForTimeout(500);
    }
    
    // Test reply functionality in dark mode
    await page.waitForSelector('.reply-btn', { timeout: 5000 });
    const replyButton = page.locator('.reply-btn').first();
    
    await replyButton.click();
    
    const replyForm = page.locator('.reply-form').first();
    await expect(replyForm).toBeVisible();
    
    // Check if form elements are properly styled in dark mode
    const textarea = replyForm.locator('textarea');
    const textareaBackground = await textarea.evaluate(el => 
      window.getComputedStyle(el).backgroundColor
    );
    
    // Should not be white in dark mode
    expect(textareaBackground).not.toBe('rgb(255, 255, 255)');
  });

  test('should show proper hover effects in dark mode', async ({ page }) => {
    // Switch to dark mode
    const themeToggle = page.locator('#themeToggle');
    if (await themeToggle.count() > 0) {
      await themeToggle.click();
      await page.waitForTimeout(500);
    }
    
    await page.waitForSelector('.comment-item', { timeout: 5000 });
    const firstComment = page.locator('.comment-item').first();
    
    // Test hover effect in dark mode
    await firstComment.hover();
    await page.waitForTimeout(300);
    
    // Verify hover effect is applied
    const transform = await firstComment.evaluate(el => 
      window.getComputedStyle(el).transform
    );
    
    expect(transform).not.toBe('none');
  });

  test('should display proper contrast in dark mode', async ({ page }) => {
    // Switch to dark mode
    const themeToggle = page.locator('#themeToggle');
    if (await themeToggle.count() > 0) {
      await themeToggle.click();
      await page.waitForTimeout(500);
    }
    
    await page.waitForSelector('.comment-item', { timeout: 5000 });
    
    // Check text contrast in dark mode
    const commentContent = page.locator('.comment-content').first();
    const textColor = await commentContent.evaluate(el => 
      window.getComputedStyle(el).color
    );
    
    // Text should be light in dark mode
    expect(textColor).not.toBe('rgb(0, 0, 0)');
    
    // Check if text is readable (not too dark)
    const rgb = textColor.match(/rgb\((\d+), (\d+), (\d+)\)/);
    if (rgb) {
      const [, r, g, b] = rgb.map(Number);
      const brightness = (r * 299 + g * 587 + b * 114) / 1000;
      expect(brightness).toBeGreaterThan(100); // Should be reasonably bright
    }
  });

  test('should maintain button styling in dark mode', async ({ page }) => {
    // Switch to dark mode
    const themeToggle = page.locator('#themeToggle');
    if (await themeToggle.count() > 0) {
      await themeToggle.click();
      await page.waitForTimeout(500);
    }
    
    await page.waitForSelector('.reply-btn', { timeout: 5000 });
    const replyButton = page.locator('.reply-btn').first();
    
    // Check button styling in dark mode
    const buttonBackground = await replyButton.evaluate(el => 
      window.getComputedStyle(el).backgroundColor
    );
    
    const buttonBorder = await replyButton.evaluate(el => 
      window.getComputedStyle(el).borderColor
    );
    
    // Buttons should have appropriate dark mode styling
    expect(buttonBackground).not.toBe('rgb(255, 255, 255)');
    expect(buttonBorder).not.toBe('rgb(0, 0, 0)');
  });

  test('should show proper form styling in dark mode', async ({ page }) => {
    // Switch to dark mode
    const themeToggle = page.locator('#themeToggle');
    if (await themeToggle.count() > 0) {
      await themeToggle.click();
      await page.waitForTimeout(500);
    }
    
    const commentForm = page.locator('.comment-form');
    
    if (await commentForm.count() > 0) {
      // Check form background in dark mode
      const formBackground = await commentForm.evaluate(el => 
        window.getComputedStyle(el).backgroundColor
      );
      
      expect(formBackground).not.toBe('rgb(248, 249, 250)'); // Light mode color
      
      // Check textarea styling
      const textarea = commentForm.locator('textarea');
      const textareaBackground = await textarea.evaluate(el => 
        window.getComputedStyle(el).backgroundColor
      );
      
      expect(textareaBackground).not.toBe('rgb(255, 255, 255)');
    }
  });

  test('should preserve animations in dark mode', async ({ page }) => {
    // Switch to dark mode
    const themeToggle = page.locator('#themeToggle');
    if (await themeToggle.count() > 0) {
      await themeToggle.click();
      await page.waitForTimeout(500);
    }
    
    // Reload to see animations
    await page.reload();
    await page.waitForLoadState('networkidle');
    
    await page.waitForSelector('.comment-item', { timeout: 5000 });
    const firstComment = page.locator('.comment-item').first();
    
    // Check if animations still work in dark mode
    const animationName = await firstComment.evaluate(el => 
      window.getComputedStyle(el).animationName
    );
    
    expect(animationName).toBe('slideInRight');
  });

  test('should handle theme switching while interacting with comments', async ({ page }) => {
    // Start with light mode, open a reply form
    await page.waitForSelector('.reply-btn', { timeout: 5000 });
    const replyButton = page.locator('.reply-btn').first();
    await replyButton.click();
    
    const replyForm = page.locator('.reply-form').first();
    await expect(replyForm).toBeVisible();
    
    // Switch to dark mode while form is open
    const themeToggle = page.locator('#themeToggle');
    if (await themeToggle.count() > 0) {
      await themeToggle.click();
      await page.waitForTimeout(500);
      
      // Form should still be visible and properly styled
      await expect(replyForm).toBeVisible();
      
      const textarea = replyForm.locator('textarea');
      const textareaBackground = await textarea.evaluate(el => 
        window.getComputedStyle(el).backgroundColor
      );
      
      expect(textareaBackground).not.toBe('rgb(255, 255, 255)');
    }
  });
});
