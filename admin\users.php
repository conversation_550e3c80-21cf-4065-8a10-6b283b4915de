<?php
require_once __DIR__ . '/../config/config.php';

// Check if user is logged in and is admin
if (!isLoggedIn() || getUserType() !== 'admin') {
    redirect('/auth/login.php');
}

try {
    $database = new Database();
    $db = $database->getConnection();
    
    // Initialize variables
    $users = [];
    $total_users = 0;
    $user_type_filter = sanitize($_GET['type'] ?? '');
    $search = sanitize($_GET['search'] ?? '');
    $page = max(1, (int)($_GET['page'] ?? 1));
    $per_page = 20;
    $offset = ($page - 1) * $per_page;
    
    // Build query
    try {
        $where_conditions = [];
        $params = [];
        
        if (!empty($user_type_filter)) {
            $where_conditions[] = "u.user_type = ?";
            $params[] = $user_type_filter;
        }
        
        if (!empty($search)) {
            $where_conditions[] = "(u.first_name LIKE ? OR u.last_name LIKE ? OR u.email LIKE ?)";
            $search_term = "%$search%";
            $params[] = $search_term;
            $params[] = $search_term;
            $params[] = $search_term;
        }
        
        $where_clause = !empty($where_conditions) ? "WHERE " . implode(" AND ", $where_conditions) : "";
        
        // Get total count
        $count_query = "SELECT COUNT(*) as total FROM users u $where_clause";
        $stmt = $db->prepare($count_query);
        $stmt->execute($params);
        $result = $stmt->fetch(PDO::FETCH_ASSOC);
        $total_users = $result ? (int)$result['total'] : 0;
        
        // Get users with pagination
        $query = "SELECT u.*, 
                         CASE 
                           WHEN u.user_type = 'business' THEN bp.company_name 
                           WHEN u.user_type = 'jobseeker' THEN jp.education_level 
                           ELSE NULL 
                         END as additional_info
                  FROM users u 
                  LEFT JOIN business_profiles bp ON u.id = bp.user_id 
                  LEFT JOIN jobseeker_profiles jp ON u.id = jp.user_id 
                  $where_clause 
                  ORDER BY u.created_at DESC 
                  LIMIT ? OFFSET ?";
        
        $params[] = $per_page;
        $params[] = $offset;
        
        $stmt = $db->prepare($query);
        $stmt->execute($params);
        $users = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
    } catch (Exception $e) {
        error_log("Users query error: " . $e->getMessage());
        $users = [];
        $total_users = 0;
    }
    
    // Calculate pagination
    $total_pages = ceil($total_users / $per_page);
    
} catch (Exception $e) {
    error_log("Admin users page error: " . $e->getMessage());
    $_SESSION['error'] = "Database error occurred.";
    $users = [];
    $total_users = 0;
    $total_pages = 0;
}

$page_title = "User Management";
include __DIR__ . '/../includes/header.php';
?>

<main class="py-4">
    <div class="container-fluid">
        <!-- Success/Error Messages -->
        <?php if (isset($_SESSION['success'])): ?>
        <div class="alert alert-success alert-dismissible fade show" role="alert">
            <i class="fas fa-check-circle me-2"></i>
            <?php echo htmlspecialchars($_SESSION['success']); unset($_SESSION['success']); ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
        <?php endif; ?>

        <?php if (isset($_SESSION['error'])): ?>
        <div class="alert alert-danger alert-dismissible fade show" role="alert">
            <i class="fas fa-exclamation-circle me-2"></i>
            <?php echo htmlspecialchars($_SESSION['error']); unset($_SESSION['error']); ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
        <?php endif; ?>

        <!-- Header -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h1 class="h3 mb-0">User Management</h1>
                        <p class="text-muted">
                            <?php if (!empty($user_type_filter)): ?>
                                Showing <?php echo ucfirst($user_type_filter); ?>s
                            <?php else: ?>
                                All Users
                            <?php endif; ?>
                            (<?php echo number_format($total_users); ?> total)
                        </p>
                    </div>
                    <div class="d-flex gap-2">
                        <a href="dashboard.php" class="btn btn-outline-primary">
                            <i class="fas fa-arrow-left me-1"></i>Back to Dashboard
                        </a>
                    </div>
                </div>
            </div>
        </div>

        <!-- Filters -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-body">
                        <form method="GET" class="row g-3">
                            <div class="col-md-4">
                                <label for="search" class="form-label">Search Users</label>
                                <input type="text" class="form-control" id="search" name="search" 
                                       placeholder="Name or email" value="<?php echo htmlspecialchars($search); ?>">
                            </div>
                            <div class="col-md-3">
                                <label for="type" class="form-label">User Type</label>
                                <select class="form-select" id="type" name="type">
                                    <option value="">All Types</option>
                                    <option value="jobseeker" <?php echo $user_type_filter === 'jobseeker' ? 'selected' : ''; ?>>Job Seekers</option>
                                    <option value="business" <?php echo $user_type_filter === 'business' ? 'selected' : ''; ?>>Businesses</option>
                                    <option value="admin" <?php echo $user_type_filter === 'admin' ? 'selected' : ''; ?>>Admins</option>
                                </select>
                            </div>
                            <div class="col-md-3 d-flex align-items-end">
                                <button type="submit" class="btn btn-primary me-2">
                                    <i class="fas fa-search"></i> Filter
                                </button>
                                <a href="users.php" class="btn btn-outline-secondary">Clear</a>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>

        <!-- Users Table -->
        <div class="row">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">Users (<?php echo count($users); ?> of <?php echo number_format($total_users); ?>)</h5>
                    </div>
                    <div class="card-body">
                        <?php if (empty($users)): ?>
                            <div class="text-center py-5">
                                <i class="fas fa-users fa-3x text-muted mb-3"></i>
                                <h5>No Users Found</h5>
                                <p class="text-muted">
                                    <?php if (!empty($user_type_filter) || !empty($search)): ?>
                                        Try adjusting your filters or <a href="users.php">view all users</a>.
                                    <?php else: ?>
                                        No users have registered yet.
                                    <?php endif; ?>
                                </p>
                            </div>
                        <?php else: ?>
                            <div class="table-responsive">
                                <table class="table table-hover">
                                    <thead>
                                        <tr>
                                            <th>User</th>
                                            <th>Type</th>
                                            <th>Contact</th>
                                            <th>Status</th>
                                            <th>Joined</th>
                                            <th>Actions</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <?php foreach ($users as $user): ?>
                                        <tr>
                                            <td>
                                                <div class="d-flex align-items-center">
                                                    <div class="avatar-sm me-3">
                                                        <i class="fas fa-user-circle fa-2x text-primary"></i>
                                                    </div>
                                                    <div>
                                                        <strong><?php echo htmlspecialchars($user['first_name'] . ' ' . $user['last_name']); ?></strong>
                                                        <br>
                                                        <small class="text-muted"><?php echo htmlspecialchars($user['email']); ?></small>
                                                        <?php if (!empty($user['additional_info'])): ?>
                                                        <br>
                                                        <small class="text-info">
                                                            <?php 
                                                            if ($user['user_type'] === 'business') {
                                                                echo htmlspecialchars($user['additional_info']);
                                                            } elseif ($user['user_type'] === 'jobseeker') {
                                                                echo 'Education: ' . htmlspecialchars($user['additional_info']);
                                                            }
                                                            ?>
                                                        </small>
                                                        <?php endif; ?>
                                                    </div>
                                                </div>
                                            </td>
                                            <td>
                                                <?php
                                                $type_colors = [
                                                    'admin' => 'danger',
                                                    'business' => 'success',
                                                    'jobseeker' => 'primary'
                                                ];
                                                $type_icons = [
                                                    'admin' => 'user-shield',
                                                    'business' => 'building',
                                                    'jobseeker' => 'user'
                                                ];
                                                ?>
                                                <span class="badge bg-<?php echo $type_colors[$user['user_type']] ?? 'secondary'; ?>">
                                                    <i class="fas fa-<?php echo $type_icons[$user['user_type']] ?? 'user'; ?> me-1"></i>
                                                    <?php echo ucfirst($user['user_type']); ?>
                                                </span>
                                            </td>
                                            <td>
                                                <small>
                                                    <?php if (!empty($user['phone'])): ?>
                                                        <i class="fas fa-phone me-1"></i><?php echo htmlspecialchars($user['phone']); ?><br>
                                                    <?php endif; ?>
                                                    <i class="fas fa-envelope me-1"></i><?php echo htmlspecialchars($user['email']); ?>
                                                </small>
                                            </td>
                                            <td>
                                                <?php if ($user['is_verified']): ?>
                                                    <span class="badge bg-success">
                                                        <i class="fas fa-check me-1"></i>Verified
                                                    </span>
                                                <?php else: ?>
                                                    <span class="badge bg-warning">
                                                        <i class="fas fa-clock me-1"></i>Pending
                                                    </span>
                                                <?php endif; ?>
                                            </td>
                                            <td>
                                                <small class="text-muted">
                                                    <?php echo date('M j, Y', strtotime($user['created_at'])); ?>
                                                    <br>
                                                    <?php echo date('g:i A', strtotime($user['created_at'])); ?>
                                                </small>
                                            </td>
                                            <td>
                                                <div class="btn-group btn-group-sm">
                                                    <button class="btn btn-outline-info" 
                                                            onclick="viewUserDetails(<?php echo $user['id']; ?>)" 
                                                            title="View Details">
                                                        <i class="fas fa-eye"></i>
                                                    </button>
                                                    <?php if ($user['user_type'] === 'business' && !$user['is_verified']): ?>
                                                    <button class="btn btn-outline-success" 
                                                            onclick="approveUser(<?php echo $user['id']; ?>)" 
                                                            title="Approve">
                                                        <i class="fas fa-check"></i>
                                                    </button>
                                                    <?php endif; ?>
                                                    <button class="btn btn-outline-danger" 
                                                            onclick="deleteUser(<?php echo $user['id']; ?>)" 
                                                            title="Delete">
                                                        <i class="fas fa-trash"></i>
                                                    </button>
                                                </div>
                                            </td>
                                        </tr>
                                        <?php endforeach; ?>
                                    </tbody>
                                </table>
                            </div>

                            <!-- Pagination -->
                            <?php if ($total_pages > 1): ?>
                            <nav aria-label="Users pagination" class="mt-4">
                                <ul class="pagination justify-content-center">
                                    <?php if ($page > 1): ?>
                                    <li class="page-item">
                                        <a class="page-link" href="?<?php echo http_build_query(array_merge($_GET, ['page' => $page - 1])); ?>">
                                            <i class="fas fa-chevron-left"></i> Previous
                                        </a>
                                    </li>
                                    <?php endif; ?>
                                    
                                    <?php for ($i = max(1, $page - 2); $i <= min($total_pages, $page + 2); $i++): ?>
                                    <li class="page-item <?php echo $i === $page ? 'active' : ''; ?>">
                                        <a class="page-link" href="?<?php echo http_build_query(array_merge($_GET, ['page' => $i])); ?>">
                                            <?php echo $i; ?>
                                        </a>
                                    </li>
                                    <?php endfor; ?>
                                    
                                    <?php if ($page < $total_pages): ?>
                                    <li class="page-item">
                                        <a class="page-link" href="?<?php echo http_build_query(array_merge($_GET, ['page' => $page + 1])); ?>">
                                            Next <i class="fas fa-chevron-right"></i>
                                        </a>
                                    </li>
                                    <?php endif; ?>
                                </ul>
                            </nav>
                            <?php endif; ?>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>
    </div>
</main>

<script>
function viewUserDetails(userId) {
    // Fetch user details via AJAX
    fetch(`user-details.php?id=${userId}`)
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                const user = data.user;
                
                // Create modal content
                const modalContent = `
                    <div class="modal-dialog modal-lg">
                        <div class="modal-content">
                            <div class="modal-header">
                                <h5 class="modal-title">
                                    <i class="fas fa-user me-2"></i>User Details
                                </h5>
                                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                            </div>
                            <div class="modal-body">
                                <div class="row">
                                    <div class="col-md-6">
                                        <h6 class="text-primary">Personal Information</h6>
                                        <table class="table table-borderless">
                                            <tr>
                                                <td><strong>Name:</strong></td>
                                                <td>${user.first_name} ${user.last_name}</td>
                                            </tr>
                                            <tr>
                                                <td><strong>Email:</strong></td>
                                                <td>${user.email}</td>
                                            </tr>
                                            <tr>
                                                <td><strong>Phone:</strong></td>
                                                <td>${user.phone || 'Not provided'}</td>
                                            </tr>
                                            <tr>
                                                <td><strong>User Type:</strong></td>
                                                <td><span class="badge bg-${getUserTypeColor(user.user_type)}">${user.user_type}</span></td>
                                            </tr>
                                            <tr>
                                                <td><strong>Status:</strong></td>
                                                <td><span class="badge bg-${user.is_verified ? 'success' : 'warning'}">${user.is_verified ? 'Verified' : 'Pending'}</span></td>
                                            </tr>
                                            <tr>
                                                <td><strong>Joined:</strong></td>
                                                <td>${new Date(user.created_at).toLocaleDateString()}</td>
                                            </tr>
                                        </table>
                                    </div>
                                    <div class="col-md-6">
                                        <h6 class="text-primary">Additional Information</h6>
                                        ${getAdditionalInfo(user)}
                                    </div>
                                </div>
                                
                                ${getUserSpecificInfo(user)}
                            </div>
                            <div class="modal-footer">
                                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                                ${getActionButtons(user)}
                            </div>
                        </div>
                    </div>
                `;
                
                // Update modal content
                const modalElement = document.getElementById('userDetailsModal');
                modalElement.innerHTML = modalContent;
                
                // Show the modal
                const modal = new bootstrap.Modal(modalElement);
                modal.show();
            } else {
                alert('Failed to load user details: ' + data.message);
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('Failed to load user details. Please try again.');
        });
}

function getUserTypeColor(userType) {
    const colors = {
        'admin': 'danger',
        'business': 'success',
        'jobseeker': 'primary'
    };
    return colors[userType] || 'secondary';
}

function getAdditionalInfo(user) {
    if (user.profile) {
        if (user.user_type === 'business') {
            return `
                <table class="table table-borderless">
                    <tr>
                        <td><strong>Company:</strong></td>
                        <td>${user.profile.company_name || 'Not provided'}</td>
                    </tr>
                    <tr>
                        <td><strong>Address:</strong></td>
                        <td>${user.profile.business_address || 'Not provided'}</td>
                    </tr>
                    <tr>
                        <td><strong>Mayor's Permit:</strong></td>
                        <td>${user.profile.mayors_permit || 'Not provided'}</td>
                    </tr>
                    <tr>
                        <td><strong>DTI Number:</strong></td>
                        <td>${user.profile.dti_number || 'Not provided'}</td>
                    </tr>
                </table>
            `;
        } else if (user.user_type === 'jobseeker') {
            return `
                <table class="table table-borderless">
                    <tr>
                        <td><strong>Education:</strong></td>
                        <td>${user.profile.education_level || 'Not provided'}</td>
                    </tr>
                    <tr>
                        <td><strong>Course:</strong></td>
                        <td>${user.profile.degree_course || 'Not provided'}</td>
                    </tr>
                    <tr>
                        <td><strong>Experience:</strong></td>
                        <td>${user.profile.experience_years || 0} years</td>
                    </tr>
                    <tr>
                        <td><strong>Location:</strong></td>
                        <td>${user.profile.location || 'Not provided'}</td>
                    </tr>
                    <tr>
                        <td><strong>Expected Salary:</strong></td>
                        <td>₱${user.profile.expected_salary ? user.profile.expected_salary.toLocaleString() : 'Not specified'}</td>
                    </tr>
                    <tr>
                        <td><strong>Resume:</strong></td>
                        <td>${getResumeInfo(user.profile.resume_file)}</td>
                    </tr>
                </table>
            `;
        }
    }
    return '<p class="text-muted">No additional information available.</p>';
}

function getResumeInfo(resumeFile) {
    if (resumeFile) {
        return `<a href="../uploads/resumes/${resumeFile}" target="_blank" class="btn btn-sm btn-outline-primary">
            <i class="fas fa-file-pdf me-1"></i>View Resume
        </a>`;
    }
    return '<span class="text-muted">No resume uploaded</span>';
}

function getUserSpecificInfo(user) {
    let specificInfo = '';
    
    if (user.user_type === 'jobseeker' && user.profile) {
        // Skills section
        if (user.profile.skills) {
            specificInfo += `
                <div class="mt-3">
                    <h6 class="text-primary">Skills</h6>
                    <div class="d-flex flex-wrap gap-1">
                        ${user.profile.skills.split(',').map(skill => 
                            `<span class="badge bg-secondary">${skill.trim()}</span>`
                        ).join('')}
                    </div>
                </div>
            `;
        }
        
        // Resume section
        specificInfo += `
            <div class="mt-3">
                <h6 class="text-primary">Resume</h6>
                ${getResumeInfo(user.profile.resume_file)}
            </div>
        `;
    } else if (user.user_type === 'business' && user.profile && user.profile.business_description) {
        specificInfo = `
            <div class="mt-3">
                <h6 class="text-primary">Business Description</h6>
                <p class="text-muted">${user.profile.business_description}</p>
            </div>
        `;
    }
    
    return specificInfo;
}

function getActionButtons(user) {
    let buttons = '';
    
    if (user.user_type === 'business' && !user.is_verified) {
        buttons += `<button type="button" class="btn btn-success" onclick="approveUser(${user.id})">
            <i class="fas fa-check me-1"></i>Approve
        </button>`;
    }
    
    buttons += `<button type="button" class="btn btn-danger" onclick="deleteUser(${user.id})">
        <i class="fas fa-trash me-1"></i>Delete
    </button>`;
    
    return buttons;
}

function approveUser(userId) {
    if (confirm('Are you sure you want to approve this user?')) {
        fetch('approve-user.php', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({ user_id: userId })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                alert('User approved successfully!');
                location.reload();
            } else {
                alert('Failed to approve user: ' + data.message);
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('Failed to approve user. Please try again.');
        });
    }
}

function deleteUser(userId) {
    if (confirm('Are you sure you want to delete this user? This action cannot be undone.')) {
        fetch('delete-user.php', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({ user_id: userId })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                alert('User deleted successfully!');
                location.reload();
            } else {
                alert('Failed to delete user: ' + data.message);
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('Failed to delete user. Please try again.');
        });
    }
}
</script>

<!-- Modal container -->
<div class="modal fade" id="userDetailsModal" tabindex="-1" aria-labelledby="userDetailsModalLabel" aria-hidden="true">
    <!-- Modal content will be dynamically inserted here -->
</div>

<?php include __DIR__ . '/../includes/footer.php'; ?> 