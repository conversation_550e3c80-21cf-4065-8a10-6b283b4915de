<?php
require_once __DIR__ . '/../config/config.php';

// Check if user is logged in and is admin
if (!isLoggedIn() || getUserType() !== 'admin') {
    http_response_code(403);
    echo json_encode(['success' => false, 'message' => 'Unauthorized']);
    exit;
}

try {
    $database = new Database();
    $db = $database->getConnection();
    
    $job_id = (int)($_GET['job_id'] ?? 0);
    
    if (!$job_id) {
        echo json_encode(['success' => false, 'message' => 'Invalid job ID']);
        exit;
    }
    
    // Get job details with company information
    $query = "SELECT jp.*, bp.company_name, u.first_name, u.last_name, u.email, u.phone 
              FROM job_posts jp 
              JOIN business_profiles bp ON jp.business_id = bp.user_id 
              JOIN users u ON bp.user_id = u.id 
              WHERE jp.id = ?";
    
    $stmt = $db->prepare($query);
    $stmt->execute([$job_id]);
    $job = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if (!$job) {
        echo json_encode(['success' => false, 'message' => 'Job not found']);
        exit;
    }
    
    echo json_encode(['success' => true, 'job' => $job]);
    
} catch (Exception $e) {
    error_log("Get job details error: " . $e->getMessage());
    echo json_encode(['success' => false, 'message' => 'Database error occurred']);
}
?> 