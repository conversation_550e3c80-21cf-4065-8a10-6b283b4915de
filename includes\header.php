<?php
// Ensure session is started
if (session_status() == PHP_SESSION_NONE) {
    session_start();
}

// Get current page for active navigation
$current_page = basename($_SERVER['PHP_SELF']);
$current_dir = basename(dirname($_SERVER['PHP_SELF']));
?>
<!DOCTYPE html>
<html lang="en" data-theme="light">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo isset($page_title) ? htmlspecialchars($page_title) . ' - ' : ''; ?>Tan-Aw Job Portal</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <!-- Custom CSS -->
    <link href="<?php echo SITE_URL; ?>/assets/css/style.css" rel="stylesheet">
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-light sticky-top">
        <div class="container">
            <!-- Brand -->
            <a class="navbar-brand fw-bold fs-3" href="<?php echo SITE_URL; ?>/">
                <i class="fas fa-briefcase text-primary me-2"></i>
                <span class="text-primary">Tan-Aw</span>
            </a>

            <!-- Mobile Toggle -->
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>

            <!-- Navigation Links -->
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link fs-5 fw-semibold px-3 <?php echo ($current_page === 'index.php' || $current_page === '') ? 'active' : ''; ?>" 
                           href="<?php echo SITE_URL; ?>/">
                            <i class="fas fa-home me-1"></i>Home
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link fs-5 fw-semibold px-3 <?php echo $current_page === 'jobs.php' ? 'active' : ''; ?>" 
                           href="<?php echo SITE_URL; ?>/jobs.php">
                            <i class="fas fa-briefcase me-1"></i>Jobs
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link fs-5 fw-semibold px-3 <?php echo $current_page === 'about.php' ? 'active' : ''; ?>" 
                           href="<?php echo SITE_URL; ?>/about.php">
                            <i class="fas fa-info-circle me-1"></i>About Us
                        </a>
                    </li>
                </ul>

                <!-- Right Side Navigation -->
                <ul class="navbar-nav align-items-center">
                    <!-- Simple Theme Toggle -->
                    <li class="nav-item me-3">
                        <button class="btn btn-outline-secondary btn-sm" id="themeToggle"
                                title="Click to toggle dark/light mode"
                                aria-label="Toggle dark mode"
                                type="button">
                            <i class="fas fa-moon" id="themeIcon"></i>
                        </button>
                    </li>

                    <?php if (isLoggedIn()): ?>
                        <!-- User Dropdown -->
                        <li class="nav-item dropdown">
                            <a class="nav-link dropdown-toggle d-flex align-items-center" href="#" id="userDropdown" 
                               role="button" data-bs-toggle="dropdown" aria-expanded="false">
                                <div class="user-avatar me-2">
                                    <i class="fas fa-user-circle fa-2x text-primary"></i>
                                </div>
                                <div class="user-info d-none d-md-block">
                                    <div class="fw-semibold"><?php echo htmlspecialchars($_SESSION['first_name'] ?? 'User'); ?></div>
                                    <small class="text-muted"><?php echo ucfirst(getUserType()); ?></small>
                                </div>
                            </a>
                            <ul class="dropdown-menu dropdown-menu-end shadow">
                                <li>
                                    <div class="dropdown-header">
                                        <strong><?php echo htmlspecialchars(($_SESSION['first_name'] ?? '') . ' ' . ($_SESSION['last_name'] ?? '')); ?></strong>
                                        <br>
                                        <small class="text-muted"><?php echo htmlspecialchars($_SESSION['email'] ?? ''); ?></small>
                                    </div>
                                </li>
                                <li><hr class="dropdown-divider"></li>
                                
                                <!-- Dashboard Link -->
                                <li>
                                    <a class="dropdown-item" href="<?php echo SITE_URL; ?>/<?php echo getUserType(); ?>/dashboard.php">
                                        <i class="fas fa-tachometer-alt me-2"></i>Dashboard
                                    </a>
                                </li>
                                
                                <!-- Profile Link -->
                                <li>
                                    <a class="dropdown-item" href="<?php echo SITE_URL; ?>/<?php echo getUserType(); ?>/profile.php">
                                        <i class="fas fa-user me-2"></i>My Profile
                                    </a>
                                </li>
                                
                                <?php if (getUserType() === 'jobseeker'): ?>
                                <li>
                                    <a class="dropdown-item" href="<?php echo SITE_URL; ?>/jobseeker/applications.php">
                                        <i class="fas fa-file-alt me-2"></i>My Applications
                                    </a>
                                </li>
                                <?php endif; ?>
                                
                                <li><hr class="dropdown-divider"></li>
                                
                                <!-- Logout -->
                                <li>
                                    <a class="dropdown-item text-danger" href="#" onclick="confirmLogout()">
                                        <i class="fas fa-sign-out-alt me-2"></i>Logout
                                    </a>
                                </li>
                            </ul>
                        </li>
                    <?php else: ?>
                        <!-- Login/Register Links -->
                        <li class="nav-item me-2">
                            <a class="btn btn-outline-primary" href="<?php echo SITE_URL; ?>/auth/login.php">
                                <i class="fas fa-sign-in-alt me-1"></i>Login
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="btn btn-primary" href="<?php echo SITE_URL; ?>/auth/register.php">
                                <i class="fas fa-user-plus me-1"></i>Register
                            </a>
                        </li>
                    <?php endif; ?>
                </ul>
            </div>
        </div>
    </nav>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <!-- Custom JS -->
    <script src="<?php echo SITE_URL; ?>/assets/js/script.js"></script>
    
    <!-- Site Configuration -->
    <script>
        window.SITE_URL = '<?php echo SITE_URL; ?>';
    </script>
