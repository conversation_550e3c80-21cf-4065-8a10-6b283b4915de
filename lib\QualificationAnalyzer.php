<?php
/**
 * Job Qualification Analyzer
 * 
 * This class handles automatic job qualification analysis based on educational background.
 * It calculates qualification percentages for job seekers based on their course/degree
 * and maps them to job categories using a qualification matrix.
 */

class QualificationAnalyzer {
    private $db;
    
    public function __construct($database) {
        $this->db = $database;
    }
    
    /**
     * Get course category based on degree/course name
     */
    public function getCourseCategory($degreeCourse) {
        if (empty($degreeCourse)) {
            return 'Other';
        }
        
        $degreeCourse = strtolower(trim($degreeCourse));
        
        // Information Technology
        if (strpos($degreeCourse, 'information technology') !== false ||
            strpos($degreeCourse, 'computer science') !== false ||
            strpos($degreeCourse, 'software engineering') !== false ||
            strpos($degreeCourse, 'computer engineering') !== false ||
            strpos($degreeCourse, 'information systems') !== false ||
            strpos($degreeCourse, 'cybersecurity') !== false ||
            strpos($degreeCourse, 'data science') !== false ||
            strpos($degreeCourse, 'bsit') !== false ||
            strpos($degreeCourse, 'bscs') !== false) {
            return 'Information Technology';
        }
        
        // Business & Management
        if (strpos($degreeCourse, 'business administration') !== false ||
            strpos($degreeCourse, 'business management') !== false ||
            strpos($degreeCourse, 'accounting') !== false ||
            strpos($degreeCourse, 'finance') !== false ||
            strpos($degreeCourse, 'marketing') !== false ||
            strpos($degreeCourse, 'economics') !== false ||
            strpos($degreeCourse, 'bsba') !== false ||
            strpos($degreeCourse, 'bsa') !== false) {
            return 'Business & Management';
        }
        
        // Engineering
        if (strpos($degreeCourse, 'civil engineering') !== false ||
            strpos($degreeCourse, 'electrical engineering') !== false ||
            strpos($degreeCourse, 'mechanical engineering') !== false ||
            strpos($degreeCourse, 'electronics engineering') !== false ||
            strpos($degreeCourse, 'chemical engineering') !== false ||
            strpos($degreeCourse, 'industrial engineering') !== false ||
            strpos($degreeCourse, 'mining engineering') !== false ||
            strpos($degreeCourse, 'petroleum engineering') !== false ||
            strpos($degreeCourse, 'agricultural engineering') !== false ||
            strpos($degreeCourse, 'bsce') !== false ||
            strpos($degreeCourse, 'bsee') !== false ||
            strpos($degreeCourse, 'bsme') !== false) {
            return 'Engineering';
        }
        
        // Education
        if (strpos($degreeCourse, 'elementary education') !== false ||
            strpos($degreeCourse, 'secondary education') !== false ||
            strpos($degreeCourse, 'education') !== false ||
            strpos($degreeCourse, 'teaching') !== false ||
            strpos($degreeCourse, 'beed') !== false ||
            strpos($degreeCourse, 'bsed') !== false) {
            return 'Education';
        }
        
        // Healthcare
        if (strpos($degreeCourse, 'nursing') !== false ||
            strpos($degreeCourse, 'medical technology') !== false ||
            strpos($degreeCourse, 'pharmacy') !== false ||
            strpos($degreeCourse, 'physical therapy') !== false ||
            strpos($degreeCourse, 'occupational therapy') !== false ||
            strpos($degreeCourse, 'bsn') !== false ||
            strpos($degreeCourse, 'bsmt') !== false) {
            return 'Healthcare';
        }
        
        // Arts & Humanities
        if (strpos($degreeCourse, 'literature') !== false ||
            strpos($degreeCourse, 'history') !== false ||
            strpos($degreeCourse, 'philosophy') !== false ||
            strpos($degreeCourse, 'english') !== false ||
            strpos($degreeCourse, 'sociology') !== false ||
            strpos($degreeCourse, 'psychology') !== false ||
            strpos($degreeCourse, 'political science') !== false ||
            strpos($degreeCourse, 'mass communication') !== false) {
            return 'Arts & Humanities';
        }
        
        // Science
        if (strpos($degreeCourse, 'biology') !== false ||
            strpos($degreeCourse, 'chemistry') !== false ||
            strpos($degreeCourse, 'physics') !== false ||
            strpos($degreeCourse, 'mathematics') !== false ||
            strpos($degreeCourse, 'statistics') !== false ||
            strpos($degreeCourse, 'environmental science') !== false ||
            strpos($degreeCourse, 'bs biology') !== false ||
            strpos($degreeCourse, 'bs chemistry') !== false ||
            strpos($degreeCourse, 'bs physics') !== false) {
            return 'Science';
        }
        
        // Vocational
        if (strpos($degreeCourse, 'vocational') !== false ||
            strpos($degreeCourse, 'technical') !== false ||
            strpos($degreeCourse, 'automotive') !== false ||
            strpos($degreeCourse, 'electrical') !== false ||
            strpos($degreeCourse, 'plumbing') !== false ||
            strpos($degreeCourse, 'welding') !== false ||
            strpos($degreeCourse, 'computer programming') !== false ||
            strpos($degreeCourse, 'computer systems') !== false ||
            strpos($degreeCourse, 'office administration') !== false) {
            return 'Vocational';
        }
        
        // High School
        if (strpos($degreeCourse, 'high school') !== false ||
            strpos($degreeCourse, 'secondary') !== false) {
            return 'High School';
        }
        
        return 'Other';
    }
    
    /**
     * Get job category based on job title and requirements
     */
    public function getJobCategory($jobTitle, $requirements = '', $skillsRequired = '') {
        $jobTitle = strtolower(trim($jobTitle));
        $requirements = strtolower(trim($requirements));
        $skillsRequired = strtolower(trim($skillsRequired));
        
        // Information Technology
        if (strpos($jobTitle, 'developer') !== false ||
            strpos($jobTitle, 'programmer') !== false ||
            strpos($jobTitle, 'software') !== false ||
            strpos($jobTitle, 'it') !== false ||
            strpos($jobTitle, 'system') !== false ||
            strpos($jobTitle, 'network') !== false ||
            strpos($jobTitle, 'database') !== false ||
            strpos($jobTitle, 'web') !== false ||
            strpos($jobTitle, 'mobile') !== false ||
            strpos($jobTitle, 'app') !== false ||
            strpos($requirements, 'programming') !== false ||
            strpos($skillsRequired, 'php') !== false ||
            strpos($skillsRequired, 'javascript') !== false ||
            strpos($skillsRequired, 'python') !== false ||
            strpos($skillsRequired, 'java') !== false) {
            return 'Information Technology';
        }
        
        // Business & Finance
        if (strpos($jobTitle, 'accountant') !== false ||
            strpos($jobTitle, 'finance') !== false ||
            strpos($jobTitle, 'analyst') !== false ||
            strpos($jobTitle, 'manager') !== false ||
            strpos($jobTitle, 'administrator') !== false ||
            strpos($jobTitle, 'coordinator') !== false ||
            strpos($jobTitle, 'supervisor') !== false ||
            strpos($requirements, 'business') !== false ||
            strpos($requirements, 'finance') !== false ||
            strpos($requirements, 'accounting') !== false) {
            return 'Business & Finance';
        }
        
        // Sales & Marketing
        if (strpos($jobTitle, 'sales') !== false ||
            strpos($jobTitle, 'marketing') !== false ||
            strpos($jobTitle, 'representative') !== false ||
            strpos($jobTitle, 'customer service') !== false ||
            strpos($jobTitle, 'retail') !== false ||
            strpos($jobTitle, 'promoter') !== false ||
            strpos($requirements, 'sales') !== false ||
            strpos($requirements, 'marketing') !== false ||
            strpos($requirements, 'customer') !== false) {
            return 'Sales & Marketing';
        }
        
        // Engineering
        if (strpos($jobTitle, 'engineer') !== false ||
            strpos($jobTitle, 'engineering') !== false ||
            strpos($requirements, 'engineering') !== false ||
            strpos($requirements, 'civil') !== false ||
            strpos($requirements, 'electrical') !== false ||
            strpos($requirements, 'mechanical') !== false) {
            return 'Engineering';
        }
        
        // Education
        if (strpos($jobTitle, 'teacher') !== false ||
            strpos($jobTitle, 'instructor') !== false ||
            strpos($jobTitle, 'trainer') !== false ||
            strpos($jobTitle, 'professor') !== false ||
            strpos($jobTitle, 'educator') !== false ||
            strpos($requirements, 'teaching') !== false ||
            strpos($requirements, 'education') !== false) {
            return 'Education';
        }
        
        // Healthcare
        if (strpos($jobTitle, 'nurse') !== false ||
            strpos($jobTitle, 'medical') !== false ||
            strpos($jobTitle, 'healthcare') !== false ||
            strpos($jobTitle, 'doctor') !== false ||
            strpos($jobTitle, 'therapist') !== false ||
            strpos($requirements, 'nursing') !== false ||
            strpos($requirements, 'medical') !== false ||
            strpos($requirements, 'healthcare') !== false) {
            return 'Healthcare';
        }
        
        // Manufacturing
        if (strpos($jobTitle, 'production') !== false ||
            strpos($jobTitle, 'manufacturing') !== false ||
            strpos($jobTitle, 'quality control') !== false ||
            strpos($jobTitle, 'operator') !== false ||
            strpos($jobTitle, 'technician') !== false ||
            strpos($requirements, 'production') !== false ||
            strpos($requirements, 'manufacturing') !== false) {
            return 'Manufacturing';
        }
        
        // Service Industry
        if (strpos($jobTitle, 'waiter') !== false ||
            strpos($jobTitle, 'server') !== false ||
            strpos($jobTitle, 'cashier') !== false ||
            strpos($jobTitle, 'receptionist') !== false ||
            strpos($jobTitle, 'host') !== false ||
            strpos($jobTitle, 'cleaner') !== false ||
            strpos($jobTitle, 'maintenance') !== false ||
            strpos($requirements, 'service') !== false ||
            strpos($requirements, 'hospitality') !== false) {
            return 'Service Industry';
        }
        
        // Skilled Trades
        if (strpos($jobTitle, 'electrician') !== false ||
            strpos($jobTitle, 'plumber') !== false ||
            strpos($jobTitle, 'carpenter') !== false ||
            strpos($jobTitle, 'welder') !== false ||
            strpos($jobTitle, 'mechanic') !== false ||
            strpos($jobTitle, 'technician') !== false ||
            strpos($requirements, 'trade') !== false ||
            strpos($requirements, 'technical') !== false) {
            return 'Skilled Trades';
        }
        
        // Administrative
        if (strpos($jobTitle, 'secretary') !== false ||
            strpos($jobTitle, 'assistant') !== false ||
            strpos($jobTitle, 'clerk') !== false ||
            strpos($jobTitle, 'data entry') !== false ||
            strpos($jobTitle, 'office') !== false ||
            strpos($requirements, 'administrative') !== false ||
            strpos($requirements, 'office') !== false) {
            return 'Administrative';
        }
        
        // Creative & Design
        if (strpos($jobTitle, 'designer') !== false ||
            strpos($jobTitle, 'artist') !== false ||
            strpos($jobTitle, 'creative') !== false ||
            strpos($jobTitle, 'content') !== false ||
            strpos($jobTitle, 'multimedia') !== false ||
            strpos($requirements, 'design') !== false ||
            strpos($requirements, 'creative') !== false ||
            strpos($skillsRequired, 'photoshop') !== false ||
            strpos($skillsRequired, 'illustrator') !== false) {
            return 'Creative & Design';
        }
        
        return 'Other';
    }
    
    /**
     * Calculate qualification percentage for a job seeker and job
     */
    public function calculateQualification($jobseekerId, $jobId) {
        try {
            // Check cache first
            $cached = $this->getCachedQualification($jobseekerId, $jobId);
            if ($cached !== null) {
                return $cached;
            }
            
            // Get job seeker's course information
            $query = "SELECT jp.degree_course, jp.education_level 
                      FROM jobseeker_profiles jp 
                      WHERE jp.user_id = ?";
            $stmt = $this->db->prepare($query);
            $stmt->execute([$jobseekerId]);
            $jobseeker = $stmt->fetch(PDO::FETCH_ASSOC);
            
            if (!$jobseeker || empty($jobseeker['degree_course'])) {
                return 50; // Default for unknown background
            }
            
            // Get job information
            $query = "SELECT jp.title, jp.requirements, jp.skills_required 
                      FROM job_posts jp 
                      WHERE jp.id = ?";
            $stmt = $this->db->prepare($query);
            $stmt->execute([$jobId]);
            $job = $stmt->fetch(PDO::FETCH_ASSOC);
            
            if (!$job) {
                return 50; // Default for unknown job
            }
            
            // Get course and job categories
            $courseCategory = $this->getCourseCategory($jobseeker['degree_course']);
            $jobCategory = $this->getJobCategory($job['title'], $job['requirements'], $job['skills_required']);
            
            // Get qualification percentage from matrix
            $query = "SELECT qualification_percentage, reasoning 
                      FROM qualification_matrix 
                      WHERE course_category = ? AND job_category = ?";
            $stmt = $this->db->prepare($query);
            $stmt->execute([$courseCategory, $jobCategory]);
            $result = $stmt->fetch(PDO::FETCH_ASSOC);
            
            $percentage = $result ? $result['qualification_percentage'] : 50;
            
            // Cache the result
            $this->cacheQualification($jobseekerId, $jobId, $percentage);
            
            return $percentage;
            
        } catch (Exception $e) {
            error_log("Qualification calculation error: " . $e->getMessage());
            return 50; // Default fallback
        }
    }
    
    /**
     * Get cached qualification percentage
     */
    private function getCachedQualification($jobseekerId, $jobId) {
        try {
            $query = "SELECT qualification_percentage 
                      FROM qualification_cache 
                      WHERE jobseeker_id = ? AND job_id = ? 
                      AND calculation_date > DATE_SUB(NOW(), INTERVAL 1 DAY)";
            $stmt = $this->db->prepare($query);
            $stmt->execute([$jobseekerId, $jobId]);
            $result = $stmt->fetch(PDO::FETCH_ASSOC);
            
            return $result ? $result['qualification_percentage'] : null;
        } catch (Exception $e) {
            error_log("Cache retrieval error: " . $e->getMessage());
            return null;
        }
    }
    
    /**
     * Cache qualification percentage
     */
    private function cacheQualification($jobseekerId, $jobId, $percentage) {
        try {
            $query = "INSERT INTO qualification_cache (jobseeker_id, job_id, qualification_percentage) 
                      VALUES (?, ?, ?) 
                      ON DUPLICATE KEY UPDATE 
                      qualification_percentage = VALUES(qualification_percentage),
                      calculation_date = CURRENT_TIMESTAMP";
            $stmt = $this->db->prepare($query);
            $stmt->execute([$jobseekerId, $jobId, $percentage]);
        } catch (Exception $e) {
            error_log("Cache storage error: " . $e->getMessage());
        }
    }
    
    /**
     * Get qualification color class based on percentage
     */
    public function getQualificationColor($percentage) {
        if ($percentage >= 80) {
            return 'success'; // Green
        } elseif ($percentage >= 60) {
            return 'warning'; // Yellow
        } elseif ($percentage >= 40) {
            return 'info'; // Blue
        } else {
            return 'danger'; // Red
        }
    }
    
    /**
     * Get qualification text based on percentage
     */
    public function getQualificationText($percentage) {
        if ($percentage >= 90) {
            return 'Excellent Match';
        } elseif ($percentage >= 80) {
            return 'Very Good Match';
        } elseif ($percentage >= 70) {
            return 'Good Match';
        } elseif ($percentage >= 60) {
            return 'Fair Match';
        } elseif ($percentage >= 50) {
            return 'Moderate Match';
        } elseif ($percentage >= 40) {
            return 'Limited Match';
        } else {
            return 'Poor Match';
        }
    }
    
    /**
     * Get qualification icon based on percentage
     */
    public function getQualificationIcon($percentage) {
        if ($percentage >= 80) {
            return 'fas fa-star text-success';
        } elseif ($percentage >= 60) {
            return 'fas fa-star-half-alt text-warning';
        } elseif ($percentage >= 40) {
            return 'fas fa-circle text-info';
        } else {
            return 'fas fa-times text-danger';
        }
    }
    
    /**
     * Clear qualification cache for a job seeker
     */
    public function clearCache($jobseekerId) {
        try {
            $query = "DELETE FROM qualification_cache WHERE jobseeker_id = ?";
            $stmt = $this->db->prepare($query);
            $stmt->execute([$jobseekerId]);
        } catch (Exception $e) {
            error_log("Cache clearing error: " . $e->getMessage());
        }
    }
    
    /**
     * Get all qualification percentages for a job seeker
     */
    public function getAllQualifications($jobseekerId) {
        try {
            $query = "SELECT jp.id, jp.title, jp.company_name, qc.qualification_percentage
                      FROM job_posts jp 
                      JOIN business_profiles bp ON jp.business_id = bp.user_id
                      LEFT JOIN qualification_cache qc ON jp.id = qc.job_id AND qc.jobseeker_id = ?
                      WHERE jp.status = 'approved'
                      ORDER BY qc.qualification_percentage DESC, jp.created_at DESC";
            $stmt = $this->db->prepare($query);
            $stmt->execute([$jobseekerId]);
            return $stmt->fetchAll(PDO::FETCH_ASSOC);
        } catch (Exception $e) {
            error_log("Get all qualifications error: " . $e->getMessage());
            return [];
        }
    }
}
?> 