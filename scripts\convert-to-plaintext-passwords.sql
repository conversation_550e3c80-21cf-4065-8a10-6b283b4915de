-- Convert hashed passwords to plain text passwords
-- This script will update all existing users to have plain text passwords

-- Update admin user
UPDATE users SET password = 'password' WHERE email = '<EMAIL>';

-- Update business user
UPDATE users SET password = 'password' WHERE email = '<EMAIL>';

-- Update jobseeker user
UPDATE users SET password = 'password' WHERE email = '<EMAIL>';

-- Update Darylle Tandug
UPDATE users SET password = 'password' WHERE email = '<EMAIL>';

-- Update <PERSON> (business)
UPDATE users SET password = 'password' WHERE email = '<EMAIL>';

-- Update <PERSON> (jobseeker)
UPDATE users SET password = 'password' WHERE email = '<EMAIL>';

-- Update Kevenzyrel Pascioles
UPDATE users SET password = 'password' WHERE email = '<EMAIL>';

-- Update any other users with hashed passwords to plain text
-- This will convert any remaining hashed passwords to 'password'
UPDATE users 
SET password = 'password' 
WHERE password LIKE '$2y$%' OR password LIKE '$argon2id$%';

-- Verify the changes
SELECT id, email, password, user_type FROM users ORDER BY id; 