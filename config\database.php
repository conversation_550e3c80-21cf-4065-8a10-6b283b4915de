<?php
class Database {
    private $host = DB_HOST;
    private $db_name = DB_NAME;
    private $username = DB_USER;
    private $password = DB_PASS;
    private $conn = null;

    public function getConnection() {
        if ($this->conn === null) {
            try {
                // Set connection options for better security and performance
                $options = [
                    PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
                    PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
                    PDO::ATTR_EMULATE_PREPARES => false,
                    PDO::MYSQL_ATTR_INIT_COMMAND => "SET NAMES utf8mb4 COLLATE utf8mb4_unicode_ci",
                    PDO::ATTR_PERSISTENT => false, // Disable persistent connections for better error handling
                    PDO::ATTR_TIMEOUT => 10, // 10 second timeout
                ];
                
                $dsn = "mysql:host=" . $this->host . ";dbname=" . $this->db_name . ";charset=utf8mb4";
                
                $this->conn = new PDO($dsn, $this->username, $this->password, $options);
                
                // Test the connection
                $this->conn->query("SELECT 1");
                
            } catch(PDOException $e) {
                error_log("Database connection error: " . $e->getMessage());
                
                // Provide more specific error messages
                if (strpos($e->getMessage(), 'Access denied') !== false) {
                    throw new Exception("Database access denied. Please check your credentials.");
                } elseif (strpos($e->getMessage(), 'Unknown database') !== false) {
                    throw new Exception("Database '" . $this->db_name . "' does not exist. Please create the database first.");
                } elseif (strpos($e->getMessage(), 'Connection refused') !== false) {
                    throw new Exception("Cannot connect to database server. Please check if MySQL is running.");
                } else {
                    throw new Exception("Database connection failed: " . $e->getMessage());
                }
            } catch(Exception $e) {
                error_log("General database error: " . $e->getMessage());
                throw new Exception("Database connection failed. Please try again later.");
            }
        }
        return $this->conn;
    }
    
    public function closeConnection() {
        $this->conn = null;
    }
    
    public function testConnection() {
        try {
            $conn = $this->getConnection();
            $conn->query("SELECT 1");
            return true;
        } catch (Exception $e) {
            return false;
        }
    }
}
?>
