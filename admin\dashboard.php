<?php
require_once __DIR__ . '/../config/config.php';

// Check if user is logged in and is admin
if (!isLoggedIn() || getUserType() !== 'admin') {
    redirect('/auth/login.php'); // Fixed redirect path
}

try {
    $database = new Database();
    $db = $database->getConnection();
    
    // Initialize variables
    $total_jobseekers = 0;
    $total_businesses = 0;
    $total_jobs = 0;
    $pending_businesses = [];
    $pending_jobs = [];
    $recent_activities = [];
    
    // Handle actions
    if ($_SERVER['REQUEST_METHOD'] === 'POST') {
        try {
            $action = $_POST['action'] ?? '';
            
            if ($action === 'approve_business') {
                $user_id = (int)$_POST['user_id'];
                $query = "UPDATE users SET is_verified = 1 WHERE id = ? AND user_type = 'business'";
                $stmt = $db->prepare($query);
                $stmt->execute([$user_id]);
                
                $query = "UPDATE business_profiles SET is_approved = 1 WHERE user_id = ?";
                $stmt = $db->prepare($query);
                $stmt->execute([$user_id]);
                
                $_SESSION['success'] = 'Business account approved successfully!';
            } elseif ($action === 'reject_business') {
                $user_id = (int)$_POST['user_id'];
                $query = "UPDATE users SET is_verified = 0 WHERE id = ? AND user_type = 'business'";
                $stmt = $db->prepare($query);
                $stmt->execute([$user_id]);
                
                $query = "UPDATE business_profiles SET is_approved = 0 WHERE user_id = ?";
                $stmt = $db->prepare($query);
                $stmt->execute([$user_id]);
                
                $_SESSION['success'] = 'Business account rejected.';
            } elseif ($action === 'approve_job') {
                $job_id = (int)$_POST['job_id'];
                $query = "UPDATE job_posts SET status = 'approved' WHERE id = ?";
                $stmt = $db->prepare($query);
                $stmt->execute([$job_id]);
                
                $_SESSION['success'] = 'Job post approved successfully!';
            } elseif ($action === 'reject_job') {
                $job_id = (int)$_POST['job_id'];
                $query = "UPDATE job_posts SET status = 'rejected' WHERE id = ?";
                $stmt = $db->prepare($query);
                $stmt->execute([$job_id]);
                
                $_SESSION['success'] = 'Job post rejected.';
            } elseif ($action === 'delete_job') {
                $job_id = (int)$_POST['job_id'];
                $query = "DELETE FROM job_posts WHERE id = ?";
                $stmt = $db->prepare($query);
                $stmt->execute([$job_id]);
                
                $_SESSION['success'] = 'Job post deleted successfully!';
            }
            
            redirect('/admin/dashboard.php'); // Fixed redirect path
        } catch (Exception $e) {
            error_log("Admin action error: " . $e->getMessage());
            $_SESSION['error'] = 'Failed to perform action. Please try again.';
            redirect('/admin/dashboard.php');
        }
    }
    
    // Get statistics
    try {
        $query = "SELECT COUNT(*) as total FROM users WHERE user_type = 'jobseeker'";
        $stmt = $db->prepare($query);
        $stmt->execute();
        $result = $stmt->fetch(PDO::FETCH_ASSOC);
        $total_jobseekers = $result ? (int)$result['total'] : 0;
    } catch (Exception $e) {
        error_log("Jobseekers count error: " . $e->getMessage());
        $total_jobseekers = 0;
    }
    
    try {
        $query = "SELECT COUNT(*) as total FROM users WHERE user_type = 'business'";
        $stmt = $db->prepare($query);
        $stmt->execute();
        $result = $stmt->fetch(PDO::FETCH_ASSOC);
        $total_businesses = $result ? (int)$result['total'] : 0;
    } catch (Exception $e) {
        error_log("Businesses count error: " . $e->getMessage());
        $total_businesses = 0;
    }
    
    try {
        $query = "SELECT COUNT(*) as total FROM job_posts";
        $stmt = $db->prepare($query);
        $stmt->execute();
        $result = $stmt->fetch(PDO::FETCH_ASSOC);
        $total_jobs = $result ? (int)$result['total'] : 0;
    } catch (Exception $e) {
        error_log("Jobs count error: " . $e->getMessage());
        $total_jobs = 0;
    }
    
    // Get pending business approvals
    try {
        $query = "SELECT u.*, bp.company_name, bp.business_description, bp.mayors_permit, bp.dti_number 
                  FROM users u 
                  JOIN business_profiles bp ON u.id = bp.user_id 
                  WHERE u.user_type = 'business' AND u.is_verified = 0 
                  ORDER BY u.created_at DESC";
        $stmt = $db->prepare($query);
        $stmt->execute();
        $pending_businesses = $stmt->fetchAll(PDO::FETCH_ASSOC);
    } catch (Exception $e) {
        error_log("Pending businesses query error: " . $e->getMessage());
        $pending_businesses = [];
    }
    
    // Get pending job approvals
    try {
        $query = "SELECT jp.*, bp.company_name, u.first_name, u.last_name 
                  FROM job_posts jp 
                  JOIN business_profiles bp ON jp.business_id = bp.user_id 
                  JOIN users u ON bp.user_id = u.id 
                  WHERE jp.status = 'pending' 
                  ORDER BY jp.created_at DESC";
        $stmt = $db->prepare($query);
        $stmt->execute();
        $pending_jobs = $stmt->fetchAll(PDO::FETCH_ASSOC);
    } catch (Exception $e) {
        error_log("Pending jobs query error: " . $e->getMessage());
        $pending_jobs = [];
    }
    
    // Get recent activities
    try {
        $query = "SELECT 'job_application' as type, ja.applied_at as created_at, jp.title as job_title, 
                         CONCAT(u1.first_name, ' ', u1.last_name) as applicant_name,
                         bp.company_name, jp.id as job_id
                  FROM job_applications ja
                  JOIN job_posts jp ON ja.job_id = jp.id
                  JOIN users u1 ON ja.jobseeker_id = u1.id
                  JOIN business_profiles bp ON jp.business_id = bp.user_id
                  UNION ALL
                  SELECT 'job_post' as type, jp.created_at, jp.title as job_title,
                         bp.company_name as applicant_name, bp.company_name, jp.id as job_id
                  FROM job_posts jp
                  JOIN business_profiles bp ON jp.business_id = bp.user_id
                  ORDER BY created_at DESC
                  LIMIT 10";
        $stmt = $db->prepare($query);
        $stmt->execute();
        $recent_activities = $stmt->fetchAll(PDO::FETCH_ASSOC);
    } catch (Exception $e) {
        error_log("Recent activities query error: " . $e->getMessage());
        $recent_activities = [];
    }
    
} catch (Exception $e) {
    error_log("Admin dashboard error: " . $e->getMessage());
    $_SESSION['error'] = "Database error occurred.";
    // Initialize variables to prevent undefined variable errors
    $total_jobseekers = 0;
    $total_businesses = 0;
    $total_jobs = 0;
    $pending_businesses = [];
    $pending_jobs = [];
    $recent_activities = [];
}

$page_title = "Admin Dashboard";
include __DIR__ . '/../includes/header.php';
?>

<main class="py-4">
    <div class="container-fluid">
        <!-- Success/Error Messages -->
        <?php if (isset($_SESSION['success'])): ?>
        <div class="alert alert-success alert-dismissible fade show" role="alert">
            <i class="fas fa-check-circle me-2"></i>
            <?php echo htmlspecialchars($_SESSION['success']); unset($_SESSION['success']); ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
        <?php endif; ?>

        <?php if (isset($_SESSION['error'])): ?>
        <div class="alert alert-danger alert-dismissible fade show" role="alert">
            <i class="fas fa-exclamation-circle me-2"></i>
            <?php echo htmlspecialchars($_SESSION['error']); unset($_SESSION['error']); ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
        <?php endif; ?>

        <!-- Dashboard Header -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h1 class="h3 mb-0">Admin Dashboard</h1>
                        <p class="text-muted">Welcome back, <?php echo htmlspecialchars($_SESSION['first_name'] ?? 'Admin'); ?>!</p>
                    </div>
                    <div class="text-end">
                        <small class="text-muted">Last updated: <?php echo date('M j, Y g:i A'); ?></small>
                    </div>
                </div>
            </div>
        </div>

        <!-- Statistics Cards -->
        <div class="row mb-4">
            <div class="col-lg-4 col-md-6 mb-3">
                <a href="users.php?type=jobseeker" class="text-decoration-none">
                    <div class="card bg-gradient-primary text-white h-100 stat-card hover-lift">
                        <div class="card-body">
                            <div class="d-flex justify-content-between">
                                <div>
                                    <h4 class="mb-0"><?php echo number_format($total_jobseekers); ?></h4>
                                    <p class="mb-0">Job Seekers</p>
                                </div>
                                <div class="align-self-center">
                                    <i class="fas fa-users fa-2x opacity-75"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </a>
            </div>
            <div class="col-lg-4 col-md-6 mb-3">
                <a href="users.php?type=business" class="text-decoration-none">
                    <div class="card bg-gradient-success text-white h-100 stat-card hover-lift">
                        <div class="card-body">
                            <div class="d-flex justify-content-between">
                                <div>
                                    <h4 class="mb-0"><?php echo number_format($total_businesses); ?></h4>
                                    <p class="mb-0">Businesses</p>
                                </div>
                                <div class="align-self-center">
                                    <i class="fas fa-building fa-2x opacity-75"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </a>
            </div>
            <div class="col-lg-4 col-md-6 mb-3">
                <a href="jobs.php" class="text-decoration-none">
                    <div class="card bg-gradient-info text-white h-100 stat-card hover-lift">
                        <div class="card-body">
                            <div class="d-flex justify-content-between">
                                <div>
                                    <h4 class="mb-0"><?php echo number_format($total_jobs); ?></h4>
                                    <p class="mb-0">Job Posts</p>
                                </div>
                                <div class="align-self-center">
                                    <i class="fas fa-briefcase fa-2x opacity-75"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </a>
            </div>
        </div>

        <div class="row">
            <!-- Pending Business Approvals -->
            <div class="col-lg-6 mb-4">
                <div class="card h-100">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <h5 class="mb-0">
                            <i class="fas fa-building me-2"></i>Pending Business Approvals
                        </h5>
                        <span class="badge bg-warning"><?php echo count($pending_businesses); ?></span>
                    </div>
                    <div class="card-body">
                        <?php if (empty($pending_businesses)): ?>
                        <div class="text-center py-4">
                            <i class="fas fa-check-circle fa-3x text-success mb-3"></i>
                            <p class="text-muted">No pending business approvals</p>
                        </div>
                        <?php else: ?>
                        <div class="table-responsive">
                            <table class="table table-sm">
                                <thead>
                                    <tr>
                                        <th>Company</th>
                                        <th>Contact</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($pending_businesses as $business): ?>
                                    <tr>
                                        <td>
                                            <strong><?php echo htmlspecialchars($business['company_name'] ?? 'Unknown Company'); ?></strong><br>
                                            <small class="text-muted"><?php echo htmlspecialchars(($business['first_name'] ?? '') . ' ' . ($business['last_name'] ?? '')); ?></small>
                                        </td>
                                        <td>
                                            <small>
                                                <?php echo htmlspecialchars($business['email'] ?? 'No email'); ?><br>
                                                <?php echo htmlspecialchars($business['phone'] ?? 'No phone'); ?>
                                            </small>
                                        </td>
                                        <td>
                                            <div class="btn-group btn-group-sm">
                                                <button type="button" class="btn btn-success" 
                                                        onclick="approveRejectBusiness(<?php echo $business['id']; ?>, 'approve')" title="Approve">
                                                    <i class="fas fa-check"></i>
                                                </button>
                                                <button type="button" class="btn btn-danger" 
                                                        onclick="approveRejectBusiness(<?php echo $business['id']; ?>, 'reject')" title="Reject">
                                                    <i class="fas fa-times"></i>
                                                </button>
                                                <button type="button" class="btn btn-info" 
                                                        onclick="viewBusinessDetails(<?php echo $business['id']; ?>)" title="View Details">
                                                    <i class="fas fa-eye"></i>
                                                </button>
                                            </div>
                                        </td>
                                    </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>

            <!-- Pending Job Approvals -->
            <div class="col-lg-6 mb-4">
                <div class="card h-100">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <h5 class="mb-0">
                            <i class="fas fa-briefcase me-2"></i>Pending Job Approvals
                        </h5>
                        <span class="badge bg-warning"><?php echo count($pending_jobs); ?></span>
                    </div>
                    <div class="card-body">
                        <?php if (empty($pending_jobs)): ?>
                        <div class="text-center py-4">
                            <i class="fas fa-check-circle fa-3x text-success mb-3"></i>
                            <p class="text-muted">No pending job approvals</p>
                        </div>
                        <?php else: ?>
                        <div class="table-responsive">
                            <table class="table table-sm">
                                <thead>
                                    <tr>
                                        <th>Job Title</th>
                                        <th>Company</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($pending_jobs as $job): ?>
                                    <tr>
                                        <td>
                                            <strong><?php echo htmlspecialchars($job['title'] ?? 'Unknown Job'); ?></strong><br>
                                            <small class="text-muted"><?php echo htmlspecialchars($job['location'] ?? 'No location'); ?></small>
                                        </td>
                                        <td>
                                            <small>
                                                <?php echo htmlspecialchars($job['company_name'] ?? 'Unknown Company'); ?><br>
                                                <span class="text-muted"><?php echo htmlspecialchars(($job['first_name'] ?? '') . ' ' . ($job['last_name'] ?? '')); ?></span>
                                            </small>
                                        </td>
                                        <td>
                                            <div class="btn-group btn-group-sm">
                                                <button type="button" class="btn btn-success" 
                                                        onclick="approveRejectJob(<?php echo $job['id']; ?>, 'approve')" title="Approve">
                                                    <i class="fas fa-check"></i>
                                                </button>
                                                <button type="button" class="btn btn-danger" 
                                                        onclick="approveRejectJob(<?php echo $job['id']; ?>, 'reject')" title="Reject">
                                                    <i class="fas fa-times"></i>
                                                </button>
                                                <button type="button" class="btn btn-info" 
                                                        onclick="viewJobDetails(<?php echo $job['id']; ?>)" title="View Details">
                                                    <i class="fas fa-eye"></i>
                                                </button>
                                            </div>
                                        </td>
                                    </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>

        <!-- Recent Activities -->
        <div class="row">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="fas fa-clock me-2"></i>Recent Activities
                        </h5>
                    </div>
                    <div class="card-body">
                        <?php if (empty($recent_activities)): ?>
                        <div class="text-center py-4">
                            <i class="fas fa-history fa-3x text-muted mb-3"></i>
                            <p class="text-muted">No recent activities</p>
                        </div>
                        <?php else: ?>
                        <div class="table-responsive">
                            <table class="table">
                                <thead>
                                    <tr>
                                        <th>Type</th>
                                        <th>Details</th>
                                        <th>Date</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($recent_activities as $activity): ?>
                                    <tr>
                                        <td>
                                            <?php if (($activity['type'] ?? '') === 'job_application'): ?>
                                            <span class="badge bg-info">
                                                <i class="fas fa-file-alt me-1"></i>Application
                                            </span>
                                            <?php else: ?>
                                            <span class="badge bg-success">
                                                <i class="fas fa-briefcase me-1"></i>Job Post
                                            </span>
                                            <?php endif; ?>
                                        </td>
                                        <td>
                                            <?php if (($activity['type'] ?? '') === 'job_application'): ?>
                                            <strong><?php echo htmlspecialchars($activity['applicant_name'] ?? 'Unknown Applicant'); ?></strong> 
                                            applied for <em><?php echo htmlspecialchars($activity['job_title'] ?? 'Unknown Job'); ?></em> 
                                            at <?php echo htmlspecialchars($activity['company_name'] ?? 'Unknown Company'); ?>
                                            <?php else: ?>
                                            New job post: <strong><?php echo htmlspecialchars($activity['job_title'] ?? 'Unknown Job'); ?></strong> 
                                            by <?php echo htmlspecialchars($activity['applicant_name'] ?? 'Unknown Company'); ?>
                                            <?php endif; ?>
                                        </td>
                                        <td>
                                            <small class="text-muted">
                                                <?php echo date('M j, Y g:i A', strtotime($activity['created_at'] ?? 'now')); ?>
                                            </small>
                                        </td>
                                        <td>
                                            <?php if (($activity['type'] ?? '') === 'job_post'): ?>
                                            <button type="button" class="btn btn-sm btn-info" 
                                                    onclick="viewJobDetails(<?php echo $activity['job_id'] ?? 0; ?>)" title="View Job Details">
                                                <i class="fas fa-eye"></i> View
                                            </button>
                                            <?php endif; ?>
                                        </td>
                                    </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>
    </div>
</main>

<!-- Hidden Forms for Actions -->
<form id="businessActionForm" method="POST" style="display: none;">
    <input type="hidden" name="action" id="businessAction">
    <input type="hidden" name="user_id" id="businessUserId">
</form>

<form id="jobActionForm" method="POST" style="display: none;">
    <input type="hidden" name="action" id="jobAction">
    <input type="hidden" name="job_id" id="jobId">
</form>

<!-- Business Details Modal -->
<div class="modal fade" id="businessDetailsModal" tabindex="-1" aria-labelledby="businessDetailsModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-xl">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="businessDetailsModalLabel">Business Details</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body" id="businessDetailsContent">
                <!-- Content will be loaded here -->
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
            </div>
        </div>
    </div>
</div>

<!-- Job Details Modal -->
<div class="modal fade" id="jobDetailsModal" tabindex="-1" aria-labelledby="jobDetailsModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-xl">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="jobDetailsModalLabel">Job Details</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body" id="jobDetailsContent">
                <!-- Content will be loaded here -->
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
            </div>
        </div>
    </div>
</div>

<script>
function approveRejectBusiness(userId, action) {
    if (confirm(`Are you sure you want to ${action} this business?`)) {
        document.getElementById('businessAction').value = action + '_business';
        document.getElementById('businessUserId').value = userId;
        document.getElementById('businessActionForm').submit();
    }
}

function approveRejectJob(jobId, action) {
    if (confirm(`Are you sure you want to ${action} this job post?`)) {
        document.getElementById('jobAction').value = action + '_job';
        document.getElementById('jobId').value = jobId;
        document.getElementById('jobActionForm').submit();
    }
}

function deleteJob(jobId) {
    if (confirm('Are you sure you want to delete this job post? This action cannot be undone.')) {
        document.getElementById('jobAction').value = 'delete_job';
        document.getElementById('jobId').value = jobId;
        document.getElementById('jobActionForm').submit();
    }
}

function viewBusinessDetails(userId) {
    // Show loading state
    document.getElementById('businessDetailsContent').innerHTML = `
        <div class="text-center py-4">
            <div class="spinner-border text-primary" role="status">
                <span class="visually-hidden">Loading...</span>
            </div>
            <p class="mt-2">Loading business details...</p>
        </div>
    `;
    
    // Show the modal
    const modal = new bootstrap.Modal(document.getElementById('businessDetailsModal'));
    modal.show();
    
    // Fetch business details via AJAX
    fetch(`get_business_details.php?user_id=${userId}`)
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                const business = data.business;
                document.getElementById('businessDetailsContent').innerHTML = `
                    <div class="row g-3">
                        <div class="col-md-6">
                            <h6 class="text-primary mb-3">Company Information</h6>
                            <table class="table table-sm">
                                <tbody>
                                    <tr><td class="fw-bold" style="width: 45%;">Company Name:</td><td>${business.company_name || 'N/A'}</td></tr>
                                    <tr><td class="fw-bold">Business Description:</td><td>${business.business_description || 'N/A'}</td></tr>
                                    <tr><td class="fw-bold">DTI Number:</td><td>${business.dti_number || 'N/A'}</td></tr>
                                    <tr><td class="fw-bold">Mayor's Permit:</td><td>${business.mayors_permit || 'N/A'}</td></tr>
                                </tbody>
                            </table>
                        </div>
                        <div class="col-md-6">
                            <h6 class="text-primary mb-3">Contact Information</h6>
                            <table class="table table-sm">
                                <tbody>
                                    <tr><td class="fw-bold" style="width: 45%;">Contact Person:</td><td>${business.first_name} ${business.last_name}</td></tr>
                                    <tr><td class="fw-bold">Email:</td><td>${business.email || 'N/A'}</td></tr>
                                    <tr><td class="fw-bold">Phone:</td><td>${business.phone || 'N/A'}</td></tr>
                                    <tr><td class="fw-bold">Registration Date:</td><td>${new Date(business.created_at).toLocaleDateString()}</td></tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                    <div class="row mt-3">
                        <div class="col-12">
                            <h6 class="text-primary mb-3">Documents</h6>
                            <div class="d-flex gap-2">
                                ${business.dti_number ? `<a href="uploads/business_documents/${business.dti_number}" target="_blank" class="btn btn-sm btn-outline-primary"><i class="fas fa-file-pdf me-1"></i>DTI Certificate</a>` : ''}
                                ${business.mayors_permit ? `<a href="uploads/business_documents/${business.mayors_permit}" target="_blank" class="btn btn-sm btn-outline-primary"><i class="fas fa-file-pdf me-1"></i>Mayor's Permit</a>` : ''}
                            </div>
                        </div>
                    </div>
                `;
            } else {
                document.getElementById('businessDetailsContent').innerHTML = `
                    <div class="alert alert-danger">
                        <i class="fas fa-exclamation-triangle me-2"></i>
                        Failed to load business details. Please try again.
                    </div>
                `;
            }
        })
        .catch(error => {
            console.error('Error:', error);
            document.getElementById('businessDetailsContent').innerHTML = `
                <div class="alert alert-danger">
                    <i class="fas fa-exclamation-triangle me-2"></i>
                    Error loading business details. Please try again.
                </div>
            `;
        });
}

function viewJobDetails(jobId) {
    // Show loading state
    document.getElementById('jobDetailsContent').innerHTML = `
        <div class="text-center py-4">
            <div class="spinner-border text-primary" role="status">
                <span class="visually-hidden">Loading...</span>
            </div>
            <p class="mt-2">Loading job details...</p>
        </div>
    `;
    
    // Show the modal
    const modal = new bootstrap.Modal(document.getElementById('jobDetailsModal'));
    modal.show();
    
    // Fetch job details via AJAX
    fetch(`get_job_details.php?job_id=${jobId}`)
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                const job = data.job;
                document.getElementById('jobDetailsContent').innerHTML = `
                    <div class="row g-3">
                        <div class="col-md-6">
                            <h6 class="text-primary mb-3">Job Information</h6>
                            <table class="table table-sm">
                                <tbody>
                                    <tr><td class="fw-bold" style="width: 45%;">Job Title:</td><td>${job.title || 'N/A'}</td></tr>
                                    <tr><td class="fw-bold">Company:</td><td>${job.company_name || 'N/A'}</td></tr>
                                    <tr><td class="fw-bold">Location:</td><td>${job.location || 'N/A'}</td></tr>
                                    <tr><td class="fw-bold">Job Type:</td><td>${job.job_type || 'N/A'}</td></tr>
                                    <tr><td class="fw-bold">Salary Range:</td><td>₱${job.salary_min || '0'} - ₱${job.salary_max || '0'}</td></tr>
                                </tbody>
                            </table>
                        </div>
                        <div class="col-md-6">
                            <h6 class="text-primary mb-3">Additional Details</h6>
                            <table class="table table-sm">
                                <tbody>
                                    <tr><td class="fw-bold" style="width: 45%;">Posted By:</td><td>${job.first_name} ${job.last_name}</td></tr>
                                    <tr><td class="fw-bold">Posted Date:</td><td>${new Date(job.created_at).toLocaleDateString()}</td></tr>
                                    <tr><td class="fw-bold">Status:</td><td><span class="badge bg-${job.status === 'approved' ? 'success' : job.status === 'rejected' ? 'danger' : 'warning'}">${job.status || 'Pending'}</span></td></tr>
                                    <tr><td class="fw-bold">Experience:</td><td>${job.experience_level || 'N/A'}</td></tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                    ${job.job_image ? `
                    <div class="row mt-3">
                        <div class="col-12">
                            <h6 class="text-primary mb-3">Job Image</h6>
                            <div class="text-center">
                                <img src="../uploads/job_images/${job.job_image}" alt="Job Image" class="img-fluid rounded" style="max-height: 300px; max-width: 100%;">
                            </div>
                        </div>
                    </div>
                    ` : ''}
                    <div class="row mt-3">
                        <div class="col-12">
                            <h6 class="text-primary mb-3">Job Description</h6>
                            <div class="border rounded p-3 bg-light">
                                ${job.description || 'No description provided'}
                            </div>
                        </div>
                    </div>
                    <div class="row mt-3">
                        <div class="col-12">
                            <h6 class="text-primary mb-3">Requirements</h6>
                            <div class="border rounded p-3 bg-light">
                                ${job.skills_required || 'No specific requirements listed'}
                            </div>
                        </div>
                    </div>
                `;
            } else {
                document.getElementById('jobDetailsContent').innerHTML = `
                    <div class="alert alert-danger">
                        <i class="fas fa-exclamation-triangle me-2"></i>
                        Failed to load job details. Please try again.
                    </div>
                `;
            }
        })
        .catch(error => {
            console.error('Error:', error);
            document.getElementById('jobDetailsContent').innerHTML = `
                <div class="alert alert-danger">
                    <i class="fas fa-exclamation-triangle me-2"></i>
                    Error loading job details. Please try again.
                </div>
            `;
        });
}

// Auto-refresh every 30 seconds
setTimeout(function() {
    location.reload();
}, 30000);
</script>

<?php include __DIR__ . '/../includes/footer.php'; ?>
