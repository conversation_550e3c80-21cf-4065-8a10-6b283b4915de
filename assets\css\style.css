/* Custom CSS for Tan-Aw Job Portal */

/* Root Variables */
:root {
  --primary-color: #007bff;
  --secondary-color: #6c757d;
  --success-color: #28a745;
  --danger-color: #dc3545;
  --warning-color: #ffc107;
  --info-color: #17a2b8;
  --light-color: #f8f9fa;
  --dark-color: #343a40;
  --font-family: "Segoe UI", Tahoma, Geneva, Verdana, sans-serif;
}

/* Global Styles */
body {
  font-family: var(--font-family);
  line-height: 1.6;
  color: var(--dark-color);
  background-color: #ffffff;
}

/* Theme Transition */
.theme-transition,
.theme-transition *,
.theme-transition *:before,
.theme-transition *:after {
  transition: background-color 0.3s ease,
              color 0.3s ease,
              border-color 0.3s ease,
              box-shadow 0.3s ease !important;
  transition-delay: 0s !important;
}

/* Modern Dark Theme - Inspired by GitHub Dark & Discord */
[data-theme="dark"] {
  /* Primary Brand Colors */
  --primary-color: #58a6ff;
  --primary-hover: #4493e6;
  --primary-active: #3182ce;

  /* Semantic Colors */
  --secondary-color: #8b949e;
  --success-color: #3fb950;
  --danger-color: #f85149;
  --warning-color: #d29922;
  --info-color: #58a6ff;

  /* Background Colors - Softer, more sophisticated */
  --bg-primary: #0d1117;        /* Main background - GitHub dark */
  --bg-secondary: #161b22;      /* Cards, modals */
  --bg-tertiary: #21262d;       /* Elevated elements */
  --bg-quaternary: #30363d;     /* Highest elevation */
  --bg-overlay: rgba(13, 17, 23, 0.8); /* Overlay backgrounds */

  /* Text Colors - Better contrast ratios */
  --text-primary: #f0f6fc;      /* High contrast white */
  --text-secondary: #8b949e;    /* Secondary text */
  --text-muted: #6e7681;        /* Muted text */
  --text-disabled: #484f58;     /* Disabled text */

  /* Border & Divider Colors */
  --border-color: #30363d;      /* Subtle borders */
  --border-muted: #21262d;      /* Very subtle borders */
  --border-strong: #6e7681;     /* Prominent borders */

  /* Interactive States */
  --hover-bg: rgba(177, 186, 196, 0.12);
  --active-bg: rgba(177, 186, 196, 0.2);
  --focus-ring: #58a6ff;

  /* Shadows - Softer, more natural */
  --shadow-sm: 0 1px 3px rgba(0, 0, 0, 0.4);
  --shadow-md: 0 4px 12px rgba(0, 0, 0, 0.3);
  --shadow-lg: 0 8px 24px rgba(0, 0, 0, 0.4);
  --shadow-xl: 0 16px 48px rgba(0, 0, 0, 0.5);

  /* Legacy compatibility */
  --light-color: var(--bg-secondary);
  --dark-color: var(--text-primary);
}

/* Dark Theme Body & Base Elements */
[data-theme="dark"] body {
  background-color: var(--bg-primary);
  color: var(--text-primary);
  transition: background-color 0.3s cubic-bezier(0.4, 0, 0.2, 1),
              color 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

[data-theme="dark"] h1,
[data-theme="dark"] h2,
[data-theme="dark"] h3,
[data-theme="dark"] h4,
[data-theme="dark"] h5,
[data-theme="dark"] h6 {
  color: var(--text-primary);
}

[data-theme="dark"] p {
  color: var(--text-secondary);
}

[data-theme="dark"] small,
[data-theme="dark"] .small {
  color: var(--text-muted);
}

[data-theme="dark"] hr {
  border-color: var(--border-color);
  opacity: 1;
}

/* Enhanced Dark Theme Navigation */
[data-theme="dark"] .navbar {
  background-color: rgba(22, 27, 34, 0.95) !important;
  backdrop-filter: blur(12px);
  border-bottom: 1px solid var(--border-color);
  box-shadow: var(--shadow-md);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

[data-theme="dark"] .navbar-brand {
  color: var(--text-primary) !important;
  font-weight: 700;
  transition: color 0.2s ease;
}

[data-theme="dark"] .navbar-brand:hover {
  color: var(--primary-color) !important;
}

[data-theme="dark"] .nav-link {
  color: var(--text-secondary) !important;
  font-weight: 500;
  position: relative;
  transition: all 0.2s ease;
  border-radius: 6px;
  padding: 8px 12px !important;
}

[data-theme="dark"] .nav-link:hover {
  color: var(--text-primary) !important;
  background-color: var(--hover-bg);
}

[data-theme="dark"] .nav-link.active {
  color: var(--primary-color) !important;
  background-color: rgba(88, 166, 255, 0.1);
}

[data-theme="dark"] .nav-link.active::after {
  content: '';
  position: absolute;
  bottom: -1px;
  left: 50%;
  transform: translateX(-50%);
  width: 20px;
  height: 2px;
  background-color: var(--primary-color);
  border-radius: 1px;
}

[data-theme="dark"] .navbar-toggler {
  border-color: var(--border-color);
  padding: 6px 8px;
  border-radius: 6px;
  transition: all 0.2s ease;
}

[data-theme="dark"] .navbar-toggler:hover {
  background-color: var(--hover-bg);
  border-color: var(--border-strong);
}

[data-theme="dark"] .navbar-toggler:focus {
  box-shadow: 0 0 0 2px var(--focus-ring);
}

[data-theme="dark"] .navbar-toggler-icon {
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 30 30'%3e%3cpath stroke='rgba%28240, 246, 252, 0.8%29' stroke-linecap='round' stroke-miterlimit='10' stroke-width='2' d='m4 7h22M4 15h22M4 23h22'/%3e%3c/svg%3e");
}

/* Enhanced Dark Theme Cards */
[data-theme="dark"] .card {
  background-color: var(--bg-secondary);
  border: 1px solid var(--border-color);
  color: var(--text-primary);
  box-shadow: var(--shadow-sm);
  border-radius: 8px;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

[data-theme="dark"] .card:hover {
  border-color: var(--border-strong);
  box-shadow: var(--shadow-md);
  transform: translateY(-1px);
}

[data-theme="dark"] .card-header {
  background-color: var(--bg-tertiary);
  border-bottom: 1px solid var(--border-color);
  color: var(--text-primary);
  font-weight: 600;
  border-radius: 8px 8px 0 0;
}

[data-theme="dark"] .card-footer {
  background-color: var(--bg-tertiary);
  border-top: 1px solid var(--border-color);
  color: var(--text-secondary);
  border-radius: 0 0 8px 8px;
}

[data-theme="dark"] .card-body {
  color: var(--text-secondary);
}

[data-theme="dark"] .card-title {
  color: var(--text-primary);
  font-weight: 600;
}

[data-theme="dark"] .card-subtitle {
  color: var(--text-muted);
}

[data-theme="dark"] .card-text {
  color: var(--text-secondary);
  line-height: 1.6;
}

/* Dark Theme Tables */
[data-theme="dark"] .table {
  --bs-table-bg: var(--bg-secondary);
  --bs-table-color: var(--text-primary);
  --bs-table-border-color: var(--border-color);
  --bs-table-striped-bg: var(--bg-tertiary);
  --bs-table-hover-bg: var(--bg-tertiary);
}

[data-theme="dark"] .table thead th {
  border-bottom-color: var(--border-color);
  background-color: var(--bg-tertiary);
}

/* Enhanced Dark Theme Forms */
[data-theme="dark"] .form-control {
  background-color: var(--bg-tertiary);
  border: 1px solid var(--border-color);
  color: var(--text-primary);
  border-radius: 6px;
  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
  font-size: 14px;
  padding: 10px 12px;
}

[data-theme="dark"] .form-control:hover {
  border-color: var(--border-strong);
}

[data-theme="dark"] .form-control:focus {
  background-color: var(--bg-tertiary);
  border-color: var(--primary-color);
  color: var(--text-primary);
  box-shadow: 0 0 0 3px rgba(88, 166, 255, 0.12);
  outline: none;
}

[data-theme="dark"] .form-control::placeholder {
  color: var(--text-muted);
  opacity: 1;
}

[data-theme="dark"] .form-control:disabled {
  background-color: var(--bg-secondary);
  border-color: var(--border-muted);
  color: var(--text-disabled);
  opacity: 0.6;
}

[data-theme="dark"] .form-select {
  background-color: var(--bg-tertiary);
  border: 1px solid var(--border-color);
  color: var(--text-primary);
  border-radius: 6px;
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16'%3e%3cpath fill='none' stroke='%23f0f6fc' stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='m1 6 7 7 7-7'/%3e%3c/svg%3e");
  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
}

[data-theme="dark"] .form-select:hover {
  border-color: var(--border-strong);
}

[data-theme="dark"] .form-select:focus {
  border-color: var(--primary-color);
  box-shadow: 0 0 0 3px rgba(88, 166, 255, 0.12);
  outline: none;
}

[data-theme="dark"] .form-check-input {
  background-color: var(--bg-tertiary);
  border: 1px solid var(--border-color);
  border-radius: 4px;
  transition: all 0.2s ease;
}

[data-theme="dark"] .form-check-input:hover {
  border-color: var(--border-strong);
}

[data-theme="dark"] .form-check-input:checked {
  background-color: var(--primary-color);
  border-color: var(--primary-color);
}

[data-theme="dark"] .form-check-input:focus {
  box-shadow: 0 0 0 3px rgba(88, 166, 255, 0.12);
}

[data-theme="dark"] .form-label {
  color: var(--text-primary);
  font-weight: 500;
  margin-bottom: 6px;
}

[data-theme="dark"] .form-text {
  color: var(--text-muted);
  font-size: 13px;
}

[data-theme="dark"] .invalid-feedback {
  color: var(--danger-color);
}

[data-theme="dark"] .valid-feedback {
  color: var(--success-color);
}

/* Enhanced Dark Theme Buttons */
[data-theme="dark"] .btn {
  border-radius: 6px;
  font-weight: 500;
  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
  border-width: 1px;
}

[data-theme="dark"] .btn:focus {
  box-shadow: 0 0 0 3px rgba(88, 166, 255, 0.12);
}

/* Primary Button */
[data-theme="dark"] .btn-primary {
  background-color: var(--primary-color);
  border-color: var(--primary-color);
  color: #ffffff;
}

[data-theme="dark"] .btn-primary:hover {
  background-color: var(--primary-hover);
  border-color: var(--primary-hover);
  transform: translateY(-1px);
  box-shadow: var(--shadow-md);
}

[data-theme="dark"] .btn-primary:active {
  background-color: var(--primary-active);
  border-color: var(--primary-active);
  transform: translateY(0);
}

/* Outline Primary */
[data-theme="dark"] .btn-outline-primary {
  color: var(--primary-color);
  border-color: var(--primary-color);
  background-color: transparent;
}

[data-theme="dark"] .btn-outline-primary:hover {
  background-color: var(--primary-color);
  border-color: var(--primary-color);
  color: #ffffff;
  transform: translateY(-1px);
}

/* Secondary Button */
[data-theme="dark"] .btn-secondary {
  background-color: var(--bg-tertiary);
  border-color: var(--border-color);
  color: var(--text-primary);
}

[data-theme="dark"] .btn-secondary:hover {
  background-color: var(--bg-quaternary);
  border-color: var(--border-strong);
  color: var(--text-primary);
  transform: translateY(-1px);
}

/* Outline Secondary */
[data-theme="dark"] .btn-outline-secondary {
  color: var(--text-secondary);
  border-color: var(--border-color);
  background-color: transparent;
}

[data-theme="dark"] .btn-outline-secondary:hover {
  background-color: var(--bg-tertiary);
  border-color: var(--border-strong);
  color: var(--text-primary);
  transform: translateY(-1px);
}

/* Light Button */
[data-theme="dark"] .btn-light {
  background-color: var(--bg-secondary);
  border-color: var(--border-color);
  color: var(--text-primary);
}

[data-theme="dark"] .btn-light:hover {
  background-color: var(--bg-tertiary);
  border-color: var(--border-strong);
  color: var(--text-primary);
  transform: translateY(-1px);
}

/* Success Button */
[data-theme="dark"] .btn-success {
  background-color: var(--success-color);
  border-color: var(--success-color);
}

[data-theme="dark"] .btn-success:hover {
  background-color: #2ea043;
  border-color: #2ea043;
  transform: translateY(-1px);
}

/* Danger Button */
[data-theme="dark"] .btn-danger {
  background-color: var(--danger-color);
  border-color: var(--danger-color);
}

[data-theme="dark"] .btn-danger:hover {
  background-color: #e5484d;
  border-color: #e5484d;
  transform: translateY(-1px);
}

/* Button Sizes */
[data-theme="dark"] .btn-sm {
  padding: 6px 12px;
  font-size: 13px;
}

[data-theme="dark"] .btn-lg {
  padding: 12px 24px;
  font-size: 16px;
}

/* Dark Theme Dropdowns */
[data-theme="dark"] .dropdown-menu {
  background-color: var(--bg-secondary);
  border-color: var(--border-color);
  box-shadow: 0 0.5rem 1rem var(--shadow-color);
}

[data-theme="dark"] .dropdown-item {
  color: var(--text-primary);
}

[data-theme="dark"] .dropdown-item:hover,
[data-theme="dark"] .dropdown-item:focus {
  background-color: var(--bg-tertiary);
  color: var(--text-primary);
}

[data-theme="dark"] .dropdown-divider {
  border-top-color: var(--border-color);
}

[data-theme="dark"] .dropdown-header {
  color: var(--text-secondary);
}

/* Dark Theme Modals */
[data-theme="dark"] .modal-content {
  background-color: var(--bg-secondary);
  border-color: var(--border-color);
  color: var(--text-primary);
}

[data-theme="dark"] .modal-header {
  border-bottom-color: var(--border-color);
}

[data-theme="dark"] .modal-footer {
  border-top-color: var(--border-color);
}

[data-theme="dark"] .modal-title {
  color: var(--text-primary);
}

[data-theme="dark"] .btn-close {
  filter: invert(1) grayscale(100%) brightness(200%);
}

/* Dark Theme Alerts */
[data-theme="dark"] .alert-primary {
  background-color: rgba(77, 171, 247, 0.1);
  border-color: rgba(77, 171, 247, 0.2);
  color: var(--primary-color);
}

[data-theme="dark"] .alert-success {
  background-color: rgba(81, 207, 102, 0.1);
  border-color: rgba(81, 207, 102, 0.2);
  color: var(--success-color);
}

[data-theme="dark"] .alert-danger {
  background-color: rgba(255, 107, 107, 0.1);
  border-color: rgba(255, 107, 107, 0.2);
  color: var(--danger-color);
}

[data-theme="dark"] .alert-warning {
  background-color: rgba(255, 212, 59, 0.1);
  border-color: rgba(255, 212, 59, 0.2);
  color: var(--warning-color);
}

[data-theme="dark"] .alert-info {
  background-color: rgba(116, 192, 252, 0.1);
  border-color: rgba(116, 192, 252, 0.2);
  color: var(--info-color);
}

/* Dark Theme Footer */
[data-theme="dark"] .footer {
  background-color: var(--bg-secondary) !important;
  border-top: 1px solid var(--border-color);
}

[data-theme="dark"] .footer h5,
[data-theme="dark"] .footer h6 {
  color: var(--text-primary);
}

[data-theme="dark"] .footer p,
[data-theme="dark"] .footer li {
  color: var(--text-secondary);
}

[data-theme="dark"] .footer a {
  color: var(--text-secondary);
  transition: color 0.3s ease;
}

[data-theme="dark"] .footer a:hover {
  color: var(--primary-color);
}

/* Dark Theme Badges */
[data-theme="dark"] .badge {
  color: var(--bg-primary);
}

[data-theme="dark"] .badge.bg-light {
  background-color: var(--bg-tertiary) !important;
  color: var(--text-primary);
}

/* Dark Theme Pagination */
[data-theme="dark"] .page-link {
  background-color: var(--bg-secondary);
  border-color: var(--border-color);
  color: var(--text-primary);
}

[data-theme="dark"] .page-link:hover {
  background-color: var(--bg-tertiary);
  border-color: var(--border-color);
  color: var(--primary-color);
}

[data-theme="dark"] .page-item.active .page-link {
  background-color: var(--primary-color);
  border-color: var(--primary-color);
  color: var(--bg-primary);
}

[data-theme="dark"] .page-item.disabled .page-link {
  background-color: var(--bg-secondary);
  border-color: var(--border-color);
  color: var(--text-muted);
}

/* Dark Theme Hero Section */
[data-theme="dark"] .hero-section {
  background: linear-gradient(135deg, #000000 0%, #1a1a1a 50%, #2d2d2d 100%);
  color: var(--text-primary);
  position: relative;
  overflow: hidden;
}

[data-theme="dark"] .hero-section::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: radial-gradient(circle at 30% 20%, rgba(255, 193, 7, 0.15) 0%, transparent 50%),
              radial-gradient(circle at 70% 80%, rgba(40, 167, 69, 0.1) 0%, transparent 50%),
              radial-gradient(circle at 50% 50%, rgba(255, 255, 255, 0.02) 0%, transparent 50%);
  pointer-events: none;
}

[data-theme="dark"] .hero-section h1,
[data-theme="dark"] .hero-section h2,
[data-theme="dark"] .hero-section h3 {
  color: #ffffff;
  font-weight: 700;
  text-shadow: 0 2px 8px rgba(0, 0, 0, 0.8);
  letter-spacing: 0.5px;
}

[data-theme="dark"] .hero-section p {
  color: rgba(255, 255, 255, 0.9);
  font-size: 1.2rem;
  line-height: 1.8;
  text-shadow: 0 1px 4px rgba(0, 0, 0, 0.6);
  font-weight: 400;
}

/* Dark Theme Breadcrumbs */
[data-theme="dark"] .breadcrumb {
  background-color: var(--bg-secondary);
}

[data-theme="dark"] .breadcrumb-item a {
  color: var(--primary-color);
}

[data-theme="dark"] .breadcrumb-item.active {
  color: var(--text-secondary);
}

/* Dark Theme List Groups */
[data-theme="dark"] .list-group-item {
  background-color: var(--bg-secondary);
  border-color: var(--border-color);
  color: var(--text-primary);
}

[data-theme="dark"] .list-group-item:hover {
  background-color: var(--bg-tertiary);
}

[data-theme="dark"] .list-group-item.active {
  background-color: var(--primary-color);
  border-color: var(--primary-color);
  color: var(--bg-primary);
}

/* Dark Theme Progress Bars */
[data-theme="dark"] .progress {
  background-color: var(--bg-tertiary);
}

[data-theme="dark"] .progress-bar {
  background-color: var(--primary-color);
}

/* Dark Theme Tooltips */
[data-theme="dark"] .tooltip .tooltip-inner {
  background-color: var(--bg-tertiary);
  color: var(--text-primary);
}

[data-theme="dark"] .tooltip.bs-tooltip-top .tooltip-arrow::before {
  border-top-color: var(--bg-tertiary);
}

[data-theme="dark"] .tooltip.bs-tooltip-bottom .tooltip-arrow::before {
  border-bottom-color: var(--bg-tertiary);
}

[data-theme="dark"] .tooltip.bs-tooltip-start .tooltip-arrow::before {
  border-left-color: var(--bg-tertiary);
}

[data-theme="dark"] .tooltip.bs-tooltip-end .tooltip-arrow::before {
  border-right-color: var(--bg-tertiary);
}

/* Dark Theme Popovers */
[data-theme="dark"] .popover {
  background-color: var(--bg-secondary);
  border-color: var(--border-color);
  color: var(--text-primary);
}

[data-theme="dark"] .popover-header {
  background-color: var(--bg-tertiary);
  border-bottom-color: var(--border-color);
  color: var(--text-primary);
}

[data-theme="dark"] .popover-body {
  color: var(--text-primary);
}

/* Dark Theme Scrollbar */
[data-theme="dark"] ::-webkit-scrollbar-track {
  background: var(--bg-secondary);
}

[data-theme="dark"] ::-webkit-scrollbar-thumb {
  background: linear-gradient(45deg, var(--primary-color), var(--info-color));
}

[data-theme="dark"] ::-webkit-scrollbar-thumb:hover {
  background: var(--primary-color);
}

/* Dark Theme Text Colors */
[data-theme="dark"] .text-muted {
  color: var(--text-muted) !important;
}

[data-theme="dark"] .text-dark {
  color: var(--text-primary) !important;
}

[data-theme="dark"] .text-light {
  color: var(--text-secondary) !important;
}

/* Dark Theme Background Colors */
[data-theme="dark"] .bg-light {
  background-color: var(--bg-secondary) !important;
}

[data-theme="dark"] .bg-dark {
  background-color: var(--bg-primary) !important;
}

[data-theme="dark"] .bg-white {
  background-color: var(--bg-secondary) !important;
}

/* Enhanced Theme Toggle Button */
#themeToggle {
  position: relative;
  background: transparent;
  border: 2px solid var(--border-color, #dee2e6);
  border-radius: 50% !important;
  width: 44px;
  height: 44px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer !important;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  overflow: hidden;
  padding: 0 !important;
  min-width: 44px !important;
  min-height: 44px !important;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

#themeToggle:hover {
  border-color: var(--primary-color, #007bff) !important;
  background-color: var(--hover-bg, rgba(0, 123, 255, 0.1)) !important;
  transform: scale(1.05);
  cursor: pointer !important;
  box-shadow: 0 4px 16px rgba(0, 123, 255, 0.3);
}

#themeToggle:focus {
  outline: none;
  box-shadow: 0 0 0 3px var(--focus-ring, rgba(0, 123, 255, 0.25)) !important;
  cursor: pointer !important;
}

#themeToggle:active {
  transform: scale(0.95);
  cursor: pointer !important;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
}

#themeToggle .theme-toggle-icon {
  font-size: 18px;
  color: var(--text-secondary, #6c757d);
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
}

#themeToggle:hover .theme-toggle-icon {
  color: var(--primary-color, #007bff);
  transform: rotate(15deg);
}

/* Dark theme toggle styles */
[data-theme="dark"] #themeToggle {
  border-color: var(--border-color);
  background: var(--bg-tertiary);
}

[data-theme="dark"] #themeToggle:hover {
  border-color: var(--primary-color) !important;
  background-color: var(--hover-bg) !important;
  box-shadow: var(--shadow-sm);
}

[data-theme="dark"] #themeToggle:focus {
  box-shadow: 0 0 0 3px rgba(88, 166, 255, 0.12) !important;
}

[data-theme="dark"] #themeToggle .theme-toggle-icon {
  color: var(--text-secondary);
}

[data-theme="dark"] #themeToggle:hover .theme-toggle-icon {
  color: var(--primary-color);
}

/* Theme switching animation */
.theme-switching {
  pointer-events: none;
}

.theme-switching .theme-toggle-icon {
  animation: themeSwitch 0.6s cubic-bezier(0.4, 0, 0.2, 1);
}

@keyframes themeSwitch {
  0% { transform: rotate(0deg) scale(1); }
  50% { transform: rotate(180deg) scale(1.2); }
  100% { transform: rotate(360deg) scale(1); }
}

/* Tooltip for theme toggle */
.theme-toggle-tooltip {
  position: absolute;
  bottom: -35px;
  left: 50%;
  transform: translateX(-50%);
  background: var(--bg-tertiary, #333);
  color: var(--text-primary, #fff);
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
  white-space: nowrap;
  opacity: 0;
  pointer-events: none;
  transition: opacity 0.2s ease;
  z-index: 1000;
}

.theme-toggle:hover .theme-toggle-tooltip {
  opacity: 1;
}

[data-theme="dark"] .theme-toggle-tooltip {
  background: var(--bg-quaternary);
  color: var(--text-primary);
}

/* Enhanced Footer Styling */
[data-theme="dark"] .footer {
  background: linear-gradient(135deg, var(--bg-secondary) 0%, var(--bg-tertiary) 100%) !important;
  border-top: 1px solid var(--border-color);
  backdrop-filter: blur(10px);
}

[data-theme="dark"] .footer h5,
[data-theme="dark"] .footer h6 {
  color: var(--text-primary);
  font-weight: 600;
}

[data-theme="dark"] .footer p,
[data-theme="dark"] .footer li {
  color: var(--text-secondary);
  line-height: 1.6;
}

[data-theme="dark"] .footer a {
  color: var(--text-secondary);
  text-decoration: none;
  transition: all 0.2s ease;
  border-radius: 4px;
  padding: 2px 4px;
}

[data-theme="dark"] .footer a:hover {
  color: var(--primary-color);
  background-color: var(--hover-bg);
  text-decoration: none;
}

/* Enhanced Hero Section */
[data-theme="dark"] .hero-section {
  background: linear-gradient(135deg, #000000 0%, #1a1a1a 50%, #2d2d2d 100%);
  color: var(--text-primary);
  position: relative;
  overflow: hidden;
}

[data-theme="dark"] .hero-section::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: radial-gradient(circle at 30% 20%, rgba(255, 193, 7, 0.15) 0%, transparent 50%),
              radial-gradient(circle at 70% 80%, rgba(40, 167, 69, 0.1) 0%, transparent 50%),
              radial-gradient(circle at 50% 50%, rgba(255, 255, 255, 0.02) 0%, transparent 50%);
  pointer-events: none;
}

[data-theme="dark"] .hero-section h1,
[data-theme="dark"] .hero-section h2,
[data-theme="dark"] .hero-section h3 {
  color: #ffffff;
  font-weight: 700;
  text-shadow: 0 2px 8px rgba(0, 0, 0, 0.8);
  letter-spacing: 0.5px;
}

[data-theme="dark"] .hero-section p {
  color: rgba(255, 255, 255, 0.9);
  font-size: 1.2rem;
  line-height: 1.8;
  text-shadow: 0 1px 4px rgba(0, 0, 0, 0.6);
  font-weight: 400;
}

/* Enhanced Alert Styling */
[data-theme="dark"] .alert {
  border-radius: 8px;
  border-width: 1px;
  backdrop-filter: blur(10px);
}

[data-theme="dark"] .alert-primary {
  background-color: rgba(88, 166, 255, 0.1);
  border-color: rgba(88, 166, 255, 0.3);
  color: var(--primary-color);
}

[data-theme="dark"] .alert-success {
  background-color: rgba(63, 185, 80, 0.1);
  border-color: rgba(63, 185, 80, 0.3);
  color: var(--success-color);
}

[data-theme="dark"] .alert-danger {
  background-color: rgba(248, 81, 73, 0.1);
  border-color: rgba(248, 81, 73, 0.3);
  color: var(--danger-color);
}

[data-theme="dark"] .alert-warning {
  background-color: rgba(210, 153, 34, 0.1);
  border-color: rgba(210, 153, 34, 0.3);
  color: var(--warning-color);
}

[data-theme="dark"] .alert-info {
  background-color: rgba(88, 166, 255, 0.1);
  border-color: rgba(88, 166, 255, 0.3);
  color: var(--info-color);
}

/* Screen Reader Only Class */
.sr-only {
  position: absolute !important;
  width: 1px !important;
  height: 1px !important;
  padding: 0 !important;
  margin: -1px !important;
  overflow: hidden !important;
  clip: rect(0, 0, 0, 0) !important;
  white-space: nowrap !important;
  border: 0 !important;
}

/* Navigation */
.navbar {
  background-color: rgba(255, 255, 255, 0.95) !important;
  backdrop-filter: blur(10px);
  box-shadow: 0 2px 20px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
}

.navbar-brand {
  font-weight: 700;
  font-size: 1.8rem !important;
  transition: transform 0.3s ease;
}

.navbar-brand:hover {
  transform: scale(1.05);
}

.nav-link {
  font-weight: 500;
  transition: all 0.3s ease;
  position: relative;
}

.nav-link:hover {
  color: var(--primary-color) !important;
  transform: translateY(-2px);
}

.nav-link.active {
  color: var(--primary-color) !important;
  font-weight: 600;
}

.nav-link.active::after {
  content: "";
  position: absolute;
  bottom: -5px;
  left: 50%;
  transform: translateX(-50%);
  width: 30px;
  height: 3px;
  background-color: var(--primary-color);
  border-radius: 2px;
}

/* Hero Section */
.hero-section {
  background: linear-gradient(135deg, #1a1a1a 0%, #2d2d2d 50%, #404040 100%);
  min-height: 100vh;
  display: flex;
  align-items: center;
  position: relative;
  overflow: hidden;
}

.hero-section::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: radial-gradient(circle at 30% 20%, rgba(255, 193, 7, 0.1) 0%, transparent 50%),
              radial-gradient(circle at 70% 80%, rgba(40, 167, 69, 0.05) 0%, transparent 50%);
  opacity: 0.4;
  z-index: 1;
}

.hero-content {
  position: relative;
  z-index: 2;
}

.hero-stats {
  position: relative;
  z-index: 2;
}

/* Animations */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideInLeft {
  from {
    opacity: 0;
    transform: translateX(-50px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes slideInRight {
  from {
    opacity: 0;
    transform: translateX(50px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes slideInUp {
  from {
    opacity: 0;
    transform: translateY(50px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes pulse {
  0% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.05);
  }
  100% {
    transform: scale(1);
  }
}

@keyframes bounce {
  0%,
  20%,
  50%,
  80%,
  100% {
    transform: translateY(0);
  }
  40% {
    transform: translateY(-10px);
  }
  60% {
    transform: translateY(-5px);
  }
}

/* Animation Classes */
.fade-in {
  animation: fadeIn 0.8s ease-out;
}

.slide-in-left {
  animation: slideInLeft 0.8s ease-out;
}

.slide-in-right {
  animation: slideInRight 0.8s ease-out;
}

.slide-in-up {
  animation: slideInUp 0.8s ease-out;
}

.pulse-animation {
  animation: pulse 2s infinite;
}

.bounce-animation {
  animation: bounce 2s infinite;
}

/* Staggered animations */
.stagger-animation > * {
  opacity: 0;
  animation: fadeIn 0.6s ease-out forwards;
}

.stagger-animation > *:nth-child(1) {
  animation-delay: 0.1s;
}
.stagger-animation > *:nth-child(2) {
  animation-delay: 0.2s;
}
.stagger-animation > *:nth-child(3) {
  animation-delay: 0.3s;
}
.stagger-animation > *:nth-child(4) {
  animation-delay: 0.4s;
}
.stagger-animation > *:nth-child(5) {
  animation-delay: 0.5s;
}
.stagger-animation > *:nth-child(6) {
  animation-delay: 0.6s;
}

/* Cards */
.card {
  border: none;
  border-radius: 15px;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.08);
  transition: all 0.3s ease;
  overflow: hidden;
}

.card:hover {
  transform: translateY(-5px);
  box-shadow: 0 15px 35px rgba(0, 0, 0, 0.15);
}

.job-card {
  transition: all 0.3s ease;
  border: 1px solid #e9ecef;
}

.job-card:hover {
  transform: translateY(-8px);
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
  border-color: var(--primary-color);
}

/* Buttons */
.btn {
  border-radius: 25px;
  font-weight: 500;
  padding: 0.5rem 1.5rem;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.btn::before {
  content: "";
  position: absolute;
  top: 50%;
  left: 50%;
  width: 0;
  height: 0;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 50%;
  transform: translate(-50%, -50%);
  transition: width 0.6s, height 0.6s;
}

.btn:hover::before {
  width: 300px;
  height: 300px;
}

.btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
}

.btn-primary {
  background: linear-gradient(45deg, #007bff, #0056b3);
  border: none;
}

.btn-success {
  background: linear-gradient(45deg, #28a745, #1e7e34);
  border: none;
}

.btn-warning {
  background: linear-gradient(45deg, #ffc107, #e0a800);
  border: none;
}

.btn-danger {
  background: linear-gradient(45deg, #dc3545, #c82333);
  border: none;
}

/* Forms */
.form-control {
  border-radius: 10px;
  border: 2px solid #e9ecef;
  padding: 0.75rem 1rem;
  transition: all 0.3s ease;
}

.form-control:focus {
  border-color: var(--primary-color);
  box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
  transform: translateY(-2px);
}

.form-select {
  border-radius: 10px;
  border: 2px solid #e9ecef;
  transition: all 0.3s ease;
}

.form-select:focus {
  border-color: var(--primary-color);
  box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
}

/* Tables */
.table {
  border-radius: 10px;
  overflow: hidden;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
}

.table thead th {
  background: linear-gradient(45deg, #f8f9fa, #e9ecef);
  border: none;
  font-weight: 600;
  text-transform: uppercase;
  font-size: 0.85rem;
  letter-spacing: 0.5px;
}

.table tbody tr {
  transition: all 0.3s ease;
}

.table tbody tr:hover {
  background-color: rgba(0, 123, 255, 0.05);
  transform: scale(1.01);
}

/* Badges */
.badge {
  border-radius: 20px;
  padding: 0.5rem 1rem;
  font-weight: 500;
  transition: all 0.3s ease;
}

.badge:hover {
  transform: scale(1.1);
}

/* Alerts */
.alert {
  border-radius: 15px;
  border: none;
  padding: 1rem 1.5rem;
  animation: slideInUp 0.5s ease-out;
}

.alert-success {
  background: linear-gradient(45deg, #d4edda, #c3e6cb);
  color: #155724;
}

.alert-danger {
  background: linear-gradient(45deg, #f8d7da, #f5c6cb);
  color: #721c24;
}

.alert-warning {
  background: linear-gradient(45deg, #fff3cd, #ffeaa7);
  color: #856404;
}

.alert-info {
  background: linear-gradient(45deg, #d1ecf1, #bee5eb);
  color: #0c5460;
}

/* Modals */
.modal-content {
  border-radius: 20px;
  border: none;
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.2);
  animation: slideInUp 0.3s ease-out;
}

.modal-header {
  border-bottom: 1px solid #e9ecef;
  border-radius: 20px 20px 0 0;
  background: linear-gradient(45deg, #f8f9fa, #ffffff);
}

/* Dropdowns */
.dropdown-menu {
  border-radius: 15px;
  border: none;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.15);
  animation: slideInUp 0.3s ease-out;
}

.dropdown-item {
  transition: all 0.3s ease;
  border-radius: 10px;
  margin: 0.2rem 0.5rem;
}

.dropdown-item:hover {
  background-color: var(--primary-color);
  color: white;
  transform: translateX(5px);
}

/* User Avatar */
.user-avatar {
  transition: all 0.3s ease;
}

.user-avatar:hover {
  transform: scale(1.1);
}

/* Statistics Cards */
.stat-card {
  background: rgba(255, 255, 255, 0.05);
  backdrop-filter: blur(10px);
  border-radius: 20px;
  padding: 2rem;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  cursor: pointer;
  position: relative;
  overflow: hidden;
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.stat-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.1), transparent);
  transition: left 0.5s;
}

.stat-card:hover::before {
  left: 100%;
}

.stat-card:hover {
  transform: translateY(-5px);
  background: rgba(255, 255, 255, 0.1);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.2);
  border-color: rgba(255, 255, 255, 0.2);
}

.stat-card:active {
  transform: translateY(-2px);
  transition: transform 0.1s;
}

/* Stat card icon animation */
.stat-card:hover .fa-2x {
  transform: scale(1.1);
  transition: transform 0.3s ease;
}

/* Stat card number animation */
.stat-card:hover h4 {
  transform: scale(1.05);
  transition: transform 0.3s ease;
}

/* Accessibility improvements */
.stat-card:focus {
  outline: 2px solid var(--primary-color);
  outline-offset: 2px;
}

.stat-number {
  font-size: 3rem;
  font-weight: 700;
  background: linear-gradient(45deg, var(--warning-color, #ffc107), var(--primary-color, #007bff));
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.stat-label {
  font-size: 1rem;
  font-weight: 500;
  color: rgba(255, 255, 255, 0.8);
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
}

/* Feature Icons */
.feature-icon {
  width: 80px;
  height: 80px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 1rem;
  transition: all 0.3s ease;
}

.feature-icon:hover {
  transform: scale(1.1) rotate(5deg);
}

/* Job Images */
.job-image-container {
  height: 200px;
  overflow: hidden;
  border-radius: 15px 15px 0 0;
}

.job-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.5s ease;
}

.job-card:hover .job-image {
  transform: scale(1.1);
}

/* Loading Animations */
.loading {
  display: inline-block;
  width: 20px;
  height: 20px;
  border: 3px solid rgba(255, 255, 255, 0.3);
  border-radius: 50%;
  border-top-color: #fff;
  animation: spin 1s ease-in-out infinite;
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

/* Scroll Animations */
.scroll-animate {
  opacity: 0;
  transform: translateY(50px);
  transition: all 0.6s ease-out;
}

.scroll-animate.animate {
  opacity: 1;
  transform: translateY(0);
}

/* Progress Bars */
.progress {
  height: 10px;
  border-radius: 10px;
  background-color: #e9ecef;
  overflow: hidden;
}

.progress-bar {
  background: linear-gradient(45deg, var(--primary-color), var(--info-color));
  transition: width 0.6s ease;
}

/* Skill Match Indicator */
.skill-match {
  position: relative;
  display: inline-block;
}

.skill-match::after {
  content: attr(data-match) "%";
  position: absolute;
  top: -30px;
  left: 50%;
  transform: translateX(-50%);
  background: var(--success-color);
  color: white;
  padding: 0.25rem 0.5rem;
  border-radius: 15px;
  font-size: 0.75rem;
  font-weight: 600;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.skill-match:hover::after {
  opacity: 1;
}

/* Footer */
.footer {
  background: linear-gradient(135deg, #2c3e50, #34495e);
  color: white;
  padding: 3rem 0 1rem;
  margin-top: 4rem;
}

.footer a {
  color: #bdc3c7;
  transition: color 0.3s ease;
}

.footer a:hover {
  color: white;
}

/* Responsive Design */
@media (max-width: 768px) {
  .hero-section {
    min-height: 80vh;
    text-align: center;
  }

  .stat-number {
    font-size: 2rem;
  }

  .card {
    margin-bottom: 1rem;
  }

  .table-responsive {
    font-size: 0.875rem;
  }
}

@media (max-width: 576px) {
  .navbar-brand {
    font-size: 1.5rem !important;
  }

  .btn {
    padding: 0.375rem 1rem;
    font-size: 0.875rem;
  }

  .hero-section h1 {
    font-size: 2rem !important;
  }
}

/* Print Styles */
@media print {
  .navbar,
  .btn,
  .modal,
  .dropdown {
    display: none !important;
  }

  .card {
    box-shadow: none;
    border: 1px solid #dee2e6;
  }
}

/* High Contrast Mode */
@media (prefers-contrast: high) {
  .card {
    border: 2px solid #000;
  }

  .btn {
    border: 2px solid #000;
  }
}

/* Reduced Motion */
@media (prefers-reduced-motion: reduce) {
  *,
  *::before,
  *::after {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
}

/* Custom Scrollbar */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 10px;
}

::-webkit-scrollbar-thumb {
  background: linear-gradient(45deg, var(--primary-color), var(--info-color));
  border-radius: 10px;
}

::-webkit-scrollbar-thumb:hover {
  background: linear-gradient(45deg, var(--info-color), var(--primary-color));
}

/* Selection */
::selection {
  background-color: var(--primary-color);
  color: white;
}

::-moz-selection {
  background-color: var(--primary-color);
  color: white;
}

/* Override Bootstrap button styles for theme toggle */
#themeToggle.btn {
  border-radius: 50% !important;
  width: 44px !important;
  height: 44px !important;
  padding: 0 !important;
  min-width: 44px !important;
  min-height: 44px !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  cursor: pointer !important;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1) !important;
  position: relative !important;
  overflow: hidden !important;
}

#themeToggle.btn:hover {
  cursor: pointer !important;
  transform: scale(1.05) !important;
}

#themeToggle.btn:focus {
  cursor: pointer !important;
  box-shadow: 0 0 0 3px var(--focus-ring, rgba(0, 123, 255, 0.25)) !important;
}

#themeToggle.btn:active {
  cursor: pointer !important;
  transform: scale(0.95) !important;
}

/* Ensure the icon inside the button is properly styled */
#themeToggle i {
  font-size: 18px !important;
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1) !important;
  pointer-events: none !important;
}

#themeToggle:hover i {
  transform: rotate(15deg) !important;
}

/* Make sure the button is always clickable */
#themeToggle * {
  pointer-events: none !important;
}

#themeToggle {
  pointer-events: auto !important;
}

/* Enhanced visual feedback for theme toggle */
#themeToggle {
  position: relative;
  background: transparent;
  border: 2px solid var(--border-color, #dee2e6);
  border-radius: 50% !important;
  width: 44px;
  height: 44px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer !important;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  overflow: hidden;
  padding: 0 !important;
  min-width: 44px !important;
  min-height: 44px !important;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

#themeToggle:hover {
  border-color: var(--primary-color, #007bff) !important;
  background-color: var(--hover-bg, rgba(0, 123, 255, 0.1)) !important;
  transform: scale(1.05);
  cursor: pointer !important;
  box-shadow: 0 4px 16px rgba(0, 123, 255, 0.3);
}

#themeToggle:focus {
  outline: none;
  box-shadow: 0 0 0 3px var(--focus-ring, rgba(0, 123, 255, 0.25)) !important;
  cursor: pointer !important;
}

#themeToggle:active {
  transform: scale(0.95);
  cursor: pointer !important;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
}

/* Add a subtle pulse animation to draw attention */
@keyframes themeTogglePulse {
  0% { box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1); }
  50% { box-shadow: 0 4px 16px rgba(0, 123, 255, 0.2); }
  100% { box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1); }
}

#themeToggle {
  animation: themeTogglePulse 3s ease-in-out infinite;
}

#themeToggle:hover {
  animation: none;
}

/* Enhanced Dark Mode Styles for Job Portal */

/* Job Cards in Dark Mode */
[data-theme="dark"] .job-card {
  background: var(--bg-secondary);
  border: 1px solid var(--border-color);
  transition: all 0.3s ease;
}

[data-theme="dark"] .job-card:hover {
  background: var(--bg-tertiary);
  border-color: var(--primary-color);
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.3);
}

[data-theme="dark"] .job-card .card-title a {
  color: var(--primary-color) !important;
}

[data-theme="dark"] .job-card .card-title a:hover {
  color: var(--primary-hover) !important;
}

[data-theme="dark"] .job-card .company-info p {
  color: var(--text-primary);
}

[data-theme="dark"] .job-card .card-text {
  color: var(--text-secondary) !important;
}

[data-theme="dark"] .job-card .badge.bg-light {
  background-color: var(--bg-tertiary) !important;
  color: var(--text-primary) !important;
  border: 1px solid var(--border-color);
}

/* Search Form in Dark Mode */
[data-theme="dark"] .search-form {
  background: var(--bg-secondary);
  border: 1px solid var(--border-color);
}

[data-theme="dark"] .search-form .form-control,
[data-theme="dark"] .search-form .form-select {
  background: var(--bg-tertiary);
  border-color: var(--border-color);
  color: var(--text-primary);
}

[data-theme="dark"] .search-form .form-control:focus,
[data-theme="dark"] .search-form .form-select:focus {
  background: var(--bg-tertiary);
  border-color: var(--primary-color);
  color: var(--text-primary);
  box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
}

/* Pagination in Dark Mode */
[data-theme="dark"] .pagination .page-link {
  background: var(--bg-secondary);
  border-color: var(--border-color);
  color: var(--text-primary);
}

[data-theme="dark"] .pagination .page-link:hover {
  background: var(--bg-tertiary);
  border-color: var(--primary-color);
  color: var(--primary-color);
}

[data-theme="dark"] .pagination .page-item.active .page-link {
  background: var(--primary-color);
  border-color: var(--primary-color);
  color: white;
}

[data-theme="dark"] .pagination .page-item.disabled .page-link {
  background: var(--bg-secondary);
  border-color: var(--border-color);
  color: var(--text-muted);
}

/* Statistics Cards in Dark Mode */
[data-theme="dark"] .stat-card {
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(15px);
}

[data-theme="dark"] .stat-card::before {
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.08), transparent);
}

[data-theme="dark"] .stat-card:hover {
  background: rgba(255, 255, 255, 0.08);
  border-color: rgba(255, 255, 255, 0.2);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.4);
}

[data-theme="dark"] .stat-card:focus {
  outline-color: var(--primary-color);
}

[data-theme="dark"] .stat-number {
  background: linear-gradient(45deg, var(--warning-color), var(--primary-color));
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

[data-theme="dark"] .stat-label {
  color: rgba(255, 255, 255, 0.9);
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
}

/* Modal Improvements in Dark Mode */
[data-theme="dark"] .modal-content {
  background: var(--bg-secondary);
  border: 1px solid var(--border-color);
}

[data-theme="dark"] .modal-header {
  border-bottom: 1px solid var(--border-color);
  background: var(--bg-tertiary);
}

[data-theme="dark"] .modal-footer {
  border-top: 1px solid var(--border-color);
  background: var(--bg-tertiary);
}

[data-theme="dark"] .modal-title {
  color: var(--text-primary);
}

/* Form Improvements in Dark Mode */
[data-theme="dark"] .form-label {
  color: var(--text-primary);
  font-weight: 500;
}

[data-theme="dark"] .form-text {
  color: var(--text-muted);
}

[data-theme="dark"] .input-group-text {
  background: var(--bg-tertiary);
  border-color: var(--border-color);
  color: var(--text-secondary);
}

/* Table Improvements in Dark Mode */
[data-theme="dark"] .table {
  color: var(--text-primary);
}

[data-theme="dark"] .table thead th {
  background: var(--bg-tertiary);
  border-bottom: 2px solid var(--border-color);
  color: var(--text-primary);
  font-weight: 600;
}

[data-theme="dark"] .table tbody tr {
  border-bottom: 1px solid var(--border-color);
}

[data-theme="dark"] .table tbody tr:hover {
  background: var(--bg-tertiary);
}

/* Alert Improvements in Dark Mode */
[data-theme="dark"] .alert {
  border: 1px solid var(--border-color);
}

[data-theme="dark"] .alert-primary {
  background: rgba(0, 123, 255, 0.1);
  border-color: rgba(0, 123, 255, 0.3);
  color: var(--primary-color);
}

[data-theme="dark"] .alert-success {
  background: rgba(40, 167, 69, 0.1);
  border-color: rgba(40, 167, 69, 0.3);
  color: #28a745;
}

[data-theme="dark"] .alert-danger {
  background: rgba(220, 53, 69, 0.1);
  border-color: rgba(220, 53, 69, 0.3);
  color: #dc3545;
}

[data-theme="dark"] .alert-warning {
  background: rgba(255, 193, 7, 0.1);
  border-color: rgba(255, 193, 7, 0.3);
  color: #ffc107;
}

[data-theme="dark"] .alert-info {
  background: rgba(23, 162, 184, 0.1);
  border-color: rgba(23, 162, 184, 0.3);
  color: #17a2b8;
}

/* Hover Effects in Dark Mode */
[data-theme="dark"] .hover-lift:hover {
  transform: translateY(-4px);
  box-shadow: 0 12px 30px rgba(0, 0, 0, 0.4);
}

/* Smooth Theme Transitions */
.theme-transition * {
  transition: background-color 0.3s ease, color 0.3s ease, border-color 0.3s ease, box-shadow 0.3s ease;
}

/* Enhanced Theme Toggle Animation */
[data-theme="dark"] #themeToggle {
  animation: themeToggleGlow 2s ease-in-out infinite alternate;
}

@keyframes themeToggleGlow {
  from {
    box-shadow: 0 0 5px rgba(0, 123, 255, 0.3);
  }
  to {
    box-shadow: 0 0 15px rgba(0, 123, 255, 0.6);
  }
}

/* Dark Mode Specific Utilities */
[data-theme="dark"] .text-dark {
  color: var(--text-primary) !important;
}

[data-theme="dark"] .bg-light {
  background-color: var(--bg-secondary) !important;
}

[data-theme="dark"] .border-light {
  border-color: var(--border-color) !important;
}

/* Improved Focus States for Accessibility */
[data-theme="dark"] .btn:focus,
[data-theme="dark"] .form-control:focus,
[data-theme="dark"] .form-select:focus {
  box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
}

/* Loading States in Dark Mode */
[data-theme="dark"] .loading {
  background: var(--bg-secondary);
  color: var(--text-primary);
}

[data-theme="dark"] .loading::after {
  border-color: var(--border-color);
  border-top-color: var(--primary-color);
}

/* Hero Section Buttons */
.hero-section .btn {
  border-radius: 50px;
  font-weight: 600;
  padding: 12px 30px;
  font-size: 1.1rem;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;
  text-transform: none;
  letter-spacing: 0.5px;
}

.hero-section .btn::before {
  content: "";
  position: absolute;
  top: 50%;
  left: 50%;
  width: 0;
  height: 0;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 50%;
  transform: translate(-50%, -50%);
  transition: width 0.6s, height 0.6s;
}

.hero-section .btn:hover::before {
  width: 300px;
  height: 300px;
}

.hero-section .btn:hover {
  transform: translateY(-3px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.3);
}

.hero-section .btn-warning {
  background: linear-gradient(45deg, var(--warning-color, #ffc107), #ff8c00);
  border: none;
  color: #000;
  font-weight: 700;
}

.hero-section .btn-warning:hover {
  background: linear-gradient(45deg, #ff8c00, var(--warning-color, #ffc107));
  color: #000;
}

.hero-section .btn-outline-light {
  background: transparent;
  border: 2px solid rgba(255, 255, 255, 0.8);
  color: rgba(255, 255, 255, 0.9);
  backdrop-filter: blur(10px);
}

.hero-section .btn-outline-light:hover {
  background: rgba(255, 255, 255, 0.1);
  border-color: rgba(255, 255, 255, 1);
  color: #fff;
  backdrop-filter: blur(15px);
}

/* Dark theme hero buttons */
[data-theme="dark"] .hero-section .btn {
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
}

[data-theme="dark"] .hero-section .btn-warning {
  background: linear-gradient(45deg, var(--warning-color), #ff8c00);
  box-shadow: 0 4px 15px rgba(255, 193, 7, 0.3);
}

[data-theme="dark"] .hero-section .btn-warning:hover {
  box-shadow: 0 6px 20px rgba(255, 193, 7, 0.4);
}

[data-theme="dark"] .hero-section .btn-outline-light {
  border-color: rgba(255, 255, 255, 0.6);
  color: rgba(255, 255, 255, 0.9);
}

[data-theme="dark"] .hero-section .btn-outline-light:hover {
  border-color: rgba(255, 255, 255, 0.9);
  color: #fff;
  background: rgba(255, 255, 255, 0.1);
}

/* Hero Section Stat Cards in Dark Mode */
[data-theme="dark"] .hero-section .stat-card {
  background: rgba(255, 255, 255, 0.08);
  border: 1px solid rgba(255, 255, 255, 0.15);
  backdrop-filter: blur(15px);
}

[data-theme="dark"] .hero-section .stat-card::before {
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.1), transparent);
}

[data-theme="dark"] .hero-section .stat-card:hover {
  background: rgba(255, 255, 255, 0.12);
  border-color: rgba(255, 255, 255, 0.25);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.5);
}

[data-theme="dark"] .hero-section .stat-number {
  background: linear-gradient(45deg, #ffc107, #ff8c00);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

[data-theme="dark"] .hero-section .stat-label {
  color: rgba(255, 255, 255, 0.95);
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
  font-weight: 500;
}

/* Scroll to Top Button */
.scroll-to-top {
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  width: 50px !important;
  height: 50px !important;
  border-radius: 50% !important;
  position: fixed !important;
  bottom: 1rem !important;
  right: 1rem !important;
  z-index: 1000 !important;
  transition: all 0.3s ease !important;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15) !important;
}

.scroll-to-top:hover {
  transform: translateY(-2px) !important;
  box-shadow: 0 6px 16px rgba(0, 0, 0, 0.2) !important;
}

.scroll-to-top i {
  font-size: 18px !important;
  line-height: 1 !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
}

/* Back to Top Button (alternative implementation) */
.back-to-top {
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  width: 50px !important;
  height: 50px !important;
  border-radius: 50% !important;
  position: fixed !important;
  bottom: 1rem !important;
  right: 1rem !important;
  z-index: 1000 !important;
  transition: all 0.3s ease !important;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15) !important;
}

.back-to-top:hover {
  transform: translateY(-2px) !important;
  box-shadow: 0 6px 16px rgba(0, 0, 0, 0.2) !important;
}

.back-to-top i {
  font-size: 18px !important;
  line-height: 1 !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
}

/* Enhanced Jobs Page Animations */
.search-form {
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.95), rgba(255, 255, 255, 0.8)) !important;
  backdrop-filter: blur(20px) !important;
  border: 1px solid rgba(255, 255, 255, 0.2) !important;
  border-radius: 20px !important;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1) !important;
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1) !important;
  animation: slideInDown 0.8s ease-out !important;
}

.search-form:hover {
  transform: translateY(-5px) !important;
  box-shadow: 0 30px 60px rgba(0, 0, 0, 0.15) !important;
}

.search-form .card-title {
  background: linear-gradient(45deg, var(--primary-color), var(--info-color)) !important;
  -webkit-background-clip: text !important;
  -webkit-text-fill-color: transparent !important;
  background-clip: text !important;
  font-weight: 700 !important;
  font-size: 1.5rem !important;
  animation: fadeInUp 0.6s ease-out 0.2s both !important;
}

.search-form .form-control,
.search-form .form-select {
  border-radius: 12px !important;
  border: 2px solid rgba(0, 123, 255, 0.1) !important;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1) !important;
  background: rgba(255, 255, 255, 0.9) !important;
  backdrop-filter: blur(10px) !important;
}

.search-form .form-control:focus,
.search-form .form-select:focus {
  border-color: var(--primary-color) !important;
  box-shadow: 0 0 0 4px rgba(0, 123, 255, 0.1) !important;
  transform: translateY(-2px) !important;
  background: rgba(255, 255, 255, 1) !important;
}

.search-form .form-label {
  font-weight: 600 !important;
  color: var(--dark-color) !important;
  margin-bottom: 8px !important;
  animation: fadeInUp 0.6s ease-out 0.3s both !important;
}

.search-form .btn-primary {
  border-radius: 12px !important;
  background: linear-gradient(45deg, var(--primary-color), var(--info-color)) !important;
  border: none !important;
  font-weight: 600 !important;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1) !important;
  animation: fadeInUp 0.6s ease-out 0.4s both !important;
}

.search-form .btn-primary:hover {
  transform: translateY(-3px) scale(1.05) !important;
  box-shadow: 0 10px 25px rgba(0, 123, 255, 0.3) !important;
}

/* Enhanced Job Cards */
.job-card {
  border-radius: 20px !important;
  border: 1px solid rgba(0, 123, 255, 0.1) !important;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.95), rgba(255, 255, 255, 0.8)) !important;
  backdrop-filter: blur(20px) !important;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1) !important;
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1) !important;
  animation: fadeInUp 0.6s ease-out both !important;
  overflow: hidden !important;
  position: relative !important;
}

.job-card::before {
  content: '' !important;
  position: absolute !important;
  top: 0 !important;
  left: -100% !important;
  width: 100% !important;
  height: 100% !important;
  background: linear-gradient(90deg, transparent, rgba(0, 123, 255, 0.1), transparent) !important;
  transition: left 0.6s ease !important;
}

.job-card:hover::before {
  left: 100% !important;
}

.job-card:hover {
  transform: translateY(-10px) scale(1.02) !important;
  box-shadow: 0 20px 50px rgba(0, 0, 0, 0.15) !important;
  border-color: var(--primary-color) !important;
}

.job-card .card-title a {
  background: linear-gradient(45deg, var(--primary-color), var(--info-color)) !important;
  -webkit-background-clip: text !important;
  -webkit-text-fill-color: transparent !important;
  background-clip: text !important;
  font-weight: 700 !important;
  transition: all 0.3s ease !important;
}

.job-card:hover .card-title a {
  transform: scale(1.05) !important;
}

.job-card .badge {
  border-radius: 20px !important;
  font-weight: 600 !important;
  padding: 8px 16px !important;
  transition: all 0.3s ease !important;
}

.job-card:hover .badge {
  transform: scale(1.1) !important;
}

.job-card .btn {
  border-radius: 12px !important;
  font-weight: 600 !important;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1) !important;
}

.job-card .btn:hover {
  transform: translateY(-2px) !important;
  box-shadow: 0 8px 20px rgba(0, 0, 0, 0.2) !important;
}

/* Staggered Animation for Job Cards */
.job-card:nth-child(1) { animation-delay: 0.1s !important; }
.job-card:nth-child(2) { animation-delay: 0.2s !important; }
.job-card:nth-child(3) { animation-delay: 0.3s !important; }
.job-card:nth-child(4) { animation-delay: 0.4s !important; }
.job-card:nth-child(5) { animation-delay: 0.5s !important; }
.job-card:nth-child(6) { animation-delay: 0.6s !important; }

/* Enhanced Pagination */
.pagination .page-link {
  border-radius: 12px !important;
  margin: 0 4px !important;
  border: 2px solid rgba(0, 123, 255, 0.1) !important;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1) !important;
  font-weight: 600 !important;
}

.pagination .page-link:hover {
  transform: translateY(-2px) !important;
  box-shadow: 0 5px 15px rgba(0, 123, 255, 0.2) !important;
  border-color: var(--primary-color) !important;
}

.pagination .page-item.active .page-link {
  background: linear-gradient(45deg, var(--primary-color), var(--info-color)) !important;
  border-color: var(--primary-color) !important;
  transform: scale(1.1) !important;
}

/* Dark Theme Enhancements */
[data-theme="dark"] .search-form {
  background: linear-gradient(135deg, rgba(22, 27, 34, 0.95), rgba(22, 27, 34, 0.8)) !important;
  border: 1px solid rgba(88, 166, 255, 0.2) !important;
}

[data-theme="dark"] .search-form .form-control,
[data-theme="dark"] .search-form .form-select {
  background: rgba(33, 38, 45, 0.9) !important;
  border-color: rgba(88, 166, 255, 0.2) !important;
  color: var(--text-primary) !important;
}

[data-theme="dark"] .search-form .form-control:focus,
[data-theme="dark"] .search-form .form-select:focus {
  background: rgba(33, 38, 45, 1) !important;
  border-color: var(--primary-color) !important;
}

[data-theme="dark"] .job-card {
  background: linear-gradient(135deg, rgba(22, 27, 34, 0.95), rgba(22, 27, 34, 0.8)) !important;
  border-color: rgba(88, 166, 255, 0.2) !important;
}

[data-theme="dark"] .job-card::before {
  background: linear-gradient(90deg, transparent, rgba(88, 166, 255, 0.1), transparent) !important;
}

[data-theme="dark"] .job-card:hover {
  border-color: var(--primary-color) !important;
  box-shadow: 0 20px 50px rgba(0, 0, 0, 0.4) !important;
}

/* Loading Animation for Search Results */
@keyframes shimmer {
  0% { background-position: -200px 0; }
  100% { background-position: calc(200px + 100%) 0; }
}

.loading-skeleton {
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%) !important;
  background-size: 200px 100% !important;
  animation: shimmer 1.5s infinite !important;
  border-radius: 8px !important;
}

[data-theme="dark"] .loading-skeleton {
  background: linear-gradient(90deg, #2a2a2a 25%, #3a3a3a 50%, #2a2a2a 75%) !important;
}

/* Enhanced Animations */
@keyframes slideInDown {
  from {
    opacity: 0;
    transform: translateY(-30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes pulse {
  0%, 100% { transform: scale(1); }
  50% { transform: scale(1.05); }
}

/* Floating Animation for Search Button */
.search-form .btn-primary {
  animation: pulse 2s infinite !important;
}

.search-form .btn-primary:hover {
  animation: none !important;
}
