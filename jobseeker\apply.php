<?php
require_once __DIR__ . '/../config/config.php';

// Check if user is logged in and is jobseeker
if (!isLoggedIn() || getUserType() !== 'jobseeker') {
    redirect('../auth/login.php');
}

$job_id = (int)($_GET['job_id'] ?? 0);
$error = '';
$success = '';
$job = null;

if ($job_id <= 0) {
    redirect('../jobs.php');
}

try {
    $database = new Database();
    $db = $database->getConnection();
    $user_id = $_SESSION['user_id'];

    // Get job details
    $query = "SELECT jp.*, bp.company_name 
              FROM job_posts jp 
              JOIN business_profiles bp ON jp.business_id = bp.user_id 
              WHERE jp.id = ? AND jp.status = 'approved'";
    $stmt = $db->prepare($query);
    $stmt->execute([$job_id]);
    $job = $stmt->fetch(PDO::FETCH_ASSOC);

    if (!$job) {
        redirect('../jobs.php');
    }

    // Check if already applied
    $query = "SELECT id FROM job_applications WHERE job_id = ? AND jobseeker_id = ?";
    $stmt = $db->prepare($query);
    $stmt->execute([$job_id, $user_id]);
    if ($stmt->fetch()) {
        redirect("../job-details.php?id=$job_id&error=already_applied");
    }

    // Get jobseeker profile for skill matching
    $query = "SELECT * FROM jobseeker_profiles WHERE user_id = ?";
    $stmt = $db->prepare($query);
    $stmt->execute([$user_id]);
    $profile = $stmt->fetch(PDO::FETCH_ASSOC);

    // Calculate skill match
    $skill_match = 0;
    if (!empty($job['skills_required']) && !empty($profile['skills'])) {
        $skill_match = calculateSkillMatch($job['skills_required'], $profile['skills']);
    }

    // Handle form submission
    if ($_SERVER['REQUEST_METHOD'] === 'POST') {
        $cover_letter = sanitize($_POST['cover_letter'] ?? '');
        $inquiry_message = sanitize($_POST['inquiry_message'] ?? '');

        if (empty($cover_letter)) {
            $error = 'Please write a cover letter.';
        } else {
            try {
                $query = "INSERT INTO job_applications (job_id, jobseeker_id, cover_letter, inquiry_message, skill_match_percentage, status) 
                          VALUES (?, ?, ?, ?, ?, 'pending')";
                $stmt = $db->prepare($query);
                $stmt->execute([$job_id, $user_id, $cover_letter, $inquiry_message, $skill_match]);

                $success = 'Your application has been submitted successfully!';
                header("Location: applications.php?success=" . urlencode($success));
                exit();
            } catch (Exception $e) {
                $error = 'Failed to submit application. Please try again.';
                error_log("Application submission error: " . $e->getMessage());
            }
        }
    }

} catch (Exception $e) {
    error_log("Apply page error: " . $e->getMessage());
    $error = 'An error occurred while loading the application form.';
}

$page_title = "Apply for Job";
include __DIR__ . '/../includes/header.php';
?>

<main class="py-4">
    <div class="container">
        <div class="row">
            <div class="col-lg-8 mx-auto">
                <div class="card shadow">
                    <div class="card-header">
                        <h4 class="mb-0">
                            <i class="fas fa-paper-plane me-2"></i>Apply for Job
                        </h4>
                    </div>
                    <div class="card-body">
                        <?php if ($error): ?>
                        <div class="alert alert-danger alert-dismissible fade show" role="alert">
                            <?php echo htmlspecialchars($error); ?>
                            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                        </div>
                        <?php endif; ?>

                        <!-- Job Information -->
                        <div class="card mb-4">
                            <div class="card-body">
                                <h5 class="card-title"><?php echo htmlspecialchars($job['title']); ?></h5>
                                <p class="text-primary mb-2">
                                    <i class="fas fa-building me-1"></i>
                                    <?php echo htmlspecialchars($job['company_name']); ?>
                                </p>
                                <div class="row">
                                    <div class="col-md-6">
                                        <small class="text-muted">Location:</small>
                                        <span><?php echo htmlspecialchars($job['location']); ?></span>
                                    </div>
                                    <div class="col-md-6">
                                        <small class="text-muted">Job Type:</small>
                                        <span class="badge bg-info"><?php echo ucfirst($job['job_type']); ?></span>
                                    </div>
                                </div>
                                <?php if ($job['salary_min'] && $job['salary_max']): ?>
                                <div class="mt-2">
                                    <small class="text-muted">Salary:</small>
                                    <span class="text-success fw-bold">
                                        ₱<?php echo number_format($job['salary_min']); ?> - ₱<?php echo number_format($job['salary_max']); ?>
                                    </span>
                                </div>
                                <?php endif; ?>
                            </div>
                        </div>

                        <!-- Skill Match Display -->
                        <?php if ($skill_match > 0): ?>
                        <div class="alert alert-success mb-4">
                            <div class="d-flex align-items-center">
                                <i class="fas fa-star fa-2x me-3"></i>
                                <div>
                                    <h6 class="mb-1">Skill Match: <?php echo $skill_match; ?>%</h6>
                                    <p class="mb-0">Your skills are a great match for this position!</p>
                                </div>
                            </div>
                        </div>
                        <?php endif; ?>

                        <!-- Application Form -->
                        <form method="POST">
                            <div class="mb-4">
                                <label for="cover_letter" class="form-label">Cover Letter <span class="text-danger">*</span></label>
                                <textarea class="form-control" id="cover_letter" name="cover_letter" rows="8" required
                                          placeholder="Write a compelling cover letter explaining why you're the perfect fit for this position..."><?php echo htmlspecialchars($_POST['cover_letter'] ?? ''); ?></textarea>
                                <small class="form-text text-muted">
                                    Tip: Mention your relevant experience, skills, and why you want to work for this company.
                                </small>
                            </div>

                            <div class="mb-4">
                                <label for="inquiry_message" class="form-label">Additional Questions/Comments (Optional)</label>
                                <textarea class="form-control" id="inquiry_message" name="inquiry_message" rows="4"
                                          placeholder="Any questions about the role, company culture, benefits, or work environment?"><?php echo htmlspecialchars($_POST['inquiry_message'] ?? ''); ?></textarea>
                                <small class="form-text text-muted">
                                    Use this space to ask questions or provide additional information.
                                </small>
                            </div>

                            <!-- Profile Check -->
                            <?php if (empty($profile['resume_file'])): ?>
                            <div class="alert alert-warning">
                                <i class="fas fa-exclamation-triangle me-2"></i>
                                <strong>Recommendation:</strong> Consider uploading your resume to your profile to strengthen your application.
                                <a href="profile.php" class="btn btn-sm btn-outline-warning ms-2">Update Profile</a>
                            </div>
                            <?php endif; ?>

                            <div class="d-flex justify-content-between">
                                <a href="../job-details.php?id=<?php echo $job_id; ?>" class="btn btn-secondary">
                                    <i class="fas fa-arrow-left me-1"></i>Back to Job Details
                                </a>
                                <button type="submit" class="btn btn-primary btn-lg">
                                    <i class="fas fa-paper-plane me-1"></i>Submit Application
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>
</main>

<?php include __DIR__ . '/../includes/footer.php'; ?>
