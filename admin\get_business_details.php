<?php
require_once __DIR__ . '/../config/config.php';

// Check if user is logged in and is admin
if (!isLoggedIn() || getUserType() !== 'admin') {
    http_response_code(403);
    echo json_encode(['success' => false, 'message' => 'Unauthorized access']);
    exit;
}

// Check if user_id is provided
if (!isset($_GET['user_id']) || empty($_GET['user_id'])) {
    http_response_code(400);
    echo json_encode(['success' => false, 'message' => 'User ID is required']);
    exit;
}

$user_id = (int)$_GET['user_id'];

try {
    $database = new Database();
    $db = $database->getConnection();
    
    // Get business details
    $query = "SELECT u.*, bp.company_name, bp.business_description, bp.mayors_permit, bp.dti_number 
              FROM users u 
              LEFT JOIN business_profiles bp ON u.id = bp.user_id 
              WHERE u.id = ? AND u.user_type = 'business'";
    
    $stmt = $db->prepare($query);
    $stmt->execute([$user_id]);
    $business = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if ($business) {
        // Sanitize the data for JSON output
        $business['company_name'] = htmlspecialchars($business['company_name'] ?? '');
        $business['business_description'] = htmlspecialchars($business['business_description'] ?? '');
        $business['first_name'] = htmlspecialchars($business['first_name'] ?? '');
        $business['last_name'] = htmlspecialchars($business['last_name'] ?? '');
        $business['email'] = htmlspecialchars($business['email'] ?? '');
        $business['phone'] = htmlspecialchars($business['phone'] ?? '');
        $business['dti_number'] = htmlspecialchars($business['dti_number'] ?? '');
        $business['mayors_permit'] = htmlspecialchars($business['mayors_permit'] ?? '');
        
        echo json_encode([
            'success' => true,
            'business' => $business
        ]);
    } else {
        echo json_encode([
            'success' => false,
            'message' => 'Business not found'
        ]);
    }
    
} catch (Exception $e) {
    error_log("Business details error: " . $e->getMessage());
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'message' => 'Database error occurred'
    ]);
}
?> 