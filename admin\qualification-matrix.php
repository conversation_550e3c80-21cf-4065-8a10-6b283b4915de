<?php
require_once __DIR__ . '/../config/config.php';

// Check if user is logged in and is admin
if (!isLoggedIn() || getUserType() !== 'admin') {
    redirect('/auth/login.php');
}

$success = '';
$error = '';

try {
    $database = new Database();
    $db = $database->getConnection();
    
    // Handle form submission for updating qualification matrix
    if ($_SERVER['REQUEST_METHOD'] === 'POST') {
        if (isset($_POST['action']) && $_POST['action'] === 'update_matrix') {
            $course_category = sanitize($_POST['course_category']);
            $job_category = sanitize($_POST['job_category']);
            $percentage = (int)$_POST['percentage'];
            $reasoning = sanitize($_POST['reasoning']);
            
            if ($percentage >= 0 && $percentage <= 100) {
                $query = "INSERT INTO qualification_matrix (course_category, job_category, qualification_percentage, reasoning) 
                          VALUES (?, ?, ?, ?) 
                          ON DUPLICATE KEY UPDATE 
                          qualification_percentage = VALUES(qualification_percentage),
                          reasoning = VALUES(reasoning),
                          updated_at = CURRENT_TIMESTAMP";
                $stmt = $db->prepare($query);
                $stmt->execute([$course_category, $job_category, $percentage, $reasoning]);
                
                $success = "Qualification matrix updated successfully!";
            } else {
                $error = "Percentage must be between 0 and 100.";
            }
        }
    }
    
    // Get course categories
    $query = "SELECT * FROM course_categories ORDER BY category_name";
    $stmt = $db->prepare($query);
    $stmt->execute();
    $course_categories = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    // Get job categories
    $query = "SELECT * FROM job_categories ORDER BY category_name";
    $stmt = $db->prepare($query);
    $stmt->execute();
    $job_categories = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    // Get current qualification matrix
    $query = "SELECT qm.*, cc.description as course_description, jc.description as job_description
              FROM qualification_matrix qm
              JOIN course_categories cc ON qm.course_category = cc.category_name
              JOIN job_categories jc ON qm.job_category = jc.category_name
              ORDER BY qm.course_category, qm.job_category";
    $stmt = $db->prepare($query);
    $stmt->execute();
    $matrix = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
} catch (Exception $e) {
    error_log("Qualification matrix error: " . $e->getMessage());
    $error = "Database error occurred.";
    $course_categories = [];
    $job_categories = [];
    $matrix = [];
}

$page_title = "Qualification Matrix Management";
include __DIR__ . '/../includes/header.php';
?>

<main class="py-4">
    <div class="container-fluid">
        <!-- Success/Error Messages -->
        <?php if ($success): ?>
        <div class="alert alert-success alert-dismissible fade show" role="alert">
            <i class="fas fa-check-circle me-2"></i>
            <?php echo htmlspecialchars($success); ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
        <?php endif; ?>

        <?php if ($error): ?>
        <div class="alert alert-danger alert-dismissible fade show" role="alert">
            <i class="fas fa-exclamation-circle me-2"></i>
            <?php echo htmlspecialchars($error); ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
        <?php endif; ?>

        <!-- Page Header -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="d-flex justify-content-between align-items-center">
                    <h2><i class="fas fa-chart-line me-2"></i>Qualification Matrix Management</h2>
                    <a href="dashboard.php" class="btn btn-outline-secondary">
                        <i class="fas fa-arrow-left me-1"></i>Back to Dashboard
                    </a>
                </div>
                <p class="text-muted">Manage course-to-job qualification percentages for automatic job matching.</p>
            </div>
        </div>

        <!-- Add/Edit Matrix Entry -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="card shadow-sm">
                    <div class="card-header">
                        <h5 class="mb-0"><i class="fas fa-plus me-2"></i>Add/Edit Qualification Rule</h5>
                    </div>
                    <div class="card-body">
                        <form method="POST">
                            <input type="hidden" name="action" value="update_matrix">
                            <div class="row">
                                <div class="col-md-3">
                                    <label for="course_category" class="form-label">Course Category</label>
                                    <select class="form-select" id="course_category" name="course_category" required>
                                        <option value="">Select Course Category</option>
                                        <?php foreach ($course_categories as $category): ?>
                                        <option value="<?php echo htmlspecialchars($category['category_name']); ?>">
                                            <?php echo htmlspecialchars($category['category_name']); ?>
                                        </option>
                                        <?php endforeach; ?>
                                    </select>
                                </div>
                                <div class="col-md-3">
                                    <label for="job_category" class="form-label">Job Category</label>
                                    <select class="form-select" id="job_category" name="job_category" required>
                                        <option value="">Select Job Category</option>
                                        <?php foreach ($job_categories as $category): ?>
                                        <option value="<?php echo htmlspecialchars($category['category_name']); ?>">
                                            <?php echo htmlspecialchars($category['category_name']); ?>
                                        </option>
                                        <?php endforeach; ?>
                                    </select>
                                </div>
                                <div class="col-md-2">
                                    <label for="percentage" class="form-label">Qualification %</label>
                                    <input type="number" class="form-control" id="percentage" name="percentage" 
                                           min="0" max="100" required placeholder="0-100">
                                </div>
                                <div class="col-md-3">
                                    <label for="reasoning" class="form-label">Reasoning</label>
                                    <input type="text" class="form-control" id="reasoning" name="reasoning" 
                                           placeholder="Brief explanation">
                                </div>
                                <div class="col-md-1">
                                    <label class="form-label">&nbsp;</label>
                                    <button type="submit" class="btn btn-primary w-100">
                                        <i class="fas fa-save"></i>
                                    </button>
                                </div>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>

        <!-- Qualification Matrix Table -->
        <div class="row">
            <div class="col-12">
                <div class="card shadow-sm">
                    <div class="card-header">
                        <h5 class="mb-0"><i class="fas fa-table me-2"></i>Current Qualification Matrix</h5>
                    </div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table table-striped table-hover">
                                <thead class="table-dark">
                                    <tr>
                                        <th>Course Category</th>
                                        <th>Job Category</th>
                                        <th>Qualification %</th>
                                        <th>Reasoning</th>
                                        <th>Last Updated</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($matrix as $entry): ?>
                                    <tr>
                                        <td>
                                            <strong><?php echo htmlspecialchars($entry['course_category']); ?></strong>
                                            <br>
                                            <small class="text-muted"><?php echo htmlspecialchars($entry['course_description']); ?></small>
                                        </td>
                                        <td>
                                            <strong><?php echo htmlspecialchars($entry['job_category']); ?></strong>
                                            <br>
                                            <small class="text-muted"><?php echo htmlspecialchars($entry['job_description']); ?></small>
                                        </td>
                                        <td>
                                            <?php 
                                            $percentage = $entry['qualification_percentage'];
                                            $color = '';
                                            if ($percentage >= 80) $color = 'success';
                                            elseif ($percentage >= 60) $color = 'warning';
                                            elseif ($percentage >= 40) $color = 'info';
                                            else $color = 'danger';
                                            ?>
                                            <span class="badge bg-<?php echo $color; ?> fs-6">
                                                <?php echo $percentage; ?>%
                                            </span>
                                        </td>
                                        <td>
                                            <small><?php echo htmlspecialchars($entry['reasoning']); ?></small>
                                        </td>
                                        <td>
                                            <small class="text-muted">
                                                <?php echo date('M j, Y', strtotime($entry['updated_at'])); ?>
                                            </small>
                                        </td>
                                    </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Statistics -->
        <div class="row mt-4">
            <div class="col-md-3">
                <div class="card bg-primary text-white">
                    <div class="card-body">
                        <div class="d-flex justify-content-between">
                            <div>
                                <h4 class="mb-0"><?php echo count($matrix); ?></h4>
                                <small>Total Rules</small>
                            </div>
                            <div class="align-self-center">
                                <i class="fas fa-chart-line fa-2x"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card bg-success text-white">
                    <div class="card-body">
                        <div class="d-flex justify-content-between">
                            <div>
                                <h4 class="mb-0"><?php echo count($course_categories); ?></h4>
                                <small>Course Categories</small>
                            </div>
                            <div class="align-self-center">
                                <i class="fas fa-graduation-cap fa-2x"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card bg-info text-white">
                    <div class="card-body">
                        <div class="d-flex justify-content-between">
                            <div>
                                <h4 class="mb-0"><?php echo count($job_categories); ?></h4>
                                <small>Job Categories</small>
                            </div>
                            <div class="align-self-center">
                                <i class="fas fa-briefcase fa-2x"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card bg-warning text-white">
                    <div class="card-body">
                        <div class="d-flex justify-content-between">
                            <div>
                                <h4 class="mb-0">
                                    <?php 
                                    $high_matches = array_filter($matrix, function($entry) {
                                        return $entry['qualification_percentage'] >= 80;
                                    });
                                    echo count($high_matches);
                                    ?>
                                </h4>
                                <small>High Matches (80%+)</small>
                            </div>
                            <div class="align-self-center">
                                <i class="fas fa-star fa-2x"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</main>

<script>
// Auto-fill form when clicking on table rows
document.addEventListener('DOMContentLoaded', function() {
    const tableRows = document.querySelectorAll('tbody tr');
    const courseSelect = document.getElementById('course_category');
    const jobSelect = document.getElementById('job_category');
    const percentageInput = document.getElementById('percentage');
    const reasoningInput = document.getElementById('reasoning');
    
    tableRows.forEach(row => {
        row.addEventListener('click', function() {
            const cells = this.cells;
            const courseCategory = cells[0].querySelector('strong').textContent;
            const jobCategory = cells[1].querySelector('strong').textContent;
            const percentage = cells[2].querySelector('.badge').textContent.replace('%', '');
            const reasoning = cells[3].querySelector('small').textContent;
            
            courseSelect.value = courseCategory;
            jobSelect.value = jobCategory;
            percentageInput.value = percentage;
            reasoningInput.value = reasoning;
            
            // Scroll to form
            document.querySelector('.card-header').scrollIntoView({ behavior: 'smooth' });
        });
    });
});
</script>

<?php include __DIR__ . '/../includes/footer.php'; ?> 