<?php
require_once __DIR__ . '/../config/config.php';

// Check if user is logged in and is jobseeker
if (!isLoggedIn() || getUserType() !== 'jobseeker') {
    redirect('/auth/login.php'); // Fixed redirect path
}

try {
    $database = new Database();
    $db = $database->getConnection();
    $user_id = $_SESSION['user_id'];

    // Initialize variables
    $applications = [];
    $stats = ['total' => 0, 'pending' => 0, 'reviewed' => 0, 'accepted' => 0, 'rejected' => 0];

    // Get filter parameters
    $status_filter = sanitize($_GET['status'] ?? '');
    $search = sanitize($_GET['search'] ?? '');

    // Build query
    try {
        $query = "SELECT ja.*, jp.title, jp.location, jp.job_type, jp.salary_min, jp.salary_max, 
                         bp.company_name, bp.business_address, u.first_name as business_first_name, u.last_name as business_last_name
                  FROM job_applications ja
                  JOIN job_posts jp ON ja.job_id = jp.id
                  JOIN business_profiles bp ON jp.business_id = bp.user_id
                  JOIN users u ON jp.business_id = u.id
                  WHERE ja.user_id = ?"; // Fixed column name from jobseeker_id to user_id
        
        $params = [$user_id];
        
        if (!empty($status_filter)) {
            $query .= " AND ja.status = ?";
            $params[] = $status_filter;
        }
        
        if (!empty($search)) {
            $query .= " AND (jp.title LIKE ? OR bp.company_name LIKE ?)";
            $searchTerm = "%$search%";
            $params[] = $searchTerm;
            $params[] = $searchTerm;
        }
        
        $query .= " ORDER BY ja.created_at DESC"; // Fixed column name from applied_at to created_at
        
        $stmt = $db->prepare($query);
        $stmt->execute($params);
        $applications = $stmt->fetchAll(PDO::FETCH_ASSOC);
    } catch (Exception $e) {
        error_log("Applications query error: " . $e->getMessage());
        $applications = [];
    }

    // Get application statistics
    try {
        $query = "SELECT 
                    COUNT(*) as total,
                    SUM(CASE WHEN status = 'pending' THEN 1 ELSE 0 END) as pending,
                    SUM(CASE WHEN status = 'accepted' THEN 1 ELSE 0 END) as accepted,
                    SUM(CASE WHEN status = 'rejected' THEN 1 ELSE 0 END) as rejected
                  FROM job_applications 
                  WHERE user_id = ?"; // Fixed column name from jobseeker_id to user_id
        $stmt = $db->prepare($query);
        $stmt->execute([$user_id]);
        $stats = $stmt->fetch(PDO::FETCH_ASSOC);
        
        // Ensure all values are integers
        $stats = array_map('intval', $stats);
    } catch (Exception $e) {
        error_log("Statistics query error: " . $e->getMessage());
        $stats = ['total' => 0, 'pending' => 0, 'accepted' => 0, 'rejected' => 0];
    }

} catch (Exception $e) {
    error_log("Applications page error: " . $e->getMessage());
    $applications = [];
    $stats = ['total' => 0, 'pending' => 0, 'accepted' => 0, 'rejected' => 0];
}

$page_title = "My Applications";
include __DIR__ . '/../includes/header.php';
?>

<main class="py-4">
    <div class="container-fluid">
        <!-- Success/Error Messages -->
        <?php if (isset($_SESSION['success'])): ?>
        <div class="alert alert-success alert-dismissible fade show" role="alert">
            <i class="fas fa-check-circle me-2"></i>
            <?php echo htmlspecialchars($_SESSION['success']); unset($_SESSION['success']); ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
        <?php endif; ?>

        <?php if (isset($_SESSION['error'])): ?>
        <div class="alert alert-danger alert-dismissible fade show" role="alert">
            <i class="fas fa-exclamation-circle me-2"></i>
            <?php echo htmlspecialchars($_SESSION['error']); unset($_SESSION['error']); ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
        <?php endif; ?>

        <!-- Header -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="d-flex justify-content-between align-items-center">
                    <h1 class="h3 mb-0">My Job Applications</h1>
                    <div class="d-flex gap-2">
                        <a href="../jobs.php" class="btn btn-success">
                            <i class="fas fa-search me-1"></i>Browse Jobs
                        </a>
                        <a href="dashboard.php" class="btn btn-outline-primary">
                            <i class="fas fa-arrow-left me-1"></i>Back to Dashboard
                        </a>
                    </div>
                </div>
            </div>
        </div>

        <!-- Statistics Cards -->
        <div class="row mb-4">
            <div class="col-lg-3 col-md-6 mb-3">
                <div class="card bg-primary text-white h-100 stat-card">
                    <div class="card-body">
                        <div class="d-flex justify-content-between">
                            <div>
                                <h4 class="mb-0"><?php echo number_format($stats['total']); ?></h4>
                                <p class="mb-0">Total Applications</p>
                            </div>
                            <div class="align-self-center">
                                <i class="fas fa-paper-plane fa-2x opacity-75"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-lg-3 col-md-6 mb-3">
                <div class="card bg-warning text-white h-100 stat-card">
                    <div class="card-body">
                        <div class="d-flex justify-content-between">
                            <div>
                                <h4 class="mb-0"><?php echo number_format($stats['pending']); ?></h4>
                                <p class="mb-0">Pending Review</p>
                            </div>
                            <div class="align-self-center">
                                <i class="fas fa-clock fa-2x opacity-75"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-lg-3 col-md-6 mb-3">
                <div class="card bg-success text-white h-100 stat-card">
                    <div class="card-body">
                        <div class="d-flex justify-content-between">
                            <div>
                                <h4 class="mb-0"><?php echo number_format($stats['accepted']); ?></h4>
                                <p class="mb-0">Accepted</p>
                            </div>
                            <div class="align-self-center">
                                <i class="fas fa-check-circle fa-2x opacity-75"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-lg-3 col-md-6 mb-3">
                <div class="card bg-danger text-white h-100 stat-card">
                    <div class="card-body">
                        <div class="d-flex justify-content-between">
                            <div>
                                <h4 class="mb-0"><?php echo number_format($stats['rejected']); ?></h4>
                                <p class="mb-0">Rejected</p>
                            </div>
                            <div class="align-self-center">
                                <i class="fas fa-times-circle fa-2x opacity-75"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Filters -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-body">
                        <form method="GET" class="row g-3">
                            <div class="col-md-4">
                                <label for="search" class="form-label">Search</label>
                                <input type="text" class="form-control" id="search" name="search" 
                                       placeholder="Job title or company" value="<?php echo htmlspecialchars($search); ?>">
                            </div>
                            <div class="col-md-3">
                                <label for="status" class="form-label">Status</label>
                                <select class="form-select" id="status" name="status">
                                    <option value="">All Status</option>
                                    <option value="pending" <?php echo $status_filter === 'pending' ? 'selected' : ''; ?>>Pending</option>
                                    <option value="reviewed" <?php echo $status_filter === 'reviewed' ? 'selected' : ''; ?>>Under Review</option>
                                    <option value="accepted" <?php echo $status_filter === 'accepted' ? 'selected' : ''; ?>>Accepted</option>
                                    <option value="rejected" <?php echo $status_filter === 'rejected' ? 'selected' : ''; ?>>Rejected</option>
                                </select>
                            </div>
                            <div class="col-md-3 d-flex align-items-end">
                                <button type="submit" class="btn btn-primary me-2">
                                    <i class="fas fa-search"></i> Filter
                                </button>
                                <a href="applications.php" class="btn btn-outline-secondary">Clear</a>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>

        <!-- Applications List -->
        <div class="row">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">Applications (<?php echo count($applications); ?>)</h5>
                    </div>
                    <div class="card-body">
                        <?php if (empty($applications)): ?>
                            <div class="text-center py-5">
                                <i class="fas fa-inbox fa-3x text-muted mb-3"></i>
                                <h5>No Applications Found</h5>
                                <p class="text-muted">
                                    <?php if (!empty($status_filter) || !empty($search)): ?>
                                        Try adjusting your filters or <a href="applications.php">view all applications</a>.
                                    <?php else: ?>
                                        You haven't applied to any jobs yet. <a href="../jobs.php">Browse available jobs</a> to get started.
                                    <?php endif; ?>
                                </p>
                            </div>
                        <?php else: ?>
                            <div class="table-responsive">
                                <table class="table table-hover">
                                    <thead>
                                        <tr>
                                            <th>Job Details</th>
                                            <th>Company</th>
                                            <th>Salary</th>
                                            <th>Status</th>
                                            <th>Applied Date</th>
                                            <th>Actions</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <?php foreach ($applications as $app): ?>
                                        <tr>
                                            <td>
                                                <div>
                                                    <strong><?php echo htmlspecialchars($app['title']); ?></strong>
                                                    <br>
                                                    <small class="text-muted">
                                                        <i class="fas fa-map-marker-alt me-1"></i><?php echo htmlspecialchars($app['location']); ?>
                                                        <span class="mx-2">•</span>
                                                        <span class="badge bg-info"><?php echo ucfirst($app['job_type']); ?></span>
                                                    </small>
                                                </div>
                                            </td>
                                            <td>
                                                <strong><?php echo htmlspecialchars($app['company_name']); ?></strong>
                                                <br>
                                                <small class="text-muted">
                                                    <?php echo htmlspecialchars($app['business_first_name'] . ' ' . $app['business_last_name']); ?>
                                                </small>
                                            </td>
                                            <td>
                                                <?php if ($app['salary_min'] && $app['salary_max']): ?>
                                                    <span class="text-success">
                                                        ₱<?php echo number_format($app['salary_min']); ?> - ₱<?php echo number_format($app['salary_max']); ?>
                                                    </span>
                                                <?php else: ?>
                                                    <span class="text-muted">Not specified</span>
                                                <?php endif; ?>
                                            </td>
                                            <td>
                                                <?php
                                                $status_colors = [
                                                    'pending' => 'warning',
                                                    'reviewed' => 'info',
                                                    'accepted' => 'success',
                                                    'rejected' => 'danger'
                                                ];
                                                $status_icons = [
                                                    'pending' => 'clock',
                                                    'reviewed' => 'eye',
                                                    'accepted' => 'check',
                                                    'rejected' => 'times'
                                                ];
                                                ?>
                                                <span class="badge bg-<?php echo $status_colors[$app['status']] ?? 'secondary'; ?>">
                                                    <i class="fas fa-<?php echo $status_icons[$app['status']] ?? 'question'; ?> me-1"></i>
                                                    <?php echo ucfirst($app['status']); ?>
                                                </span>
                                                <?php if (!empty($app['skill_match_percentage'])): ?>
                                                <br>
                                                <small class="text-muted">
                                                    <?php echo $app['skill_match_percentage']; ?>% skill match
                                                </small>
                                                <?php endif; ?>
                                            </td>
                                            <td>
                                                <?php echo date('M j, Y', strtotime($app['applied_at'])); ?>
                                                <br>
                                                <small class="text-muted"><?php echo date('g:i A', strtotime($app['applied_at'])); ?></small>
                                            </td>
                                            <td>
                                                <div class="btn-group btn-group-sm">
                                                    <a href="../job-details.php?id=<?php echo $app['job_id']; ?>" 
                                                       class="btn btn-outline-primary" target="_blank">
                                                        <i class="fas fa-eye"></i> View Job
                                                    </a>
                                                    <button class="btn btn-outline-info" 
                                                            onclick="viewApplication(<?php echo $app['id']; ?>)">
                                                        <i class="fas fa-file-alt"></i> Details
                                                    </button>
                                                </div>
                                            </td>
                                        </tr>
                                        <?php endforeach; ?>
                                    </tbody>
                                </table>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>
    </div>
</main>

<!-- Application Details Modal -->
<div class="modal fade" id="applicationModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Application Details</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body" id="applicationDetails">
                <!-- Content will be loaded here -->
            </div>
        </div>
    </div>
</div>

<script>
function viewApplication(applicationId) {
    // Show modal with application details
    const modal = new bootstrap.Modal(document.getElementById('applicationModal'));
    document.getElementById('applicationDetails').innerHTML = '<div class="text-center"><i class="fas fa-spinner fa-spin"></i> Loading...</div>';
    modal.show();
    
    // In a real implementation, you would fetch application details via AJAX
    setTimeout(() => {
        document.getElementById('applicationDetails').innerHTML = `
            <div class="alert alert-info">
                <h6>Cover Letter</h6>
                <p>This feature will show the cover letter and other application details.</p>
            </div>
        `;
    }, 1000);
}
</script>

<?php include __DIR__ . '/../includes/footer.php'; ?>
