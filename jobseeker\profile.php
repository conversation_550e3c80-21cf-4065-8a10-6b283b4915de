<?php
require_once __DIR__ . '/../config/config.php';

// Check if user is logged in and is jobseeker
if (!isLoggedIn() || getUserType() !== 'jobseeker') {
    redirect('/auth/login.php'); // Fixed redirect path
}

try {
    $database = new Database();
    $db = $database->getConnection();
    $user_id = $_SESSION['user_id'];

    // Get current jobseeker data
    $query = "SELECT u.*, jp.* FROM users u 
              LEFT JOIN jobseeker_profiles jp ON u.id = jp.user_id 
              WHERE u.id = ? AND u.user_type = 'jobseeker'";
    $stmt = $db->prepare($query);
    $stmt->execute([$user_id]);
    $jobseeker = $stmt->fetch(PDO::FETCH_ASSOC);

    if (!$jobseeker) {
        redirect('/auth/login.php'); // Fixed redirect path
    }

    // Handle form submission
    if ($_SERVER['REQUEST_METHOD'] === 'POST') {
        try {
            $first_name = sanitize($_POST['first_name'] ?? '');
            $last_name = sanitize($_POST['last_name'] ?? '');
            $phone = sanitize($_POST['phone'] ?? '');
            $skills = sanitize($_POST['skills'] ?? '');
            $experience_years = (int)($_POST['experience_years'] ?? 0);
            $education_level = sanitize($_POST['education_level'] ?? '');
            $degree_course = sanitize($_POST['degree_course'] ?? '');
            $location = sanitize($_POST['location'] ?? '');
            $expected_salary = !empty($_POST['expected_salary']) ? (float)$_POST['expected_salary'] : null;

            // Validate required fields
            if (empty($first_name) || empty($last_name) || empty($phone)) {
                throw new Exception('Please fill in all required fields.');
            }
            
            if (strlen($phone) !== 11 || !is_numeric($phone)) {
                throw new Exception('Phone number must be exactly 11 digits.');
            }

            // Handle resume upload
            $resume_file = $jobseeker['resume_file'] ?? null; // Keep existing file
            if (isset($_FILES['resume']) && $_FILES['resume']['error'] === UPLOAD_ERR_OK) {
                $upload_dir = UPLOAD_PATH . 'resumes/';
                
                // Create directory if it doesn't exist
                if (!is_dir($upload_dir)) {
                    if (!mkdir($upload_dir, 0755, true)) {
                        throw new Exception('Failed to create upload directory.');
                    }
                }

                $file_extension = strtolower(pathinfo($_FILES['resume']['name'], PATHINFO_EXTENSION));
                $file_type = $_FILES['resume']['type'];
                
                // Validate file type
                if ($file_extension !== 'pdf') {
                    throw new Exception('Only PDF files are allowed for resume upload.');
                }
                
                // Additional MIME type check
                $finfo = finfo_open(FILEINFO_MIME_TYPE);
                $mime_type = finfo_file($finfo, $_FILES['resume']['tmp_name']);
                finfo_close($finfo);
                
                if ($mime_type !== 'application/pdf') {
                    throw new Exception('Invalid file type. Only PDF files are allowed.');
                }
                
                if ($_FILES['resume']['size'] > 5 * 1024 * 1024) { // 5MB limit
                    throw new Exception('Resume file size must be less than 5MB.');
                }
                
                $resume_filename = 'resume_' . $user_id . '_' . time() . '.pdf';
                $resume_path = $upload_dir . $resume_filename;
                
                if (!move_uploaded_file($_FILES['resume']['tmp_name'], $resume_path)) {
                    throw new Exception('Failed to upload resume file. Please try again.');
                }
                
                // Delete old resume file if exists
                if ($jobseeker['resume_file'] && file_exists($upload_dir . $jobseeker['resume_file'])) {
                    unlink($upload_dir . $jobseeker['resume_file']);
                }
                $resume_file = $resume_filename;
            } elseif (isset($_FILES['resume']) && $_FILES['resume']['error'] !== UPLOAD_ERR_NO_FILE) {
                // Handle upload errors
                switch ($_FILES['resume']['error']) {
                    case UPLOAD_ERR_INI_SIZE:
                    case UPLOAD_ERR_FORM_SIZE:
                        throw new Exception('File size exceeds the maximum allowed limit.');
                    case UPLOAD_ERR_PARTIAL:
                        throw new Exception('File upload was incomplete. Please try again.');
                    case UPLOAD_ERR_NO_TMP_DIR:
                        throw new Exception('Temporary folder is missing. Please contact administrator.');
                    case UPLOAD_ERR_CANT_WRITE:
                        throw new Exception('Failed to write file to disk. Please try again.');
                    case UPLOAD_ERR_EXTENSION:
                        throw new Exception('File upload was stopped by extension. Please try again.');
                    default:
                        throw new Exception('Unknown upload error occurred. Please try again.');
                }
            }

            $db->beginTransaction();

            // Update user table
            $query = "UPDATE users SET first_name = ?, last_name = ?, phone = ? WHERE id = ?";
            $stmt = $db->prepare($query);
            $stmt->execute([$first_name, $last_name, $phone, $user_id]);

            // Check if jobseeker profile exists
            $query = "SELECT id FROM jobseeker_profiles WHERE user_id = ?";
            $stmt = $db->prepare($query);
            $stmt->execute([$user_id]);
            $profile_exists = $stmt->fetch();

            if ($profile_exists) {
                // Update jobseeker profile - check if columns exist first
                $update_fields = [];
                $update_values = [];
                
                // Always update these fields
                $update_fields[] = "skills = ?";
                $update_values[] = $skills;
                
                $update_fields[] = "experience_years = ?";
                $update_values[] = $experience_years;
                
                $update_fields[] = "education_level = ?";
                $update_values[] = $education_level;
                
                $update_fields[] = "degree_course = ?";
                $update_values[] = $degree_course;
                
                // Try to update location if column exists
                try {
                    $test_query = "SELECT location FROM jobseeker_profiles LIMIT 1";
                    $db->query($test_query);
                    $update_fields[] = "location = ?";
                    $update_values[] = $location;
                } catch (Exception $e) {
                    // Column doesn't exist, skip it
                }
                
                // Try to update expected_salary if column exists
                try {
                    $test_query = "SELECT expected_salary FROM jobseeker_profiles LIMIT 1";
                    $db->query($test_query);
                    $update_fields[] = "expected_salary = ?";
                    $update_values[] = $expected_salary;
                } catch (Exception $e) {
                    // Column doesn't exist, skip it
                }
                
                // Try to update resume_file if column exists
                try {
                    $test_query = "SELECT resume_file FROM jobseeker_profiles LIMIT 1";
                    $db->query($test_query);
                    $update_fields[] = "resume_file = ?";
                    $update_values[] = $resume_file;
                } catch (Exception $e) {
                    // Column doesn't exist, skip it
                }
                
                $update_values[] = $user_id;
                
                $query = "UPDATE jobseeker_profiles SET " . implode(", ", $update_fields) . " WHERE user_id = ?";
                $stmt = $db->prepare($query);
                $stmt->execute($update_values);
            } else {
                // Insert new jobseeker profile - use only existing columns
                $insert_fields = ["user_id", "skills", "experience_years", "education_level", "degree_course"];
                $insert_values = [$user_id, $skills, $experience_years, $education_level, $degree_course];
                $placeholders = ["?", "?", "?", "?", "?"];
                
                // Try to add location if column exists
                try {
                    $test_query = "SELECT location FROM jobseeker_profiles LIMIT 1";
                    $db->query($test_query);
                    $insert_fields[] = "location";
                    $insert_values[] = $location;
                    $placeholders[] = "?";
                } catch (Exception $e) {
                    // Column doesn't exist, skip it
                }
                
                // Try to add expected_salary if column exists
                try {
                    $test_query = "SELECT expected_salary FROM jobseeker_profiles LIMIT 1";
                    $db->query($test_query);
                    $insert_fields[] = "expected_salary";
                    $insert_values[] = $expected_salary;
                    $placeholders[] = "?";
                } catch (Exception $e) {
                    // Column doesn't exist, skip it
                }
                
                // Try to add resume_file if column exists
                try {
                    $test_query = "SELECT resume_file FROM jobseeker_profiles LIMIT 1";
                    $db->query($test_query);
                    $insert_fields[] = "resume_file";
                    $insert_values[] = $resume_file;
                    $placeholders[] = "?";
                } catch (Exception $e) {
                    // Column doesn't exist, skip it
                }
                
                $query = "INSERT INTO jobseeker_profiles (" . implode(", ", $insert_fields) . ") VALUES (" . implode(", ", $placeholders) . ")";
                $stmt = $db->prepare($query);
                $stmt->execute($insert_values);
            }

            $db->commit();
            
            // Update session data
            $_SESSION['first_name'] = $first_name;
            $_SESSION['last_name'] = $last_name;
            
            $_SESSION['success'] = 'Profile updated successfully!';
            redirect('/jobseeker/profile.php'); // Redirect back to profile page to show updated resume
            
        } catch (Exception $e) {
            $db->rollback();
            error_log("Profile update error: " . $e->getMessage());
            $_SESSION['error'] = $e->getMessage();
            redirect('/jobseeker/profile.php'); // Fixed redirect path
        }
    }

} catch (Exception $e) {
    error_log("Jobseeker profile error: " . $e->getMessage());
    $_SESSION['error'] = "Database error occurred.";
    redirect('/jobseeker/dashboard.php'); // Fixed redirect path
}

$page_title = "Edit Profile";
include __DIR__ . '/../includes/header.php';
?>

<main class="py-4">
    <div class="container">
        <div class="row">
            <div class="col-lg-8 mx-auto">
                <div class="card shadow">
                    <div class="card-header">
                        <h4 class="mb-0"><i class="fas fa-user me-2"></i>My Profile</h4>
                    </div>
                    <div class="card-body">
                        <?php if (isset($_SESSION['error'])): ?>
                        <div class="alert alert-danger alert-dismissible fade show" role="alert">
                            <?php echo htmlspecialchars($_SESSION['error']); ?>
                            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                        </div>
                        <?php unset($_SESSION['error']); endif; ?>

                        <?php if (isset($_SESSION['success'])): ?>
                        <div class="alert alert-success alert-dismissible fade show" role="alert">
                            <?php echo htmlspecialchars($_SESSION['success']); ?>
                            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                        </div>
                        <?php unset($_SESSION['success']); endif; ?>

                        <form method="POST" enctype="multipart/form-data">
                            <!-- Personal Information -->
                            <div class="mb-4">
                                <h5 class="border-bottom pb-2 mb-3">Personal Information</h5>
                                <div class="row">
                                    <div class="col-md-6 mb-3">
                                        <label for="first_name" class="form-label">First Name <span class="text-danger">*</span></label>
                                        <input type="text" class="form-control" id="first_name" name="first_name" 
                                               value="<?php echo htmlspecialchars($jobseeker['first_name'] ?? ''); ?>" required>
                                    </div>
                                    <div class="col-md-6 mb-3">
                                        <label for="last_name" class="form-label">Last Name <span class="text-danger">*</span></label>
                                        <input type="text" class="form-control" id="last_name" name="last_name" 
                                               value="<?php echo htmlspecialchars($jobseeker['last_name'] ?? ''); ?>" required>
                                    </div>
                                </div>
                                <div class="row">
                                    <div class="col-md-6 mb-3">
                                        <label for="email" class="form-label">Email Address</label>
                                        <input type="email" class="form-control" id="email" 
                                               value="<?php echo htmlspecialchars($jobseeker['email'] ?? ''); ?>" disabled>
                                        <small class="form-text text-muted">Email cannot be changed</small>
                                    </div>
                                    <div class="col-md-6 mb-3">
                                        <label for="phone" class="form-label">Phone Number <span class="text-danger">*</span></label>
                                        <div class="input-group">
                                            <span class="input-group-text">+63</span>
                                            <input type="tel" class="form-control" id="phone" name="phone" 
                                                   placeholder="9123456789" maxlength="11" pattern="[0-9]{11}" 
                                                   value="<?php echo htmlspecialchars($jobseeker['phone'] ?? ''); ?>" required>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Professional Information -->
                            <div class="mb-4">
                                <h5 class="border-bottom pb-2 mb-3">Professional Information</h5>
                                <div class="mb-3">
                                    <label for="skills" class="form-label">Skills</label>
                                    <input type="text" class="form-control" id="skills" name="skills" 
                                           placeholder="e.g. PHP, JavaScript, HTML, CSS, MySQL"
                                           value="<?php echo htmlspecialchars($jobseeker['skills'] ?? ''); ?>">
                                    <small class="form-text text-muted">Separate skills with commas</small>
                                </div>
                                <div class="row">
                                    <div class="col-md-6 mb-3">
                                        <label for="experience_years" class="form-label">Years of Experience</label>
                                        <input type="number" class="form-control" id="experience_years" name="experience_years" 
                                               min="0" value="<?php echo $jobseeker['experience_years'] ?? 0; ?>">
                                    </div>
                                    <div class="col-md-6 mb-3">
                                        <label for="education_level" class="form-label">Education Level</label>
                                        <select class="form-select" id="education_level" name="education_level">
                                            <option value="">Select Education Level</option>
                                            <option value="High School" <?php echo ($jobseeker['education_level'] ?? '') === 'High School' ? 'selected' : ''; ?>>High School</option>
                                            <option value="Associate Degree" <?php echo ($jobseeker['education_level'] ?? '') === 'Associate Degree' ? 'selected' : ''; ?>>Associate Degree</option>
                                            <option value="Bachelor's Degree" <?php echo ($jobseeker['education_level'] ?? '') === "Bachelor's Degree" ? 'selected' : ''; ?>>Bachelor's Degree</option>
                                            <option value="Master's Degree" <?php echo ($jobseeker['education_level'] ?? '') === "Master's Degree" ? 'selected' : ''; ?>>Master's Degree</option>
                                            <option value="Doctorate" <?php echo ($jobseeker['education_level'] ?? '') === 'Doctorate' ? 'selected' : ''; ?>>Doctorate</option>
                                        </select>
                                    </div>
                                </div>
                                <div class="row">
                                    <div class="col-md-6 mb-3">
                                        <label for="location" class="form-label">Location</label>
                                        <input type="text" class="form-control" id="location" name="location" 
                                               value="<?php echo htmlspecialchars($jobseeker['location'] ?? 'Midsayap, Cotabato'); ?>">
                                    </div>
                                    <div class="col-md-6 mb-3">
                                        <label for="expected_salary" class="form-label">Expected Salary</label>
                                        <div class="input-group">
                                            <span class="input-group-text">₱</span>
                                            <input type="number" class="form-control" id="expected_salary" name="expected_salary" 
                                                   min="0" step="1000" value="<?php echo $jobseeker['expected_salary'] ?? ''; ?>">
                                            <span class="input-group-text">/month</span>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Resume Upload -->
                            <div class="mb-4">
                                <h5 class="border-bottom pb-2 mb-3">Resume Upload</h5>
                                <div class="mb-3">
                                    <label for="resume" class="form-label">Upload Resume (PDF only)</label>
                                    <input type="file" class="form-control" id="resume" name="resume" accept=".pdf">
                                    <small class="form-text text-muted">Maximum file size: 5MB. Only PDF files are allowed.</small>
                                    
                                    <?php if (!empty($jobseeker['resume_file'])): ?>
                                    <div class="mt-3">
                                        <div class="alert alert-info d-flex align-items-center">
                                            <i class="fas fa-file-pdf fa-2x me-3 text-danger"></i>
                                            <div class="flex-grow-1">
                                                <strong>Current Resume:</strong> <?php echo htmlspecialchars($jobseeker['resume_file']); ?>
                                                <br>
                                                <small class="text-muted">Uploaded: <?php echo date('M j, Y', filemtime(__DIR__ . '/../uploads/resumes/' . $jobseeker['resume_file'])); ?></small>
                                            </div>
                                            <div>
                                                <a href="../uploads/resumes/<?php echo htmlspecialchars($jobseeker['resume_file']); ?>" 
                                                   target="_blank" class="btn btn-outline-primary btn-sm">
                                                    <i class="fas fa-eye me-1"></i>View Resume
                                                </a>
                                            </div>
                                        </div>
                                    </div>
                                    <?php else: ?>
                                    <div class="mt-2">
                                        <div class="alert alert-warning">
                                            <i class="fas fa-exclamation-triangle me-2"></i>
                                            <strong>No resume uploaded.</strong> Upload your resume to increase your chances of getting hired!
                                        </div>
                                    </div>
                                    <?php endif; ?>
                                </div>
                            </div>

                            <div class="d-flex justify-content-between">
                                <a href="dashboard.php" class="btn btn-secondary">
                                    <i class="fas fa-arrow-left me-1"></i>Back to Dashboard
                                </a>
                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-save me-1"></i>Save Changes
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>
</main>

<script>
// Phone number validation
document.getElementById('phone').addEventListener('input', function(e) {
    let value = e.target.value.replace(/\D/g, '');
    if (value.length > 11) {
        value = value.slice(0, 11);
    }
    e.target.value = value;
});

// File upload validation and preview
document.getElementById('resume').addEventListener('change', function(e) {
    const file = e.target.files[0];
    
    // Remove existing preview
    const existingPreview = document.querySelector('.file-info-preview');
    if (existingPreview) {
        existingPreview.remove();
    }
    
    if (file) {
        // Validate file type
        if (file.type !== 'application/pdf') {
            alert('Only PDF files are allowed.');
            e.target.value = '';
            return;
        }
        
        // Validate file size (5MB)
        if (file.size > 5 * 1024 * 1024) {
            alert('File size must be less than 5MB.');
            e.target.value = '';
            return;
        }
        
        // Show file info
        const fileInfo = document.createElement('div');
        fileInfo.className = 'mt-2 p-2 bg-light rounded file-info-preview';
        fileInfo.innerHTML = `
            <div class="d-flex align-items-center">
                <i class="fas fa-file-pdf text-danger me-2"></i>
                <div>
                    <strong>${file.name}</strong><br>
                    <small class="text-muted">Size: ${(file.size / 1024 / 1024).toFixed(2)} MB</small>
                </div>
            </div>
        `;
        
        e.target.parentNode.appendChild(fileInfo);
    }
});

// Form validation and submission
document.querySelector('form').addEventListener('submit', function(e) {
    const firstName = document.getElementById('first_name').value.trim();
    const lastName = document.getElementById('last_name').value.trim();
    const phone = document.getElementById('phone').value.trim();
    
    if (!firstName || !lastName || !phone) {
        e.preventDefault();
        alert('Please fill in all required fields.');
        return false;
    }
    
    if (phone.length !== 11 || !/^\d+$/.test(phone)) {
        e.preventDefault();
        alert('Phone number must be exactly 11 digits.');
        return false;
    }
    
    // Show loading state but don't prevent form submission
    const submitBtn = this.querySelector('button[type="submit"]');
    submitBtn.disabled = true;
    submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-1"></i>Saving...';
    
    // Allow form to submit normally
    return true;
});

// Preserve file input value on page load
document.addEventListener('DOMContentLoaded', function() {
    const resumeInput = document.getElementById('resume');
    if (resumeInput && resumeInput.files.length > 0) {
        // Trigger change event to show preview
        resumeInput.dispatchEvent(new Event('change'));
    }
});
</script>

<?php include __DIR__ . '/../includes/footer.php'; ?>
