<?php
require_once __DIR__ . '/../config/config.php';

// Check if user is logged in and is admin
if (!isLoggedIn() || getUserType() !== 'admin') {
    redirect('/auth/login.php');
}

try {
    $database = new Database();
    $db = $database->getConnection();
    
    // Initialize variables
    $applications = [];
    $total_applications = 0;
    $status_filter = sanitize($_GET['status'] ?? '');
    $search = sanitize($_GET['search'] ?? '');
    $page = max(1, (int)($_GET['page'] ?? 1));
    $per_page = 20;
    $offset = ($page - 1) * $per_page;
    
    // Build query
    try {
        $where_conditions = [];
        $params = [];
        
        if (!empty($status_filter)) {
            $where_conditions[] = "ja.status = ?";
            $params[] = $status_filter;
        }
        
        if (!empty($search)) {
            $where_conditions[] = "(jp.title LIKE ? OR bp.company_name LIKE ? OR CONCAT(u1.first_name, ' ', u1.last_name) LIKE ?)";
            $search_term = "%$search%";
            $params[] = $search_term;
            $params[] = $search_term;
            $params[] = $search_term;
        }
        
        $where_clause = !empty($where_conditions) ? "WHERE " . implode(" AND ", $where_conditions) : "";
        
        // Get total count
        $count_query = "SELECT COUNT(*) as total FROM job_applications ja
                        JOIN job_posts jp ON ja.job_id = jp.id
                        JOIN business_profiles bp ON jp.business_id = bp.user_id
                        JOIN users u1 ON ja.jobseeker_id = u1.id
                        $where_clause";
        $stmt = $db->prepare($count_query);
        $stmt->execute($params);
        $result = $stmt->fetch(PDO::FETCH_ASSOC);
        $total_applications = $result ? (int)$result['total'] : 0;
        
        // Get applications with pagination
        $query = "SELECT ja.*, jp.title, jp.location, jp.job_type, jp.salary_min, jp.salary_max,
                         bp.company_name, bp.business_address,
                         u1.first_name as applicant_first_name, u1.last_name as applicant_last_name, u1.email as applicant_email,
                         u2.first_name as business_first_name, u2.last_name as business_last_name
                  FROM job_applications ja
                  JOIN job_posts jp ON ja.job_id = jp.id
                  JOIN business_profiles bp ON jp.business_id = bp.user_id
                  JOIN users u1 ON ja.jobseeker_id = u1.id
                  JOIN users u2 ON jp.business_id = u2.id
                  $where_clause 
                  ORDER BY ja.applied_at DESC 
                  LIMIT ? OFFSET ?";
        
        $params[] = $per_page;
        $params[] = $offset;
        
        $stmt = $db->prepare($query);
        $stmt->execute($params);
        $applications = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
    } catch (Exception $e) {
        error_log("Applications query error: " . $e->getMessage());
        $applications = [];
        $total_applications = 0;
    }
    
    // Calculate pagination
    $total_pages = ceil($total_applications / $per_page);
    
} catch (Exception $e) {
    error_log("Admin applications page error: " . $e->getMessage());
    $_SESSION['error'] = "Database error occurred.";
    $applications = [];
    $total_applications = 0;
    $total_pages = 0;
}

$page_title = "Application Management";
include __DIR__ . '/../includes/header.php';
?>

<main class="py-4">
    <div class="container-fluid">
        <!-- Success/Error Messages -->
        <?php if (isset($_SESSION['success'])): ?>
        <div class="alert alert-success alert-dismissible fade show" role="alert">
            <i class="fas fa-check-circle me-2"></i>
            <?php echo htmlspecialchars($_SESSION['success']); unset($_SESSION['success']); ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
        <?php endif; ?>

        <?php if (isset($_SESSION['error'])): ?>
        <div class="alert alert-danger alert-dismissible fade show" role="alert">
            <i class="fas fa-exclamation-circle me-2"></i>
            <?php echo htmlspecialchars($_SESSION['error']); unset($_SESSION['error']); ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
        <?php endif; ?>

        <!-- Header -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h1 class="h3 mb-0">Application Management</h1>
                        <p class="text-muted">
                            <?php if (!empty($status_filter)): ?>
                                Showing <?php echo ucfirst($status_filter); ?> Applications
                            <?php else: ?>
                                All Applications
                            <?php endif; ?>
                            (<?php echo number_format($total_applications); ?> total)
                        </p>
                    </div>
                    <div class="d-flex gap-2">
                        <a href="dashboard.php" class="btn btn-outline-primary">
                            <i class="fas fa-arrow-left me-1"></i>Back to Dashboard
                        </a>
                    </div>
                </div>
            </div>
        </div>

        <!-- Filters -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-body">
                        <form method="GET" class="row g-3">
                            <div class="col-md-4">
                                <label for="search" class="form-label">Search Applications</label>
                                <input type="text" class="form-control" id="search" name="search" 
                                       placeholder="Job title, company, or applicant name" value="<?php echo htmlspecialchars($search); ?>">
                            </div>
                            <div class="col-md-3">
                                <label for="status" class="form-label">Status</label>
                                <select class="form-select" id="status" name="status">
                                    <option value="">All Status</option>
                                    <option value="pending" <?php echo $status_filter === 'pending' ? 'selected' : ''; ?>>Pending</option>
                                    <option value="reviewed" <?php echo $status_filter === 'reviewed' ? 'selected' : ''; ?>>Under Review</option>
                                    <option value="accepted" <?php echo $status_filter === 'accepted' ? 'selected' : ''; ?>>Accepted</option>
                                    <option value="rejected" <?php echo $status_filter === 'rejected' ? 'selected' : ''; ?>>Rejected</option>
                                </select>
                            </div>
                            <div class="col-md-3 d-flex align-items-end">
                                <button type="submit" class="btn btn-primary me-2">
                                    <i class="fas fa-search"></i> Filter
                                </button>
                                <a href="applications.php" class="btn btn-outline-secondary">Clear</a>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>

        <!-- Applications Table -->
        <div class="row">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">Applications (<?php echo count($applications); ?> of <?php echo number_format($total_applications); ?>)</h5>
                    </div>
                    <div class="card-body">
                        <?php if (empty($applications)): ?>
                            <div class="text-center py-5">
                                <i class="fas fa-file-alt fa-3x text-muted mb-3"></i>
                                <h5>No Applications Found</h5>
                                <p class="text-muted">
                                    <?php if (!empty($status_filter) || !empty($search)): ?>
                                        Try adjusting your filters or <a href="applications.php">view all applications</a>.
                                    <?php else: ?>
                                        No job applications have been submitted yet.
                                    <?php endif; ?>
                                </p>
                            </div>
                        <?php else: ?>
                            <div class="table-responsive">
                                <table class="table table-hover">
                                    <thead>
                                        <tr>
                                            <th>Application</th>
                                            <th>Job Details</th>
                                            <th>Company</th>
                                            <th>Status</th>
                                            <th>Applied</th>
                                            <th>Actions</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <?php foreach ($applications as $app): ?>
                                        <tr>
                                            <td>
                                                <div class="d-flex align-items-center">
                                                    <div class="avatar-sm me-3">
                                                        <i class="fas fa-user-circle fa-2x text-primary"></i>
                                                    </div>
                                                    <div>
                                                        <strong><?php echo htmlspecialchars($app['applicant_first_name'] . ' ' . $app['applicant_last_name']); ?></strong>
                                                        <br>
                                                        <small class="text-muted"><?php echo htmlspecialchars($app['applicant_email']); ?></small>
                                                    </div>
                                                </div>
                                            </td>
                                            <td>
                                                <div>
                                                    <strong><?php echo htmlspecialchars($app['title']); ?></strong>
                                                    <br>
                                                    <small class="text-muted">
                                                        <i class="fas fa-map-marker-alt me-1"></i><?php echo htmlspecialchars($app['location']); ?>
                                                        <span class="mx-2">•</span>
                                                        <span class="badge bg-info"><?php echo ucfirst(str_replace('-', ' ', $app['job_type'])); ?></span>
                                                    </small>
                                                    <?php if ($app['salary_min'] && $app['salary_max']): ?>
                                                    <br>
                                                    <small class="text-success">
                                                        ₱<?php echo number_format($app['salary_min']); ?> - ₱<?php echo number_format($app['salary_max']); ?>
                                                    </small>
                                                    <?php endif; ?>
                                                </div>
                                            </td>
                                            <td>
                                                <strong><?php echo htmlspecialchars($app['company_name']); ?></strong>
                                                <br>
                                                <small class="text-muted">
                                                    <?php echo htmlspecialchars($app['business_first_name'] . ' ' . $app['business_last_name']); ?>
                                                </small>
                                                <?php if (!empty($app['business_address'])): ?>
                                                <br>
                                                <small class="text-muted">
                                                    <i class="fas fa-map-marker-alt me-1"></i><?php echo htmlspecialchars($app['business_address']); ?>
                                                </small>
                                                <?php endif; ?>
                                            </td>
                                            <td>
                                                <?php
                                                $status_colors = [
                                                    'pending' => 'warning',
                                                    'reviewed' => 'info',
                                                    'accepted' => 'success',
                                                    'rejected' => 'danger'
                                                ];
                                                $status_icons = [
                                                    'pending' => 'clock',
                                                    'reviewed' => 'eye',
                                                    'accepted' => 'check',
                                                    'rejected' => 'times'
                                                ];
                                                ?>
                                                <span class="badge bg-<?php echo $status_colors[$app['status']] ?? 'secondary'; ?>">
                                                    <i class="fas fa-<?php echo $status_icons[$app['status']] ?? 'question'; ?> me-1"></i>
                                                    <?php echo ucfirst($app['status']); ?>
                                                </span>
                                            </td>
                                            <td>
                                                <small class="text-muted">
                                                    <?php echo date('M j, Y', strtotime($app['applied_at'])); ?>
                                                    <br>
                                                    <?php echo date('g:i A', strtotime($app['applied_at'])); ?>
                                                </small>
                                            </td>
                                            <td>
                                                <div class="btn-group btn-group-sm">
                                                    <button class="btn btn-outline-info" 
                                                            onclick="viewApplication(<?php echo $app['id']; ?>)" 
                                                            title="View Details">
                                                        <i class="fas fa-eye"></i>
                                                    </button>
                                                    <a href="../job-details.php?id=<?php echo $app['job_id']; ?>" 
                                                       class="btn btn-outline-primary" target="_blank" title="View Job">
                                                        <i class="fas fa-briefcase"></i>
                                                    </a>
                                                    <button class="btn btn-outline-danger" 
                                                            onclick="deleteApplication(<?php echo $app['id']; ?>)" 
                                                            title="Delete">
                                                        <i class="fas fa-trash"></i>
                                                    </button>
                                                </div>
                                            </td>
                                        </tr>
                                        <?php endforeach; ?>
                                    </tbody>
                                </table>
                            </div>

                            <!-- Pagination -->
                            <?php if ($total_pages > 1): ?>
                            <nav aria-label="Applications pagination" class="mt-4">
                                <ul class="pagination justify-content-center">
                                    <?php if ($page > 1): ?>
                                    <li class="page-item">
                                        <a class="page-link" href="?<?php echo http_build_query(array_merge($_GET, ['page' => $page - 1])); ?>">
                                            <i class="fas fa-chevron-left"></i> Previous
                                        </a>
                                    </li>
                                    <?php endif; ?>
                                    
                                    <?php for ($i = max(1, $page - 2); $i <= min($total_pages, $page + 2); $i++): ?>
                                    <li class="page-item <?php echo $i === $page ? 'active' : ''; ?>">
                                        <a class="page-link" href="?<?php echo http_build_query(array_merge($_GET, ['page' => $i])); ?>">
                                            <?php echo $i; ?>
                                        </a>
                                    </li>
                                    <?php endfor; ?>
                                    
                                    <?php if ($page < $total_pages): ?>
                                    <li class="page-item">
                                        <a class="page-link" href="?<?php echo http_build_query(array_merge($_GET, ['page' => $page + 1])); ?>">
                                            Next <i class="fas fa-chevron-right"></i>
                                        </a>
                                    </li>
                                    <?php endif; ?>
                                </ul>
                            </nav>
                            <?php endif; ?>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>
    </div>
</main>

<script>
function viewApplication(applicationId) {
    alert('Application details view - to be implemented for application ID: ' + applicationId);
}

function deleteApplication(applicationId) {
    if (confirm('Are you sure you want to delete this application? This action cannot be undone.')) {
        // Implement deletion logic
        alert('Application deletion - to be implemented for application ID: ' + applicationId);
    }
}
</script>

<?php include __DIR__ . '/../includes/footer.php'; ?> 