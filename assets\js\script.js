// Tan-Aw Job Portal - Main JavaScript File

// Enhanced Theme Management with Better UX
class ThemeManager {
  constructor() {
    this.theme = localStorage.getItem("theme") || "light"
    this.isTransitioning = false
    this.init()
  }

  init() {
    this.applyTheme(false)
    this.bindEvents()
    this.addThemeTransitionClass()
  }

  addThemeTransitionClass() {
    // Add transition class to body for smooth theme changes
    document.body.classList.add('theme-transition')
  }

  bindEvents() {
    const themeToggle = document.getElementById("themeToggle")
    
    if (themeToggle) {
      themeToggle.addEventListener("click", (e) => {
        e.preventDefault()
        this.toggleTheme()
      })

      // Add keyboard support
      themeToggle.addEventListener("keydown", (e) => {
        if (e.key === 'Enter' || e.key === ' ') {
          e.preventDefault()
          this.toggleTheme()
        }
      })

      // Add hover effect for better UX
      themeToggle.addEventListener("mouseenter", () => {
        themeToggle.style.transform = "scale(1.1)"
      })

      themeToggle.addEventListener("mouseleave", () => {
        themeToggle.style.transform = "scale(1)"
      })
    }
  }

  toggleTheme() {
    if (this.isTransitioning) return

    this.isTransitioning = true
    
    // Add visual feedback
    const themeToggle = document.getElementById("themeToggle")
    if (themeToggle) {
      themeToggle.classList.add("theme-switching")
    }

    // Toggle theme
    this.theme = this.theme === "light" ? "dark" : "light"
    localStorage.setItem("theme", this.theme)

    // Add haptic feedback if available
    if (navigator.vibrate) {
      navigator.vibrate(50)
    }

    // Apply theme with transition
    this.applyTheme(true)

    // Remove switching class after animation
    setTimeout(() => {
      if (themeToggle) {
        themeToggle.classList.remove("theme-switching")
      }
      this.isTransitioning = false
    }, 300)

    // Dispatch custom event for other components to listen to
    window.dispatchEvent(new CustomEvent('themeChanged', {
      detail: {
        theme: this.theme,
        previousTheme: this.theme === 'light' ? 'dark' : 'light'
      }
    }))

    // Announce theme change to screen readers
    this.announceThemeChange()
  }

  applyTheme(animate = true) {
    const root = document.documentElement
    const themeIcon = document.getElementById("themeIcon")
    
    if (animate) {
      root.style.transition = "all 0.3s ease"
    } else {
      root.style.transition = "none"
    }

    root.setAttribute("data-theme", this.theme)

    // Update theme toggle icon
    if (themeIcon) {
      if (this.theme === "dark") {
        themeIcon.className = "fas fa-sun"
        themeIcon.setAttribute("aria-label", "Switch to light mode")
      } else {
        themeIcon.className = "fas fa-moon"
        themeIcon.setAttribute("aria-label", "Switch to dark mode")
      }
    }

    // Update theme toggle button title
    const themeToggle = document.getElementById("themeToggle")
    if (themeToggle) {
      themeToggle.title = this.theme === "dark" ? "Switch to light mode" : "Switch to dark mode"
    }

    // Remove transition after animation completes
    if (animate) {
      setTimeout(() => {
        root.style.transition = ""
      }, 300)
    }
  }

  announceThemeChange() {
    const announcement = document.createElement("div")
    announcement.setAttribute("aria-live", "polite")
    announcement.setAttribute("aria-atomic", "true")
    announcement.style.position = "absolute"
    announcement.style.left = "-10000px"
    announcement.style.width = "1px"
    announcement.style.height = "1px"
    announcement.style.overflow = "hidden"
    
    announcement.textContent = `Switched to ${this.theme} mode`
    
    document.body.appendChild(announcement)
    
    setTimeout(() => {
      document.body.removeChild(announcement)
    }, 1000)
  }

  getCurrentTheme() {
    return this.theme
  }

  setTheme(theme) {
    if (theme !== "light" && theme !== "dark") {
      console.error("Invalid theme. Must be 'light' or 'dark'")
      return
    }
    
    this.theme = theme
    localStorage.setItem("theme", theme)
    this.applyTheme(true)
  }
}

// Animation Manager
class AnimationManager {
  constructor() {
    this.init()
  }

  init() {
    this.observeElements()
    this.addHoverEffects()
    this.addClickEffects()
    this.animateCounters()
  }

  observeElements() {
    const observer = new IntersectionObserver(
      (entries) => {
        entries.forEach((entry) => {
          if (entry.isIntersecting) {
            entry.target.classList.add("animate-in")
            // Add staggered animation delay
            const delay = Array.from(entry.target.parentNode.children).indexOf(entry.target) * 100
            entry.target.style.animationDelay = `${delay}ms`
          }
        })
      },
      { threshold: 0.1 },
    )

    // Observe elements for animation
    document.querySelectorAll(".job-card, .feature-item, .stat-card, .card").forEach((el) => {
      observer.observe(el)
    })
  }

  addHoverEffects() {
    // Add ripple effect to buttons (excluding theme toggle)
    document.querySelectorAll(".btn:not(#themeToggle)").forEach((button) => {
      button.addEventListener("click", this.createRipple)
    })

    // Add hover effects to cards
    document.querySelectorAll(".card").forEach((card) => {
      card.addEventListener("mouseenter", function () {
        this.style.transform = "translateY(-8px) scale(1.02)"
        this.style.boxShadow = "0 20px 40px rgba(0,0,0,0.15)"
      })

      card.addEventListener("mouseleave", function () {
        this.style.transform = "translateY(0) scale(1)"
        this.style.boxShadow = ""
      })
    })

    // Add hover effects to navigation links
    document.querySelectorAll(".nav-link").forEach((link) => {
      link.addEventListener("mouseenter", function () {
        this.style.transform = "translateY(-2px)"
      })

      link.addEventListener("mouseleave", function () {
        this.style.transform = "translateY(0)"
      })
    })
  }

  addClickEffects() {
    // Add click animation to interactive elements (excluding theme toggle)
    document.querySelectorAll(".btn:not(#themeToggle), .nav-link, .dropdown-item").forEach((element) => {
      element.addEventListener("click", function (e) {
        this.style.transform = "scale(0.95)"
        setTimeout(() => {
          this.style.transform = ""
        }, 150)
      })
    })
  }

  createRipple(event) {
    const button = event.currentTarget
    const circle = document.createElement("span")
    const diameter = Math.max(button.clientWidth, button.clientHeight)
    const radius = diameter / 2

    circle.style.width = circle.style.height = `${diameter}px`
    circle.style.left = `${event.clientX - button.offsetLeft - radius}px`
    circle.style.top = `${event.clientY - button.offsetTop - radius}px`
    circle.classList.add("ripple-effect")

    const ripple = button.getElementsByClassName("ripple-effect")[0]
    if (ripple) {
      ripple.remove()
    }

    button.appendChild(circle)

    // Remove ripple after animation
    setTimeout(() => {
      circle.remove()
    }, 600)
  }

  animateCounters() {
    const counters = document.querySelectorAll(".stat-number")
    counters.forEach((counter) => {
      const target = Number.parseInt(counter.textContent.replace(/,/g, ""))
      if (target > 0) {
        this.animateCounter(counter, target)
      }
    })
  }

  animateCounter(element, target) {
    let current = 0
    const increment = target / 100
    const timer = setInterval(() => {
      current += increment
      if (current >= target) {
        element.textContent = target.toLocaleString()
        clearInterval(timer)
      } else {
        element.textContent = Math.floor(current).toLocaleString()
      }
    }, 20)
  }
}

// Form Manager
class FormManager {
  constructor() {
    this.init()
  }

  init() {
    this.bindFormEvents()
    this.addValidation()
    this.addAutoResize()
    this.addFileUploadHandlers()
  }

  bindFormEvents() {
    // Handle form submissions with loading states
    document.querySelectorAll("form").forEach((form) => {
      form.addEventListener("submit", this.handleFormSubmit)
    })
  }

  handleFormSubmit(event) {
    const form = event.target
    const submitButton = form.querySelector('button[type="submit"]')

    if (submitButton && !submitButton.disabled) {
      const originalText = submitButton.innerHTML
      submitButton.disabled = true
      submitButton.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Processing...'

      // Re-enable button after 5 seconds as fallback
      setTimeout(() => {
        if (submitButton.disabled) {
          submitButton.disabled = false
          submitButton.innerHTML = originalText
        }
      }, 5000)
    }
  }

  addFileUploadHandlers() {
    document.querySelectorAll('input[type="file"]').forEach((input) => {
      input.addEventListener("change", this.handleFileUpload.bind(this))
    })
  }

  handleFileUpload(event) {
    const input = event.target
    const file = input.files[0]

    if (file) {
      // Create preview for images
      if (file.type.startsWith("image/")) {
        this.createImagePreview(input, file)
      }

      // Show file info
      this.showFileInfo(input, file)
    }
  }

  createImagePreview(input, file) {
    const reader = new FileReader()
    reader.onload = (e) => {
      let preview = input.parentNode.querySelector(".image-preview")
      if (!preview) {
        preview = document.createElement("div")
        preview.className = "image-preview mt-3"
        input.parentNode.appendChild(preview)
      }

      preview.innerHTML = `
        <div class="preview-container">
          <img src="${e.target.result}" alt="Preview" 
               class="img-thumbnail preview-image" style="max-height: 200px; max-width: 100%;">
          <button type="button" class="btn btn-sm btn-outline-danger ms-2 remove-preview" 
                  onclick="this.parentNode.parentNode.remove(); document.getElementById('${input.id}').value='';">
              <i class="fas fa-times"></i> Remove
          </button>
        </div>
      `

      // Add animation to preview
      preview.style.opacity = "0"
      preview.style.transform = "translateY(20px)"
      setTimeout(() => {
        preview.style.transition = "all 0.3s ease"
        preview.style.opacity = "1"
        preview.style.transform = "translateY(0)"
      }, 100)
    }
    reader.readAsDataURL(file)
  }

  showFileInfo(input, file) {
    let info = input.parentNode.querySelector(".file-info")
    if (!info) {
      info = document.createElement("div")
      info.className = "file-info mt-2 p-3 bg-light rounded border"
      input.parentNode.appendChild(info)
    }

    const size = (file.size / 1024 / 1024).toFixed(2)
    info.innerHTML = `
      <div class="d-flex align-items-center">
          <i class="fas fa-file me-2 text-primary"></i>
          <div>
              <strong>${file.name}</strong><br>
              <small class="text-muted">Size: ${size} MB | Type: ${file.type}</small>
          </div>
      </div>
    `
  }

  addValidation() {
    // Real-time validation for common fields
    document.querySelectorAll('input[type="email"]').forEach((input) => {
      input.addEventListener("blur", this.validateEmail.bind(this))
    })

    document.querySelectorAll('input[type="tel"], input[name="phone"]').forEach((input) => {
      input.addEventListener("input", this.formatPhone.bind(this))
    })

    document.querySelectorAll('input[type="password"]').forEach((input) => {
      input.addEventListener("input", this.validatePassword.bind(this))
    })
  }

  validateEmail(event) {
    const input = event.target
    const email = input.value
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/

    if (email && !emailRegex.test(email)) {
      input.classList.add("is-invalid")
      this.showFieldError(input, "Please enter a valid email address")
    } else {
      input.classList.remove("is-invalid")
      this.hideFieldError(input)
    }
  }

  formatPhone(event) {
    const input = event.target
    let value = input.value.replace(/\D/g, "")

    if (value.length > 11) {
      value = value.slice(0, 11)
    }

    input.value = value

    // Validate phone number
    if (value.length > 0 && value.length !== 11) {
      input.classList.add("is-invalid")
      this.showFieldError(input, "Phone number must be exactly 11 digits")
    } else {
      input.classList.remove("is-invalid")
      this.hideFieldError(input)
    }
  }

  validatePassword(event) {
    const input = event.target
    const password = input.value

    if (password.length > 0 && password.length < 6) {
      input.classList.add("is-invalid")
      this.showFieldError(input, "Password must be at least 6 characters long")
    } else {
      input.classList.remove("is-invalid")
      this.hideFieldError(input)
    }
  }

  showFieldError(input, message) {
    let error = input.parentNode.querySelector(".field-error")
    if (!error) {
      error = document.createElement("div")
      error.className = "field-error invalid-feedback"
      input.parentNode.appendChild(error)
    }
    error.textContent = message
  }

  hideFieldError(input) {
    const error = input.parentNode.querySelector(".field-error")
    if (error) {
      error.remove()
    }
  }

  addAutoResize() {
    // Auto-resize textareas
    document.querySelectorAll("textarea").forEach((textarea) => {
      textarea.addEventListener("input", function () {
        this.style.height = "auto"
        this.style.height = this.scrollHeight + "px"
      })

      // Initial resize
      textarea.style.height = "auto"
      textarea.style.height = textarea.scrollHeight + "px"
    })
  }
}

// Search Manager
class SearchManager {
  constructor() {
    this.init()
  }

  init() {
    this.bindSearchEvents()
  }

  bindSearchEvents() {
    const searchInputs = document.querySelectorAll('input[type="search"], .search-input')
    searchInputs.forEach((input) => {
      input.addEventListener("input", this.debounce(this.handleSearch.bind(this), 300))
    })
  }

  handleSearch(event) {
    const query = event.target.value.toLowerCase()
    const searchableElements = document.querySelectorAll(".searchable")

    searchableElements.forEach((element) => {
      const text = element.textContent.toLowerCase()
      const isVisible = text.includes(query)
      element.style.display = isVisible ? "" : "none"

      // Add animation for showing/hiding
      if (isVisible) {
        element.style.animation = "fadeIn 0.3s ease"
      }
    })
  }

  debounce(func, wait) {
    let timeout
    return function executedFunction(...args) {
      const later = () => {
        clearTimeout(timeout)
        func(...args)
      }
      clearTimeout(timeout)
      timeout = setTimeout(later, wait)
    }
  }
}

// Notification Manager
class NotificationManager {
  constructor() {
    this.init()
  }

  init() {
    this.createContainer()
  }

  createContainer() {
    if (!document.getElementById("notificationContainer")) {
      const container = document.createElement("div")
      container.id = "notificationContainer"
      container.className = "notification-container position-fixed top-0 end-0 p-3"
      container.style.zIndex = "9999"
      document.body.appendChild(container)
    }
  }

  show(message, type = "info", duration = 5000) {
    const notification = document.createElement("div")
    notification.className = `alert alert-${type} alert-dismissible fade show notification-toast mb-2`
    notification.style.minWidth = "300px"
    notification.innerHTML = `
      ${message}
      <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `

    const container = document.getElementById("notificationContainer")
    container.appendChild(notification)

    // Add entrance animation
    notification.style.transform = "translateX(100%)"
    setTimeout(() => {
      notification.style.transition = "transform 0.3s ease"
      notification.style.transform = "translateX(0)"
    }, 100)

    // Auto remove after duration
    setTimeout(() => {
      if (notification.parentNode) {
        notification.style.transform = "translateX(100%)"
        setTimeout(() => {
          notification.remove()
        }, 300)
      }
    }, duration)
  }

  success(message) {
    this.show(`<i class="fas fa-check-circle me-2"></i>${message}`, "success")
  }

  error(message) {
    this.show(`<i class="fas fa-exclamation-circle me-2"></i>${message}`, "danger")
  }

  warning(message) {
    this.show(`<i class="fas fa-exclamation-triangle me-2"></i>${message}`, "warning")
  }

  info(message) {
    this.show(`<i class="fas fa-info-circle me-2"></i>${message}`, "info")
  }
}

// Loading Manager
class LoadingManager {
  static show(element, text = "Loading...") {
    if (element) {
      element.disabled = true
      element.innerHTML = `<i class="fas fa-spinner fa-spin me-2"></i>${text}`
    }
  }

  static hide(element, originalText) {
    if (element) {
      element.disabled = false
      element.innerHTML = originalText
    }
  }

  static showOverlay() {
    const overlay = document.createElement("div")
    overlay.id = "loadingOverlay"
    overlay.className =
      "loading-overlay position-fixed top-0 start-0 w-100 h-100 d-flex align-items-center justify-content-center"
    overlay.style.backgroundColor = "rgba(0,0,0,0.5)"
    overlay.style.zIndex = "9999"
    overlay.innerHTML = `
      <div class="text-center text-white">
          <i class="fas fa-spinner fa-spin fa-3x mb-3"></i>
          <h4>Loading...</h4>
      </div>
    `
    document.body.appendChild(overlay)
  }

  static hideOverlay() {
    const overlay = document.getElementById("loadingOverlay")
    if (overlay) {
      overlay.remove()
    }
  }
}

// Smooth Scroll Manager
class SmoothScrollManager {
  constructor() {
    this.init()
  }

  init() {
    // Add smooth scrolling to anchor links
    document.querySelectorAll('a[href^="#"]').forEach((anchor) => {
      anchor.addEventListener("click", (e) => {
        e.preventDefault()
        const target = document.querySelector(anchor.getAttribute("href"))
        if (target) {
          target.scrollIntoView({
            behavior: "smooth",
            block: "start",
          })
        }
      })
    })

    // Add scroll-to-top functionality
    this.addScrollToTop()
  }

  addScrollToTop() {
    const scrollBtn = document.createElement("button")
    scrollBtn.innerHTML = '<i class="fas fa-arrow-up"></i>'
    scrollBtn.className = "btn btn-primary position-fixed bottom-0 end-0 m-4 rounded-circle scroll-to-top"
    scrollBtn.style.width = "50px"
    scrollBtn.style.height = "50px"
    scrollBtn.style.display = "none"
    scrollBtn.style.zIndex = "1000"
    document.body.appendChild(scrollBtn)

    // Show/hide scroll button
    window.addEventListener("scroll", () => {
      if (window.pageYOffset > 300) {
        scrollBtn.style.display = "block"
        scrollBtn.style.animation = "fadeIn 0.3s ease"
      } else {
        scrollBtn.style.display = "none"
      }
    })

    // Scroll to top functionality
    scrollBtn.addEventListener("click", () => {
      window.scrollTo({
        top: 0,
        behavior: "smooth",
      })
    })
  }
}

// Enhanced Jobs Page Animations
class JobsPageManager {
  constructor() {
    this.init()
  }

  init() {
    this.addScrollAnimations()
    this.addFormAnimations()
    this.addCardInteractions()
    this.addSearchEnhancements()
  }

  addScrollAnimations() {
    // Animate elements on scroll
    const observer = new IntersectionObserver((entries) => {
      entries.forEach((entry) => {
        if (entry.isIntersecting) {
          entry.target.style.animation = 'fadeInUp 0.6s ease-out forwards'
          entry.target.style.opacity = '1'
        }
      })
    }, { threshold: 0.1 })

    // Observe job cards and other elements
    document.querySelectorAll('.job-card, .search-form, .pagination').forEach((el) => {
      el.style.opacity = '0'
      observer.observe(el)
    })
  }

  addFormAnimations() {
    const form = document.querySelector('.search-form form')
    if (!form) return

    // Add floating labels
    const inputs = form.querySelectorAll('.form-control, .form-select')
    inputs.forEach((input) => {
      input.addEventListener('focus', () => {
        input.parentElement.style.transform = 'translateY(-2px)'
        input.parentElement.style.transition = 'transform 0.3s ease'
      })

      input.addEventListener('blur', () => {
        input.parentElement.style.transform = 'translateY(0)'
      })
    })

    // Add search button pulse animation
    const searchBtn = form.querySelector('.btn-primary')
    if (searchBtn) {
      searchBtn.addEventListener('mouseenter', () => {
        searchBtn.style.animation = 'pulse 0.6s ease-in-out'
      })
      searchBtn.addEventListener('mouseleave', () => {
        searchBtn.style.animation = 'pulse 2s infinite'
      })
    }
  }

  addCardInteractions() {
    const jobCards = document.querySelectorAll('.job-card')
    jobCards.forEach((card, index) => {
      // Staggered animation delay
      card.style.animationDelay = `${index * 0.1}s`

      // Add hover effects
      card.addEventListener('mouseenter', () => {
        card.style.transform = 'translateY(-10px) scale(1.02)'
        card.style.boxShadow = '0 20px 50px rgba(0, 0, 0, 0.15)'
      })

      card.addEventListener('mouseleave', () => {
        card.style.transform = 'translateY(0) scale(1)'
        card.style.boxShadow = '0 10px 30px rgba(0, 0, 0, 0.1)'
      })

      // Add click ripple effect
      card.addEventListener('click', (e) => {
        if (e.target.tagName === 'A') return // Don't add ripple to links
        
        const ripple = document.createElement('div')
        ripple.className = 'ripple'
        ripple.style.left = e.clientX - card.offsetLeft + 'px'
        ripple.style.top = e.clientY - card.offsetTop + 'px'
        card.appendChild(ripple)
        
        setTimeout(() => ripple.remove(), 600)
      })
    })
  }

  addSearchEnhancements() {
    // Add live search suggestions (if needed)
    const searchInput = document.querySelector('#search')
    if (searchInput) {
      searchInput.addEventListener('input', this.debounce(() => {
        // Add subtle animation to show search is active
        searchInput.style.borderColor = 'var(--primary-color)'
        setTimeout(() => {
          searchInput.style.borderColor = ''
        }, 300)
      }, 300))
    }

    // Add form submission animation
    const form = document.querySelector('.search-form form')
    if (form) {
      form.addEventListener('submit', () => {
        const submitBtn = form.querySelector('.btn-primary')
        if (submitBtn) {
          submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i>'
          submitBtn.disabled = true
        }
      })
    }
  }

  debounce(func, wait) {
    let timeout
    return function executedFunction(...args) {
      const later = () => {
        clearTimeout(timeout)
        func(...args)
      }
      clearTimeout(timeout)
      timeout = setTimeout(later, wait)
    }
  }
}

// Initialize everything when DOM is loaded
document.addEventListener("DOMContentLoaded", () => {
  // Initialize all managers
  const themeManager = new ThemeManager()
  const animationManager = new AnimationManager()
  const formManager = new FormManager()
  const searchManager = new SearchManager()
  const notificationManager = new NotificationManager()
  const smoothScrollManager = new SmoothScrollManager()
  const jobsPageManager = new JobsPageManager() // Initialize the new manager

  // Initialize tooltips and popovers if Bootstrap is available
  const bootstrap = window.bootstrap // Declare bootstrap variable
  if (bootstrap) {
    const tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'))
    tooltipTriggerList.map((tooltipTriggerEl) => new bootstrap.Tooltip(tooltipTriggerEl))

    const popoverTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="popover"]'))
    popoverTriggerList.map((popoverTriggerEl) => new bootstrap.Popover(popoverTriggerEl))
  }

  // Add page transition effect
  document.body.style.opacity = "0"
  setTimeout(() => {
    document.body.style.transition = "opacity 0.5s ease"
    document.body.style.opacity = "1"
  }, 100)

  // Handle navigation active states
  const currentPath = window.location.pathname
  document.querySelectorAll(".nav-link").forEach((link) => {
    if (
      link.getAttribute("href") === currentPath ||
      (currentPath.endsWith("/") && link.getAttribute("href").endsWith("/"))
    ) {
      link.classList.add("active")
    }
  })

  // Global error handler for AJAX requests
  window.addEventListener("unhandledrejection", (event) => {
    console.error("Unhandled promise rejection:", event.reason)
    notificationManager.error("An unexpected error occurred. Please try again.")
  })

  // Auto-hide alerts
  const alerts = document.querySelectorAll(".alert:not(.alert-permanent)")
  alerts.forEach((alert) => {
    setTimeout(() => {
      if (alert.parentNode) {
        alert.style.animation = "slideOutUp 0.5s ease-in"
        setTimeout(() => {
          alert.remove()
        }, 500)
      }
    }, 5000)
  })
})

// Global utility functions
window.showNotification = (message, type = "info") => {
  const notificationManager = new NotificationManager()
  notificationManager.show(message, type)
}

window.showLoading = LoadingManager.showOverlay
window.hideLoading = LoadingManager.hideOverlay

// Export managers for global use
window.ThemeManager = ThemeManager
window.AnimationManager = AnimationManager
window.FormManager = FormManager
window.NotificationManager = NotificationManager
window.LoadingManager = LoadingManager

// Skill matching visualization
window.updateSkillMatch = (jobSkills, userSkills) => {
  const jobSkillsArray = jobSkills
    .toLowerCase()
    .split(",")
    .map((s) => s.trim())
  const userSkillsArray = userSkills
    .toLowerCase()
    .split(",")
    .map((s) => s.trim())

  const matches = jobSkillsArray.filter((skill) => userSkillsArray.includes(skill))
  const percentage = Math.round((matches.length / jobSkillsArray.length) * 100)

  return {
    percentage,
    matches,
    total: jobSkillsArray.length,
  }
}

// Copy to clipboard
window.copyToClipboard = (text) => {
  navigator.clipboard
    .writeText(text)
    .then(() => {
      window.showNotification("Copied to clipboard!", "success", 2000)
    })
    .catch(() => {
      window.showNotification("Failed to copy to clipboard", "danger", 2000)
    })
}

// Print functionality
window.printElement = (elementId) => {
  const element = document.getElementById(elementId)
  if (element) {
    const printWindow = window.open("", "_blank")
    printWindow.document.write(`
      <html>
          <head>
              <title>Print</title>
              <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
              <style>
                  body { font-family: Arial, sans-serif; }
                  .no-print { display: none !important; }
              </style>
          </head>
          <body>
              ${element.innerHTML}
          </body>
      </html>
    `)
    printWindow.document.close()
    printWindow.print()
  }
}

// Add CSS for animations
const style = document.createElement("style")
style.textContent = `
    @keyframes slideOutUp {
        from {
            transform: translateY(0);
            opacity: 1;
        }
        to {
            transform: translateY(-100%);
            opacity: 0;
        }
    }
    
    @keyframes slideOutDown {
        from {
            transform: translateY(0);
            opacity: 1;
        }
        to {
            transform: translateY(100%);
            opacity: 0;
        }
    }
    
    @keyframes slideInDown {
        from {
            transform: translateY(-100%);
            opacity: 0;
        }
        to {
            transform: translateY(0);
            opacity: 1;
        }
    }
    
    .ripple {
        position: absolute;
        border-radius: 50%;
        background: rgba(255, 255, 255, 0.6);
        transform: scale(0);
        animation: ripple-animation 0.6s linear;
        pointer-events: none;
    }
    
    @keyframes ripple-animation {
        to {
            transform: scale(4);
            opacity: 0;
        }
    }
    
    .navbar.scrolled {
        background-color: rgba(255, 255, 255, 0.98) !important;
        box-shadow: 0 2px 20px rgba(0, 0, 0, 0.15);
    }
    
    .loading::after {
        content: '';
        position: absolute;
        right: 10px;
        top: 50%;
        transform: translateY(-50%);
        width: 16px;
        height: 16px;
        border: 2px solid #f3f3f3;
        border-top: 2px solid #007bff;
        border-radius: 50%;
        animation: spin 1s linear infinite;
    }
    
    .lazy {
        opacity: 0;
        transition: opacity 0.3s;
    }
    
    .lazy.loaded {
        opacity: 1;
    }
`
document.head.appendChild(style)

// Logout confirmation function
window.confirmLogout = function() {
    if (confirm('Are you sure you want to logout?')) {
        window.location.href = window.SITE_URL + '/auth/logout.php';
    }
}
