<?php
require_once __DIR__ . '/../config/config.php';

// Check if user is logged in and is business
if (!isLoggedIn() || getUserType() !== 'business') {
    redirect('/auth/login.php');
}

try {
    $database = new Database();
    $db = $database->getConnection();
    
    $user_id = $_SESSION['user_id'];
    
    // Handle job post creation
    if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['action']) && $_POST['action'] === 'create_job') {
        try {
            $title = sanitize($_POST['title']);
            $description = sanitize($_POST['description']);
            $requirements = sanitize($_POST['requirements']);
            $location = sanitize($_POST['location']);
            $job_type = sanitize($_POST['job_type']);
            $salary_min = !empty($_POST['salary_min']) ? (int)$_POST['salary_min'] : null;
            $salary_max = !empty($_POST['salary_max']) ? (int)$_POST['salary_max'] : null;
            $required_skills = sanitize($_POST['required_skills']);
            $slots_available = !empty($_POST['slots_available']) ? (int)$_POST['slots_available'] : 1;
            
            // Validate required fields
            if (empty($title) || empty($description) || empty($requirements) || empty($location) || empty($job_type)) {
                throw new Exception('Please fill in all required fields.');
            }
            
            // Validate slots
            if ($slots_available < 1) {
                throw new Exception('Number of slots must be at least 1.');
            }
            
            // Handle image upload
            $job_image = null;
            if (isset($_FILES['job_image']) && $_FILES['job_image']['error'] === UPLOAD_ERR_OK) {
                $upload_dir = UPLOAD_PATH . 'job_images/';
                
                // Create directory if it doesn't exist
                if (!is_dir($upload_dir)) {
                    mkdir($upload_dir, 0755, true);
                }
                
                $file_extension = strtolower(pathinfo($_FILES['job_image']['name'], PATHINFO_EXTENSION));
                $allowed_extensions = ['jpg', 'jpeg', 'png', 'gif'];
                
                if (in_array($file_extension, $allowed_extensions)) {
                    $job_image = uniqid() . '.' . $file_extension;
                    $upload_path = $upload_dir . $job_image;
                    
                    if (!move_uploaded_file($_FILES['job_image']['tmp_name'], $upload_path)) {
                        error_log("Failed to upload job image: " . $_FILES['job_image']['name']);
                        $job_image = null;
                    }
                } else {
                    error_log("Invalid file extension for job image: " . $file_extension);
                }
            }
            
            $query = "INSERT INTO job_posts (business_id, title, description, requirements, location, job_type, salary_min, salary_max, skills_required, job_image, slots_available, status)
                      VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, 'pending')";
            $stmt = $db->prepare($query);
            $stmt->execute([$user_id, $title, $description, $requirements, $location, $job_type, $salary_min, $salary_max, $required_skills, $job_image, $slots_available]);
            
            $_SESSION['success'] = 'Job post created successfully! It is pending admin approval.';
            redirect('/business/dashboard.php');
            
        } catch (Exception $e) {
            error_log("Job creation error: " . $e->getMessage());
            $_SESSION['error'] = 'Failed to create job post: ' . $e->getMessage();
            redirect('/business/dashboard.php');
        }
    }
    
    // Initialize variables
    $profile = null;
    $job_posts = [];

    // Get business profile
    try {
        $query = "SELECT bp.*, u.first_name, u.last_name, u.email, u.phone
                  FROM business_profiles bp
                  JOIN users u ON bp.user_id = u.id
                  WHERE bp.user_id = ?";
        $stmt = $db->prepare($query);
        $stmt->execute([$user_id]);
        $profile = $stmt->fetch(PDO::FETCH_ASSOC);
    } catch (Exception $e) {
        error_log("Profile query error: " . $e->getMessage());
        $profile = ['company_name' => 'Unknown Company'];
    }

    // Get job posts
    try {
        $query = "SELECT * FROM job_posts WHERE business_id = ? ORDER BY created_at DESC";
        $stmt = $db->prepare($query);
        $stmt->execute([$user_id]);
        $job_posts = $stmt->fetchAll(PDO::FETCH_ASSOC);
    } catch (Exception $e) {
        error_log("Job posts query error: " . $e->getMessage());
        $job_posts = [];
    }
    
    // Initialize applications array
    $applications = [];

    // Get applications for business jobs
    try {
        $query = "SELECT ja.*, jp.title as job_title, u.first_name, u.last_name, u.email, u.phone,
                         jpr.education_level, jpr.degree_course, jpr.skills, jpr.resume_file
                  FROM job_applications ja
                  JOIN job_posts jp ON ja.job_id = jp.id
                  JOIN users u ON ja.user_id = u.id
                  LEFT JOIN jobseeker_profiles jpr ON ja.user_id = jpr.user_id
                  WHERE jp.business_id = ?
                  ORDER BY ja.created_at DESC";
        $stmt = $db->prepare($query);
        $stmt->execute([$user_id]);
        $applications = $stmt->fetchAll(PDO::FETCH_ASSOC);
    } catch (Exception $e) {
        error_log("Applications query error: " . $e->getMessage());
        $applications = [];
    }
    
    // Initialize statistics variables
    $total_jobs = 0;
    $total_applications = 0;
    $approved_jobs = 0;
    $pending_jobs = 0;

    // Get statistics
    try {
        $query = "SELECT COUNT(*) as total FROM job_posts WHERE business_id = ?";
        $stmt = $db->prepare($query);
        $stmt->execute([$user_id]);
        $result = $stmt->fetch(PDO::FETCH_ASSOC);
        $total_jobs = $result ? (int)$result['total'] : 0;

        $query = "SELECT COUNT(*) as total FROM job_applications ja JOIN job_posts jp ON ja.job_id = jp.id WHERE jp.business_id = ?";
        $stmt = $db->prepare($query);
        $stmt->execute([$user_id]);
        $result = $stmt->fetch(PDO::FETCH_ASSOC);
        $total_applications = $result ? (int)$result['total'] : 0;

        $query = "SELECT COUNT(*) as total FROM job_posts WHERE business_id = ? AND status = 'approved'";
        $stmt = $db->prepare($query);
        $stmt->execute([$user_id]);
        $result = $stmt->fetch(PDO::FETCH_ASSOC);
        $approved_jobs = $result ? (int)$result['total'] : 0;

        $query = "SELECT COUNT(*) as total FROM job_posts WHERE business_id = ? AND status = 'pending'";
        $stmt = $db->prepare($query);
        $stmt->execute([$user_id]);
        $result = $stmt->fetch(PDO::FETCH_ASSOC);
        $pending_jobs = $result ? (int)$result['total'] : 0;
    } catch (Exception $e) {
        error_log("Statistics query error: " . $e->getMessage());
        // Variables already initialized to 0
    }
    
} catch (Exception $e) {
    error_log("Business dashboard error: " . $e->getMessage());
    $error = "Database error occurred.";
}

$page_title = "Business Dashboard";
include __DIR__ . '/../includes/header.php';
?>

<main class="py-4">
    <div class="container-fluid">
        <!-- Success Message -->
        <?php if (isset($_SESSION['success'])): ?>
        <div class="alert alert-success alert-dismissible fade show" role="alert">
            <i class="fas fa-check-circle me-2"></i>
            <?php echo htmlspecialchars($_SESSION['success']); unset($_SESSION['success']); ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
        <?php endif; ?>

        <!-- Error Message -->
        <?php if (isset($_SESSION['error'])): ?>
        <div class="alert alert-danger alert-dismissible fade show" role="alert">
            <i class="fas fa-exclamation-circle me-2"></i>
            <?php echo htmlspecialchars($_SESSION['error']); unset($_SESSION['error']); ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
        <?php endif; ?>

        <!-- Dashboard Header -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h1 class="h3 mb-0">Business Dashboard</h1>
                        <p class="text-muted">Welcome back, <?php echo htmlspecialchars($profile ? $profile['company_name'] : 'Business User'); ?>!</p>
                    </div>
                    <div>
                        <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#createJobModal">
                            <i class="fas fa-plus me-1"></i>Post New Job
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <!-- Statistics Cards -->
        <div class="row mb-4">
            <div class="col-lg-3 col-md-6 mb-3">
                <a href="#job-posts-section" class="text-decoration-none">
                    <div class="card bg-friendly text-white h-100 shadow-sm">
                        <div class="card-body">
                            <div class="d-flex justify-content-between">
                                <div>
                                    <h4 class="mb-0"><?php echo number_format($total_jobs); ?></h4>
                                    <p class="mb-0">Total Job Posts</p>
                                </div>
                                <div class="align-self-center">
                                    <i class="fas fa-briefcase fa-2x opacity-75"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </a>
            </div>
            <div class="col-lg-3 col-md-6 mb-3">
                <a href="#job-posts-section" class="text-decoration-none">
                    <div class="card bg-friendly text-white h-100 shadow-sm">
                        <div class="card-body">
                            <div class="d-flex justify-content-between">
                                <div>
                                    <h4 class="mb-0"><?php echo number_format($approved_jobs); ?></h4>
                                    <p class="mb-0">Approved Jobs</p>
                                </div>
                                <div class="align-self-center">
                                    <i class="fas fa-check-circle fa-2x opacity-75"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </a>
            </div>
            <div class="col-lg-3 col-md-6 mb-3">
                <a href="#job-posts-section" class="text-decoration-none">
                    <div class="card bg-friendly text-white h-100 shadow-sm">
                        <div class="card-body">
                            <div class="d-flex justify-content-between">
                                <div>
                                    <h4 class="mb-0"><?php echo number_format($pending_jobs); ?></h4>
                                    <p class="mb-0">Pending Approval</p>
                                </div>
                                <div class="align-self-center">
                                    <i class="fas fa-clock fa-2x opacity-75"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </a>
            </div>
            <div class="col-lg-3 col-md-6 mb-3">
                <a href="#applications-section" class="text-decoration-none">
                    <div class="card bg-friendly text-white h-100 shadow-sm">
                        <div class="card-body">
                            <div class="d-flex justify-content-between">
                                <div>
                                    <h4 class="mb-0"><?php echo number_format($total_applications); ?></h4>
                                    <p class="mb-0">Total Applications</p>
                                </div>
                                <div class="align-self-center">
                                    <i class="fas fa-file-alt fa-2x opacity-75"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </a>
            </div>
        </div>

        <div class="row">
            <!-- Job Posts -->
            <div class="col-lg-8 mb-4" id="job-posts-section">
                <div class="card h-100">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <h5 class="mb-0">
                            <i class="fas fa-briefcase me-2"></i>Your Job Posts
                        </h5>
                        <button type="button" class="btn btn-sm btn-primary" data-bs-toggle="modal" data-bs-target="#createJobModal">
                            <i class="fas fa-plus me-1"></i>New Job
                        </button>
                    </div>
                    <div class="card-body">
                        <?php if (empty($job_posts)): ?>
                        <div class="text-center py-4">
                            <i class="fas fa-briefcase fa-3x text-muted mb-3"></i>
                            <p class="text-muted">No job posts yet</p>
                            <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#createJobModal">
                                <i class="fas fa-plus me-1"></i>Create Your First Job Post
                            </button>
                        </div>
                        <?php else: ?>
                        <div class="table-responsive">
                            <table class="table">
                                <thead>
                                    <tr>
                                        <th>Job Title</th>
                                        <th>Location</th>
                                        <th>Slots</th>
                                        <th>Status</th>
                                        <th>Applications</th>
                                        <th>Posted Date</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($job_posts as $job): ?>
                                    <tr>
                                        <td>
                                            <strong><?php echo htmlspecialchars($job['title']); ?></strong><br>
                                            <small class="text-muted"><?php echo ucfirst($job['job_type']); ?></small>
                                        </td>
                                        <td><?php echo htmlspecialchars($job['location']); ?></td>
                                        <td>
                                            <span class="badge bg-<?php echo $job['slots_available'] > 0 ? 'success' : 'danger'; ?>">
                                                <?php echo $job['slots_available']; ?> slot(s)
                                            </span>
                                        </td>
                                        <td>
                                            <?php
                                            $status = isset($job['status']) ? $job['status'] : 'pending';
                                            $status_class = '';
                                            switch ($status) {
                                                case 'pending':
                                                    $status_class = 'bg-warning';
                                                    break;
                                                case 'approved':
                                                    $status_class = 'bg-success';
                                                    break;
                                                case 'rejected':
                                                    $status_class = 'bg-danger';
                                                    break;
                                                default:
                                                    $status_class = 'bg-secondary';
                                                    break;
                                            }
                                            ?>
                                            <span class="badge <?php echo $status_class; ?>">
                                                <?php echo ucfirst($status); ?>
                                            </span>
                                        </td>
                                        <td>
                                            <?php
                                            $app_count = 0;
                                            foreach ($applications as $app) {
                                                if ($app['job_id'] == $job['id']) $app_count++;
                                            }
                                            echo $app_count;
                                            ?>
                                        </td>
                                        <td>
                                            <small><?php echo date('M j, Y', strtotime($job['created_at'])); ?></small>
                                        </td>
                                        <td>
                                            <div class="btn-group btn-group-sm">
                                                <a href="../job-details.php?id=<?php echo $job['id']; ?>" class="btn btn-outline-primary">
                                                    <i class="fas fa-eye"></i>
                                                </a>
                                                <button type="button" class="btn btn-outline-info" onclick="viewApplications(<?php echo $job['id']; ?>)">
                                                    <i class="fas fa-users"></i>
                                                </button>
                                            </div>
                                        </td>
                                    </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>

            <!-- Recent Applications -->
            <div class="col-lg-4 mb-4">
                <div class="card h-100">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="fas fa-file-alt me-2"></i>Recent Applications
                        </h5>
                    </div>
                    <div class="card-body">
                        <?php if (empty($applications)): ?>
                        <div class="text-center py-4">
                            <i class="fas fa-file-alt fa-3x text-muted mb-3"></i>
                            <p class="text-muted">No applications yet</p>
                        </div>
                        <?php else: ?>
                        <div class="list-group list-group-flush">
                            <?php foreach (array_slice($applications, 0, 5) as $app): ?>
                            <div class="list-group-item px-0">
                                <div class="d-flex justify-content-between align-items-start">
                                    <div class="flex-grow-1">
                                        <h6 class="mb-1"><?php echo htmlspecialchars($app['first_name'] . ' ' . $app['last_name']); ?></h6>
                                        <p class="mb-1 small text-muted"><?php echo htmlspecialchars($app['job_title']); ?></p>
                                        <small class="text-muted"><?php echo date('M j, Y', strtotime($app['created_at'])); ?></small>
                                    </div>
                                    <span class="badge bg-<?php echo $app['status'] === 'pending' ? 'warning' : ($app['status'] === 'accepted' ? 'success' : 'danger'); ?>">
                                        <?php echo ucfirst($app['status']); ?>
                                    </span>
                                </div>
                            </div>
                            <?php endforeach; ?>
                        </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>

        <!-- All Applications -->
        <?php if (!empty($applications)): ?>
        <div class="row" id="applications-section">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="fas fa-users me-2"></i>All Applications
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table">
                                <thead>
                                    <tr>
                                        <th>Applicant</th>
                                        <th>Job Title</th>
                                        <th>Education</th>
                                        <th>Skills</th>
                                        <th>Status</th>
                                        <th>Applied Date</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($applications as $app): ?>
                                    <tr>
                                        <td>
                                            <strong><?php echo htmlspecialchars($app['first_name'] . ' ' . $app['last_name']); ?></strong><br>
                                            <small class="text-muted"><?php echo htmlspecialchars($app['email']); ?></small><br>
                                            <small class="text-muted"><?php echo htmlspecialchars($app['phone']); ?></small>
                                        </td>
                                        <td><?php echo htmlspecialchars($app['job_title']); ?></td>
                                        <td>
                                            <small>
                                                <?php echo htmlspecialchars($app['education_level'] ?? 'Not specified'); ?><br>
                                                <?php echo htmlspecialchars($app['degree_course'] ?? ''); ?>
                                            </small>
                                        </td>
                                        <td>
                                            <?php if (!empty($app['skills'])): ?>
                                            <?php
                                            $skills = array_slice(explode(',', $app['skills']), 0, 3);
                                            foreach ($skills as $skill):
                                            ?>
                                            <span class="badge bg-secondary me-1"><?php echo htmlspecialchars(trim($skill)); ?></span>
                                            <?php endforeach; ?>
                                            <?php if (count(explode(',', $app['skills'])) > 3): ?>
                                            <small class="text-muted">+<?php echo count(explode(',', $app['skills'])) - 3; ?> more</small>
                                            <?php endif; ?>
                                            <?php else: ?>
                                            <small class="text-muted">No skills listed</small>
                                            <?php endif; ?>
                                        </td>
                                        <td>
                                            <span class="badge bg-<?php echo $app['status'] === 'pending' ? 'warning' : ($app['status'] === 'accepted' ? 'success' : 'danger'); ?>">
                                                <?php echo ucfirst($app['status']); ?>
                                            </span>
                                        </td>
                                        <td>
                                            <small><?php echo date('M j, Y g:i A', strtotime($app['created_at'])); ?></small>
                                        </td>
                                        <td>
                                            <div class="btn-group btn-group-sm">
                                                <?php if (!empty($app['resume_file'])): ?>
                                                <a href="../uploads/resumes/<?php echo htmlspecialchars($app['resume_file']); ?>" 
                                                   class="btn btn-outline-info" target="_blank" title="View Resume">
                                                    <i class="fas fa-file-pdf"></i>
                                                </a>
                                                <?php endif; ?>
                                                <?php if ($app['status'] === 'pending'): ?>
                                                <button type="button" class="btn btn-outline-success" 
                                                        onclick="updateApplicationStatus(<?php echo $app['id']; ?>, 'accepted')">
                                                    <i class="fas fa-check"></i>
                                                </button>
                                                <button type="button" class="btn btn-outline-danger" 
                                                        onclick="updateApplicationStatus(<?php echo $app['id']; ?>, 'rejected')">
                                                    <i class="fas fa-times"></i>
                                                </button>
                                                <?php endif; ?>
                                            </div>
                                        </td>
                                    </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <?php endif; ?>
    </div>
</main>

<!-- Create Job Modal -->
<div class="modal fade" id="createJobModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="fas fa-plus me-2"></i>Create New Job Post
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form method="POST" enctype="multipart/form-data">
                <div class="modal-body">
                    <input type="hidden" name="action" value="create_job">
                    
                    <div class="row">
                        <div class="col-md-8 mb-3">
                            <label for="title" class="form-label">Job Title <span class="text-danger">*</span></label>
                            <input type="text" class="form-control" id="title" name="title" required>
                        </div>
                        <div class="col-md-4 mb-3">
                            <label for="job_type" class="form-label">Job Type <span class="text-danger">*</span></label>
                            <select class="form-select" id="job_type" name="job_type" required>
                                <option value="">Select type</option>
                                <option value="full-time">Full Time</option>
                                <option value="part-time">Part Time</option>
                                <option value="contract">Contract</option>
                                <option value="internship">Internship</option>
                            </select>
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <label for="location" class="form-label">Location <span class="text-danger">*</span></label>
                        <input type="text" class="form-control" id="location" name="location" required>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="salary_min" class="form-label">Minimum Salary (₱)</label>
                            <input type="number" class="form-control" id="salary_min" name="salary_min" min="0">
                        </div>
                        <div class="col-md-6 mb-3">
                            <label for="salary_max" class="form-label">Maximum Salary (₱)</label>
                            <input type="number" class="form-control" id="salary_max" name="salary_max" min="0">
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <label for="description" class="form-label">Job Description <span class="text-danger">*</span></label>
                        <textarea class="form-control" id="description" name="description" rows="4" required></textarea>
                    </div>
                    
                    <div class="mb-3">
                        <label for="requirements" class="form-label">Requirements <span class="text-danger">*</span></label>
                        <textarea class="form-control" id="requirements" name="requirements" rows="3" required></textarea>
                    </div>
                    
                    <div class="mb-3">
                        <label for="required_skills" class="form-label">Required Skills</label>
                        <input type="text" class="form-control" id="required_skills" name="required_skills" 
                               placeholder="e.g., PHP, JavaScript, MySQL (comma separated)">
                        <small class="form-text text-muted">Separate skills with commas</small>
                    </div>

                    <div class="mb-3">
                        <label for="slots_available" class="form-label">Slots Available <span class="text-danger">*</span></label>
                        <input type="number" class="form-control" id="slots_available" name="slots_available" value="1" min="1" required>
                        <small class="form-text text-muted">Number of positions available for this job.</small>
                    </div>
                    
                    <div class="mb-3">
                        <label for="job_image" class="form-label">Job Image (Optional)</label>
                        <input type="file" class="form-control" id="job_image" name="job_image" accept="image/*">
                        <small class="form-text text-muted">Upload an image to make your job post more attractive</small>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-plus me-1"></i>Create Job Post
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
function viewApplications(jobId) {
    // You can implement a modal or redirect to a detailed view
    alert('View applications for job ID: ' + jobId + ' - to be implemented');
}

function updateApplicationStatus(applicationId, status) {
    if (confirm(`Are you sure you want to ${status} this application?`)) {
        // Create a form and submit it
        const form = document.createElement('form');
        form.method = 'POST';
        form.action = 'update-application.php';
        
        const appIdInput = document.createElement('input');
        appIdInput.type = 'hidden';
        appIdInput.name = 'application_id';
        appIdInput.value = applicationId;
        
        const statusInput = document.createElement('input');
        statusInput.type = 'hidden';
        statusInput.name = 'status';
        statusInput.value = status;
        
        form.appendChild(appIdInput);
        form.appendChild(statusInput);
        document.body.appendChild(form);
        form.submit();
    }
}

// Salary validation
document.getElementById('salary_min').addEventListener('input', function() {
    const maxSalary = document.getElementById('salary_max');
    if (this.value && maxSalary.value && parseInt(this.value) > parseInt(maxSalary.value)) {
        maxSalary.value = this.value;
    }
});

document.getElementById('salary_max').addEventListener('input', function() {
    const minSalary = document.getElementById('salary_min');
    if (this.value && minSalary.value && parseInt(this.value) < parseInt(minSalary.value)) {
        minSalary.value = this.value;
    }
});
</script>

<style>
/* Black and friendly color for all dashboard cards */
.bg-friendly {
    background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%) !important;
}

/* Add subtle hover effects */
.card {
    transition: transform 0.2s ease-in-out, box-shadow 0.2s ease-in-out;
}

.card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(0,0,0,0.1) !important;
}

/* Make icons more prominent */
.card .fa-2x {
    filter: drop-shadow(0 2px 4px rgba(0,0,0,0.1));
}

/* Smooth scrolling for anchor links */
html {
    scroll-behavior: smooth;
}

/* Hover effect for clickable cards */
a .card:hover {
    transform: translateY(-3px);
    box-shadow: 0 6px 20px rgba(0,0,0,0.15) !important;
}
</style>

<?php include __DIR__ . '/../includes/footer.php'; ?>
